// 🔥 اختبار بسيط للنظام v4.0 الشامل التفصيلي
console.log('🔥 اختبار بسيط للنظام v4.0 الشامل التفصيلي');
console.log('==========================================');

const fs = require('fs');

try {
    // قراءة الملفات
    const bugBountyFile = './assets/modules/bugbounty/BugBountyCore.js';
    const templateFile = './assets/modules/bugbounty/report_template.html';
    
    if (!fs.existsSync(bugBountyFile)) {
        console.log('❌ ملف BugBountyCore.js غير موجود');
        process.exit(1);
    }
    
    if (!fs.existsSync(templateFile)) {
        console.log('❌ ملف report_template.html غير موجود');
        process.exit(1);
    }
    
    console.log('✅ جميع الملفات المطلوبة موجودة');
    
    // قراءة المحتوى
    const bugBountyContent = fs.readFileSync(bugBountyFile, 'utf8');
    const templateContent = fs.readFileSync(templateFile, 'utf8');
    
    console.log('✅ تم قراءة الملفات بنجاح');
    
    // اختبار 1: فحص تحسينات استخراج البيانات الحقيقية
    console.log('🔍 اختبار 1: فحص تحسينات استخراج البيانات الحقيقية');
    
    const hasRealDataExtraction = bugBountyContent.includes('discoveredVuln.parameter') &&
                                 bugBountyContent.includes('discoveredVuln.payload') &&
                                 bugBountyContent.includes('discoveredVuln.url') &&
                                 bugBountyContent.includes('discoveredVuln.response') &&
                                 bugBountyContent.includes('discoveredVuln.evidence');
    
    if (hasRealDataExtraction) {
        console.log('   ✅ دالة استخراج البيانات الحقيقية محسنة');
    } else {
        console.log('   ❌ دالة استخراج البيانات الحقيقية تحتاج تحسين');
    }
    
    // اختبار 2: فحص تحسينات التأثير الديناميكي
    console.log('🔍 اختبار 2: فحص تحسينات التأثير الديناميكي');
    
    const usesExtractedData = bugBountyContent.includes('extractRealDataFromDiscoveredVulnerability') &&
                             bugBountyContent.includes('extractedRealData');
    
    const hasImprovedDefaults = bugBountyContent.includes('payload حقيقي للثغرة') ||
                               bugBountyContent.includes('الموقع المستهدف للثغرة');
    
    if (usesExtractedData && hasImprovedDefaults) {
        console.log('   ✅ دالة التأثير الديناميكي محسنة');
    } else {
        console.log('   ❌ دالة التأثير الديناميكي تحتاج تحسين');
    }
    
    // اختبار 3: فحص دالة استخراج المعامل الجديدة
    console.log('🔍 اختبار 3: فحص دالة استخراج المعامل الجديدة');
    
    const hasNewFunction = bugBountyContent.includes('extractParameterFromDiscoveredVulnerability(discoveredVuln)');
    const hasParameterSearch = bugBountyContent.includes('discoveredVuln.parameter') &&
                              bugBountyContent.includes('discoveredVuln.affected_parameter') &&
                              bugBountyContent.includes('discoveredVuln.vulnerable_param');
    
    const hasUrlExtraction = bugBountyContent.includes('extractParametersFromUrl') &&
                            bugBountyContent.includes('extractParameterFromPayload');
    
    if (hasNewFunction && hasParameterSearch && hasUrlExtraction) {
        console.log('   ✅ دالة استخراج المعامل الجديدة موجودة ومكتملة');
    } else {
        console.log('   ❌ دالة استخراج المعامل الجديدة مفقودة أو غير مكتملة');
    }
    
    // اختبار 4: فحص استخدام القالب الشامل
    console.log('🔍 اختبار 4: فحص استخدام القالب الشامل');
    
    const templateVariables = [
        'VULNERABILITIES_CONTENT',
        'TESTING_DETAILS', 
        'INTERACTIVE_DIALOGUES',
        'VISUAL_CHANGES',
        'PERSISTENT_RESULTS',
        'IMPACT_VISUALIZATIONS'
    ];
    
    let foundVariables = 0;
    templateVariables.forEach(variable => {
        if (templateContent.includes(variable)) {
            foundVariables++;
        }
    });
    
    const usesTemplate = bugBountyContent.includes('report_template.html') &&
                        bugBountyContent.includes('VULNERABILITIES_CONTENT');
    
    if (foundVariables === templateVariables.length && usesTemplate) {
        console.log(`   ✅ القالب الشامل يُستخدم بشكل صحيح (${foundVariables}/${templateVariables.length} متغير)`);
    } else {
        console.log(`   ❌ استخدام القالب غير مكتمل (${foundVariables}/${templateVariables.length} متغير)`);
    }
    
    // اختبار 5: فحص الدوال الشاملة التفصيلية
    console.log('🔍 اختبار 5: فحص الدوال الشاملة التفصيلية');
    
    const comprehensiveFunctions = [
        'generateComprehensiveDetailsFromRealData',
        'generateDynamicImpactForAnyVulnerability',
        'generateRealExploitationStepsForVulnerabilityComprehensive',
        'generateDynamicRecommendationsForVulnerability',
        'generateRealDetailedDialogueFromDiscoveredVulnerability'
    ];
    
    let foundFunctions = 0;
    comprehensiveFunctions.forEach(func => {
        if (bugBountyContent.includes(func)) {
            foundFunctions++;
        }
    });
    
    if (foundFunctions === comprehensiveFunctions.length) {
        console.log(`   ✅ جميع الدوال الشاملة موجودة (${foundFunctions}/${comprehensiveFunctions.length})`);
    } else {
        console.log(`   ❌ بعض الدوال الشاملة مفقودة (${foundFunctions}/${comprehensiveFunctions.length})`);
    }
    
    // اختبار 6: فحص إزالة المحتوى العام
    console.log('🔍 اختبار 6: فحص إزالة المحتوى العام');
    
    const genericTexts = [
        'معامل مكتشف',
        'payload_discovered', 
        'target_discovered',
        'موقع مستهدف'
    ];
    
    let genericCount = 0;
    genericTexts.forEach(text => {
        if (bugBountyContent.includes(text)) {
            genericCount++;
            console.log(`   ⚠️ تم العثور على نص عام: ${text}`);
        }
    });
    
    if (genericCount === 0) {
        console.log('   ✅ لا يوجد محتوى عام');
    } else {
        console.log(`   ⚠️ تم العثور على ${genericCount} نص عام`);
    }
    
    // اختبار 7: إنشاء ثغرة عينة للاختبار
    console.log('🔍 اختبار 7: إنشاء ثغرة عينة للاختبار');
    
    const sampleVuln = {
        name: 'SQL Injection Test',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'http://testphp.vulnweb.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        tested_payload: "admin' OR '1'='1' --",
        response: 'Login successful - Authentication bypassed',
        evidence: 'Successfully logged in without valid credentials',
        exploitation_response: 'Database access gained',
        exploitation_evidence: 'User data extracted'
    };
    
    console.log('   ✅ ثغرة عينة تم إنشاؤها:');
    console.log(`      الاسم: ${sampleVuln.name}`);
    console.log(`      المعامل: ${sampleVuln.parameter}`);
    console.log(`      Payload: ${sampleVuln.payload}`);
    console.log(`      الموقع: ${sampleVuln.url}`);
    
    // النتائج النهائية
    console.log('');
    console.log('🏁 النتائج النهائية للاختبار:');
    console.log('============================');
    
    const tests = [
        hasRealDataExtraction,
        usesExtractedData && hasImprovedDefaults,
        hasNewFunction && hasParameterSearch && hasUrlExtraction,
        foundVariables === templateVariables.length && usesTemplate,
        foundFunctions === comprehensiveFunctions.length,
        genericCount === 0
    ];
    
    const passedTests = tests.filter(test => test).length;
    const totalTests = tests.length;
    
    console.log(`📊 الاختبارات المجتازة: ${passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
        console.log('🎉🎉🎉 نجحت جميع التحسينات!');
        console.log('✅ النظام v4.0 الشامل التفصيلي يعمل بالبيانات الحقيقية');
        console.log('✅ التقارير ستحتوي على التفاصيل الحقيقية للثغرات المكتشفة والمختبرة');
        console.log('✅ لا يوجد محتوى عام أو افتراضي');
        console.log('🔥 التعديلات تعمل بشكل مثالي!');
    } else if (passedTests >= totalTests * 0.8) {
        console.log('🎉 معظم التحسينات نجحت!');
        console.log('✅ النظام محسن بشكل كبير');
        console.log('⚠️ قد تحتاج بعض التحسينات الإضافية');
    } else {
        console.log('⚠️ التحسينات تحتاج المزيد من العمل');
        console.log('❌ النظام قد لا يعمل بالبيانات الحقيقية بشكل كامل');
    }
    
    console.log('');
    console.log('🔥 اختبار النظام v4.0 الشامل التفصيلي مكتمل!');
    
    // إنشاء تقرير اختبار بسيط
    const testReport = `
# تقرير اختبار النظام v4.0 الشامل التفصيلي

## النتائج:
- الاختبارات المجتازة: ${passedTests}/${totalTests}
- استخراج البيانات الحقيقية: ${hasRealDataExtraction ? '✅' : '❌'}
- التأثير الديناميكي: ${usesExtractedData && hasImprovedDefaults ? '✅' : '❌'}
- دالة استخراج المعامل: ${hasNewFunction && hasParameterSearch && hasUrlExtraction ? '✅' : '❌'}
- استخدام القالب الشامل: ${foundVariables === templateVariables.length && usesTemplate ? '✅' : '❌'}
- الدوال الشاملة: ${foundFunctions === comprehensiveFunctions.length ? '✅' : '❌'}
- إزالة المحتوى العام: ${genericCount === 0 ? '✅' : '❌'}

## الخلاصة:
${passedTests === totalTests ? 'النظام v4.0 جاهز ويعمل بالبيانات الحقيقية!' : 'النظام يحتاج المزيد من التحسينات'}

## ثغرة الاختبار:
- الاسم: ${sampleVuln.name}
- المعامل: ${sampleVuln.parameter}
- Payload: ${sampleVuln.payload}
- الموقع: ${sampleVuln.url}

تاريخ الاختبار: ${new Date().toLocaleString('ar')}
    `;
    
    fs.writeFileSync('test_report_v4.md', testReport, 'utf8');
    console.log('📄 تم حفظ تقرير الاختبار: test_report_v4.md');
    
} catch (error) {
    console.log('❌ خطأ في الاختبار:', error.message);
    process.exit(1);
}
