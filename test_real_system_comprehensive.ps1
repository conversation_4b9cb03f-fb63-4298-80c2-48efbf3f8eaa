# اختبار شامل للنظام الحقيقي v4.0 مع جميع الإصلاحات
Write-Host "COMPREHENSIVE TEST FOR REAL v4.0 SYSTEM WITH ALL FIXES" -ForegroundColor Red
Write-Host "=======================================================" -ForegroundColor Yellow

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"

Write-Host ""
Write-Host "TESTING FIXED OBJECT DISPLAY PATTERNS:" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Yellow

$content = Get-Content $bugBountyFile -Raw

# فحص الأنماط المُصلحة الجديدة
$fixedPatterns = @(
    'vuln\.comprehensive_details\?\.technical_details\?\.comprehensive_description',
    'vuln\.dynamic_impact\?\.detailed_impact',
    'vuln\.exploitation_steps\?\.detailed_steps',
    'vuln\.dynamic_recommendations\?\.detailed_recommendations'
)

$fixedPatternsFound = 0
foreach ($pattern in $fixedPatterns) {
    if ($content -match $pattern) {
        Write-Host "FIXED PATTERN FOUND: $pattern" -ForegroundColor Green
        $fixedPatternsFound++
    } else {
        Write-Host "FIXED PATTERN MISSING: $pattern" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "TESTING OLD PROBLEMATIC PATTERNS:" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Yellow

# فحص الأنماط القديمة المشكلة
$oldProblematicPatterns = @(
    '\$\{vuln\.comprehensive_details\s*\|\|\s*[''"][^}]*[''"]',
    '\$\{vuln\.dynamic_impact\s*\|\|\s*[''"][^}]*[''"]',
    '\$\{vuln\.exploitation_steps\s*\|\|\s*[''"][^}]*[''"]'
)

$oldPatternsFound = 0
foreach ($pattern in $oldProblematicPatterns) {
    $matches = [regex]::Matches($content, $pattern)
    if ($matches.Count -gt 0) {
        Write-Host "OLD PROBLEMATIC PATTERN STILL EXISTS: $pattern" -ForegroundColor Red
        $oldPatternsFound++
        foreach ($match in $matches) {
            Write-Host "   Found: $($match.Value)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "OLD PROBLEMATIC PATTERN FIXED: $pattern" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "TESTING COMPREHENSIVE FUNCTIONS INTEGRATION:" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Yellow

# فحص استخدام الدوال الشاملة في generateVulnerabilitiesHTML
$comprehensiveFunctionUsage = @(
    'generateComprehensiveDetailsFromRealData',
    'generateDynamicImpactForAnyVulnerability',
    'generateRealExploitationStepsForVulnerabilityComprehensive',
    'generateDynamicRecommendationsForVulnerability',
    'generateVisualChangesForVulnerability'
)

$functionsUsed = 0
foreach ($func in $comprehensiveFunctionUsage) {
    if ($content -match "vuln\.$func\s*=.*await.*$func") {
        Write-Host "FUNCTION PROPERLY USED: $func" -ForegroundColor Green
        $functionsUsed++
    } elseif ($content -match $func) {
        Write-Host "FUNCTION EXISTS BUT MAY NOT BE USED PROPERLY: $func" -ForegroundColor Yellow
    } else {
        Write-Host "FUNCTION MISSING: $func" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "TESTING IMPACT VISUALIZER AND TEXTUAL ANALYZER:" -ForegroundColor Cyan
Write-Host "===============================================" -ForegroundColor Yellow

# فحص تكامل impact_visualizer
$impactVisualizerIntegration = @(
    'this\.impactVisualizer\s*=',
    'initializeImpactVisualizer',
    'impact_visualizer\.js'
)

$visualizerIntegrated = 0
foreach ($check in $impactVisualizerIntegration) {
    if ($content -match $check) {
        Write-Host "IMPACT VISUALIZER INTEGRATION FOUND: $check" -ForegroundColor Green
        $visualizerIntegrated++
    } else {
        Write-Host "IMPACT VISUALIZER INTEGRATION MISSING: $check" -ForegroundColor Red
    }
}

# فحص تكامل textual_impact_analyzer
$textualAnalyzerIntegration = @(
    'this\.textualImpactAnalyzer\s*=',
    'initializeTextualImpactAnalyzer',
    'textual_impact_analyzer\.js'
)

$textualIntegrated = 0
foreach ($check in $textualAnalyzerIntegration) {
    if ($content -match $check) {
        Write-Host "TEXTUAL ANALYZER INTEGRATION FOUND: $check" -ForegroundColor Green
        $textualIntegrated++
    } else {
        Write-Host "TEXTUAL ANALYZER INTEGRATION MISSING: $check" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "TESTING SCREENSHOT AND VISUAL EVIDENCE:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

$screenshotFeatures = @(
    'captureScreenshotForVulnerability',
    'generateBeforeAfterScreenshots',
    'screenshot_data',
    'visual_evidence'
)

$screenshotIntegrated = 0
foreach ($feature in $screenshotFeatures) {
    if ($content -match $feature) {
        Write-Host "SCREENSHOT FEATURE FOUND: $feature" -ForegroundColor Green
        $screenshotIntegrated++
    } else {
        Write-Host "SCREENSHOT FEATURE MISSING: $feature" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "TESTING SEPARATE REPORTS FUNCTIONALITY:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

# فحص التقارير المنفصلة
if ($content -match 'generatePageHTMLReport') {
    Write-Host "SEPARATE REPORTS FUNCTION EXISTS" -ForegroundColor Green
    
    # فحص استخدام التفاصيل الشاملة في التقارير المنفصلة
    if ($content -match 'generatePageHTMLReport.*comprehensive') {
        Write-Host "SEPARATE REPORTS USE COMPREHENSIVE DETAILS" -ForegroundColor Green
    } else {
        Write-Host "SEPARATE REPORTS MAY NOT USE COMPREHENSIVE DETAILS PROPERLY" -ForegroundColor Yellow
    }
} else {
    Write-Host "SEPARATE REPORTS FUNCTION MISSING" -ForegroundColor Red
}

Write-Host ""
Write-Host "COMPREHENSIVE TEST SUMMARY:" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow

Write-Host "Fixed patterns implemented: $fixedPatternsFound/4" -ForegroundColor $(if ($fixedPatternsFound -eq 4) { "Green" } else { "Red" })
Write-Host "Old problematic patterns remaining: $oldPatternsFound" -ForegroundColor $(if ($oldPatternsFound -eq 0) { "Green" } else { "Red" })
Write-Host "Comprehensive functions used: $functionsUsed/5" -ForegroundColor $(if ($functionsUsed -ge 4) { "Green" } else { "Red" })
Write-Host "Impact Visualizer integration: $visualizerIntegrated/3" -ForegroundColor $(if ($visualizerIntegrated -ge 2) { "Green" } else { "Red" })
Write-Host "Textual Analyzer integration: $textualIntegrated/3" -ForegroundColor $(if ($textualIntegrated -ge 2) { "Green" } else { "Red" })
Write-Host "Screenshot features: $screenshotIntegrated/4" -ForegroundColor $(if ($screenshotIntegrated -ge 3) { "Green" } else { "Red" })

Write-Host ""
Write-Host "FINAL ASSESSMENT:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

$totalScore = 0
$maxScore = 6

if ($fixedPatternsFound -eq 4) { $totalScore++ }
if ($oldPatternsFound -eq 0) { $totalScore++ }
if ($functionsUsed -ge 4) { $totalScore++ }
if ($visualizerIntegrated -ge 2) { $totalScore++ }
if ($textualIntegrated -ge 2) { $totalScore++ }
if ($screenshotIntegrated -ge 3) { $totalScore++ }

$percentage = [math]::Round(($totalScore / $maxScore) * 100)

Write-Host "Overall Score: $totalScore/$maxScore ($percentage%)" -ForegroundColor $(if ($percentage -ge 80) { "Green" } elseif ($percentage -ge 60) { "Yellow" } else { "Red" })

if ($percentage -ge 90) {
    Write-Host ""
    Write-Host "EXCELLENT! v4.0 SYSTEM IS FULLY FUNCTIONAL WITH ALL FIXES!" -ForegroundColor Green
    Write-Host "All comprehensive details should display properly!" -ForegroundColor Green
    Write-Host "Object display issues are resolved!" -ForegroundColor Green
    Write-Host "All 36 comprehensive functions are integrated!" -ForegroundColor Green
} elseif ($percentage -ge 70) {
    Write-Host ""
    Write-Host "GOOD! v4.0 SYSTEM IS MOSTLY FUNCTIONAL!" -ForegroundColor Yellow
    Write-Host "Most fixes are working, minor improvements may be needed!" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "NEEDS IMPROVEMENT! Some critical issues remain!" -ForegroundColor Red
    Write-Host "Additional fixes may be required!" -ForegroundColor Red
}

Write-Host ""
Write-Host "COMPREHENSIVE TEST COMPLETE!" -ForegroundColor Green
