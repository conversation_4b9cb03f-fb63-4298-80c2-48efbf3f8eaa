<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 التحقق المباشر من النظام الشامل التفصيلي v4.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 500px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .stat {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .verification-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 التحقق المباشر من النظام الشامل التفصيلي v4.0</h1>
        <p>فحص مباشر للكود للتأكد من أن جميع التقارير تستخدم الدوال الشاملة التفصيلية</p>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="totalChecks">0</div>
                <div>إجمالي الفحوصات</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="passedChecks">0</div>
                <div>الفحوصات الناجحة</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="successRate">0%</div>
                <div>معدل النجاح</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="functionsVerified">0</div>
                <div>الدوال المتحققة</div>
            </div>
        </div>
        
        <button class="btn" onclick="runDirectVerification()">🚀 تشغيل التحقق المباشر</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div class="verification-section">
            <h3>📊 نتائج التحقق</h3>
            <div id="results" class="result"></div>
        </div>
    </div>

    <script>
        let totalChecks = 0;
        let passedChecks = 0;
        let functionsVerified = 0;
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStats() {
            document.getElementById('totalChecks').textContent = totalChecks;
            document.getElementById('passedChecks').textContent = passedChecks;
            document.getElementById('successRate').textContent = totalChecks > 0 ? Math.round(passedChecks / totalChecks * 100) + '%' : '0%';
            document.getElementById('functionsVerified').textContent = functionsVerified;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            totalChecks = 0;
            passedChecks = 0;
            functionsVerified = 0;
            updateStats();
        }
        
        async function fetchFileContent(filePath) {
            try {
                const response = await fetch(filePath);
                if (response.ok) {
                    return await response.text();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                throw new Error(`فشل في تحميل الملف: ${error.message}`);
            }
        }
        
        function checkFunctionExists(content, functionName) {
            totalChecks++;
            const patterns = [
                new RegExp(`async\\s+${functionName}\\s*\\(`, 'g'),
                new RegExp(`${functionName}\\s*\\(`, 'g'),
                new RegExp(`${functionName}\\s*:`, 'g')
            ];
            
            for (const pattern of patterns) {
                if (pattern.test(content)) {
                    log(`✅ الدالة ${functionName} موجودة في الكود`, 'success');
                    passedChecks++;
                    functionsVerified++;
                    return true;
                }
            }
            
            log(`❌ الدالة ${functionName} غير موجودة في الكود`, 'error');
            return false;
        }
        
        function checkFunctionUsage(content, functionName, context) {
            totalChecks++;
            const usagePattern = new RegExp(`${functionName}\\s*\\(`, 'g');
            const matches = content.match(usagePattern);
            
            if (matches && matches.length > 0) {
                log(`✅ الدالة ${functionName} مستخدمة ${matches.length} مرة في ${context}`, 'success');
                passedChecks++;
                return true;
            } else {
                log(`❌ الدالة ${functionName} غير مستخدمة في ${context}`, 'error');
                return false;
            }
        }
        
        function checkReportFlow(content) {
            log('🔍 فحص تدفق التقارير...', 'info');
            
            // فحص التقرير الرئيسي
            totalChecks++;
            if (content.includes('generateFinalComprehensiveReport') && 
                content.includes('generateComprehensiveVulnerabilitiesContentUsingExistingFunctions') &&
                content.includes('generateVulnerabilitiesHTML') &&
                content.includes('formatComprehensiveVulnerabilitySection')) {
                log('✅ تدفق التقرير الرئيسي صحيح ومتكامل', 'success');
                passedChecks++;
            } else {
                log('❌ تدفق التقرير الرئيسي غير مكتمل', 'error');
            }
            
            // فحص التقارير المنفصلة
            totalChecks++;
            if (content.includes('formatSinglePageReport') && 
                content.includes('formatComprehensiveVulnerabilitySection')) {
                log('✅ تدفق التقارير المنفصلة صحيح ومتكامل', 'success');
                passedChecks++;
            } else {
                log('❌ تدفق التقارير المنفصلة غير مكتمل', 'error');
            }
        }
        
        function checkComprehensiveDetailsUsage(content) {
            log('🔥 فحص استخدام الدالة الشاملة التفصيلية...', 'info');
            
            const comprehensiveDetailsPattern = /generateComprehensiveDetailsFromRealData\s*\(/g;
            const matches = content.match(comprehensiveDetailsPattern);
            
            totalChecks++;
            if (matches && matches.length >= 3) {
                log(`✅ الدالة generateComprehensiveDetailsFromRealData مستخدمة ${matches.length} مرة`, 'success');
                passedChecks++;
                functionsVerified++;
            } else {
                log(`❌ الدالة generateComprehensiveDetailsFromRealData مستخدمة ${matches ? matches.length : 0} مرة فقط`, 'error');
            }
        }
        
        function checkNoGenericContent(content) {
            log('🚫 فحص عدم وجود محتوى عام أو افتراضي...', 'info');
            
            const genericPatterns = [
                /placeholder.*content/gi,
                /default.*template/gi,
                /generic.*text/gi,
                /\[object Object\]/gi,
                /payload_example/gi
            ];
            
            let foundGeneric = false;
            for (const pattern of genericPatterns) {
                const matches = content.match(pattern);
                if (matches && matches.length > 5) { // السماح ببعض الاستخدامات المحدودة
                    log(`⚠️ تم العثور على محتوى عام محتمل: ${pattern.source} (${matches.length} مرة)`, 'warning');
                    foundGeneric = true;
                }
            }
            
            totalChecks++;
            if (!foundGeneric) {
                log('✅ لا يوجد محتوى عام أو افتراضي مفرط', 'success');
                passedChecks++;
            } else {
                log('⚠️ قد يوجد بعض المحتوى العام', 'warning');
            }
        }
        
        async function runDirectVerification() {
            clearResults();
            log('🚀 بدء التحقق المباشر من النظام v4.0', 'info');
            
            try {
                // تحميل ملف BugBountyCore.js
                log('📂 تحميل ملف BugBountyCore.js...', 'info');
                const content = await fetchFileContent('./assets/modules/bugbounty/BugBountyCore.js');
                log(`✅ تم تحميل الملف بنجاح (${(content.length / 1024).toFixed(1)} KB)`, 'success');
                
                // فحص الدوال الشاملة التفصيلية
                log('🔧 فحص الدوال الشاملة التفصيلية...', 'info');
                const comprehensiveFunctions = [
                    'generateComprehensiveDetailsFromRealData',
                    'extractRealDataFromDiscoveredVulnerability',
                    'formatComprehensiveVulnerabilitySection',
                    'generateDynamicImpactForAnyVulnerability',
                    'generateRealExploitationStepsForVulnerabilityComprehensive',
                    'generateDynamicRecommendationsForVulnerability',
                    'generateRealVisualChangesForVulnerability',
                    'generateRealPersistentResultsForVulnerability',
                    'generateFinalComprehensiveReport',
                    'formatSinglePageReport'
                ];
                
                for (const funcName of comprehensiveFunctions) {
                    checkFunctionExists(content, funcName);
                }
                
                // فحص استخدام الدوال في التقارير
                log('📊 فحص استخدام الدوال في التقارير...', 'info');
                checkFunctionUsage(content, 'generateComprehensiveDetailsFromRealData', 'التقارير');
                checkFunctionUsage(content, 'formatComprehensiveVulnerabilitySection', 'التقارير');
                checkFunctionUsage(content, 'extractRealDataFromDiscoveredVulnerability', 'استخراج البيانات');
                
                // فحص تدفق التقارير
                checkReportFlow(content);
                
                // فحص استخدام الدالة الشاملة التفصيلية
                checkComprehensiveDetailsUsage(content);
                
                // فحص عدم وجود محتوى عام
                checkNoGenericContent(content);
                
                // النتائج النهائية
                const successRate = totalChecks > 0 ? (passedChecks / totalChecks * 100).toFixed(1) : 0;
                
                log('🏁 انتهى التحقق المباشر من النظام', 'info');
                log(`📊 النتائج النهائية: ${passedChecks}/${totalChecks} (${successRate}%)`, 'info');
                
                if (successRate >= 90) {
                    log('🎉 النظام الشامل التفصيلي v4.0 يعمل بكفاءة ممتازة!', 'success');
                    log('✅ جميع التقارير تستخدم الدوال الشاملة التفصيلية تلقائياً وديناميكياً', 'success');
                    log('🔥 لا يوجد محتوى عام أو افتراضي أو يدوي مفرط', 'success');
                } else if (successRate >= 75) {
                    log('✅ النظام الشامل التفصيلي v4.0 يعمل بكفاءة عالية', 'success');
                } else {
                    log('⚠️ النظام الشامل التفصيلي v4.0 يحتاج مراجعة', 'warning');
                }
                
                updateStats();
                
            } catch (error) {
                log(`❌ فشل في التحقق من النظام: ${error.message}`, 'error');
                updateStats();
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runDirectVerification, 1000);
        });
    </script>
</body>
</html>
