// 🔥 اختبار إنشاء تقارير فعلية للنظام الشامل التفصيلي v4.0

console.log('🔥 اختبار إنشاء تقارير فعلية');
console.log('============================');

// تحميل النظام
const fs = require('fs');

try {
    // قراءة ملف BugBountyCore
    const filePath = './assets/modules/bugbounty/BugBountyCore.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ ملف BugBountyCore.js غير موجود');
        process.exit(1);
    }
    
    console.log('✅ ملف BugBountyCore.js موجود');
    
    // قراءة المحتوى
    const code = fs.readFileSync(filePath, 'utf8');
    console.log('✅ تم قراءة الملف بنجاح');
    
    // تنفيذ الكود
    eval(code);
    console.log('✅ تم تحميل الكود بنجاح');
    
    // إنشاء النظام
    const bugBountyCore = new BugBountyCore();
    console.log('✅ تم إنشاء النظام بنجاح');
    
    // بيانات ثغرة اختبار
    const testVuln = {
        name: 'SQL Injection Test',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'http://example.com/login.php',
        payload: "admin' OR '1'='1' --",
        evidence: 'Database error revealed',
        description: 'Critical SQL injection vulnerability'
    };
    
    console.log('📊 بيانات الثغرة:');
    console.log('   الاسم:', testVuln.name);
    console.log('   النوع:', testVuln.type);
    console.log('   الخطورة:', testVuln.severity);
    
    // اختبار الدالة الشاملة التفصيلية
    console.log('🔧 اختبار الدالة الشاملة التفصيلية...');
    
    bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, {})
        .then(result => {
            if (result && typeof result === 'object') {
                console.log('✅ الدالة الشاملة التفصيلية تعمل بنجاح');
                console.log('📊 عدد الأقسام المُنشأة:', Object.keys(result).length);
                console.log('🔑 أسماء الأقسام:', Object.keys(result).join(', '));
                
                // اختبار التقرير المنفصل
                console.log('📋 اختبار التقرير المنفصل...');
                
                const pageData = {
                    page_name: 'Test Login Page',
                    page_url: 'http://example.com/login.php',
                    vulnerabilities: [testVuln]
                };
                
                return bugBountyCore.generatePageHTMLReport(pageData, 'http://example.com/login.php', 1);
            } else {
                throw new Error('الدالة الشاملة لا تعمل بشكل صحيح');
            }
        })
        .then(separateReport => {
            if (separateReport && separateReport.length > 500) {
                console.log('✅ التقرير المنفصل تم إنشاؤه بنجاح');
                console.log('📊 حجم التقرير:', Math.round(separateReport.length / 1024), 'KB');
                
                // فحص المحتوى الشامل التفصيلي
                const hasComprehensiveContent = 
                    separateReport.includes('comprehensive-vulnerability') ||
                    separateReport.includes('الوصف الشامل التفصيلي') ||
                    separateReport.includes('خطوات الاستغلال الشاملة') ||
                    separateReport.includes('النظام الشامل التفصيلي v4.0');
                
                const hasGenericContent = 
                    separateReport.includes('تم اكتشاف ثغرة أمنية') ||
                    separateReport.includes('حدث خطأ في تحميل التفاصيل الشاملة');
                
                if (hasComprehensiveContent) {
                    console.log('🎉 التقرير المنفصل يحتوي على المحتوى الشامل التفصيلي!');
                } else {
                    console.log('❌ التقرير المنفصل لا يحتوي على المحتوى الشامل التفصيلي');
                }
                
                if (!hasGenericContent) {
                    console.log('✅ التقرير المنفصل لا يحتوي على محتوى عام');
                } else {
                    console.log('❌ التقرير المنفصل يحتوي على محتوى عام');
                }
                
                // اختبار التقرير الرئيسي
                console.log('📊 اختبار التقرير الرئيسي...');
                
                const analysis = {
                    vulnerabilities: [testVuln],
                    summary: {
                        total_vulnerabilities: 1,
                        critical_severity: 1
                    }
                };
                
                return bugBountyCore.generateFinalComprehensiveReport(analysis, [], 'http://example.com');
            } else {
                throw new Error('فشل في إنشاء التقرير المنفصل');
            }
        })
        .then(mainReport => {
            if (mainReport && mainReport.length > 500) {
                console.log('✅ التقرير الرئيسي تم إنشاؤه بنجاح');
                console.log('📊 حجم التقرير:', Math.round(mainReport.length / 1024), 'KB');
                
                // فحص المحتوى الشامل التفصيلي
                const hasComprehensiveContent = 
                    mainReport.includes('vulnerability-comprehensive-v4') ||
                    mainReport.includes('الوصف الشامل التفصيلي') ||
                    mainReport.includes('خطوات الاستغلال الشاملة') ||
                    mainReport.includes('النظام الشامل التفصيلي v4.0');
                
                const hasGenericContent = 
                    mainReport.includes('تم اكتشاف ثغرة أمنية') ||
                    mainReport.includes('ثغرة مكتشفة تحتاج مراجعة');
                
                if (hasComprehensiveContent) {
                    console.log('🎉 التقرير الرئيسي يحتوي على المحتوى الشامل التفصيلي!');
                } else {
                    console.log('❌ التقرير الرئيسي لا يحتوي على المحتوى الشامل التفصيلي');
                }
                
                if (!hasGenericContent) {
                    console.log('✅ التقرير الرئيسي لا يحتوي على محتوى عام');
                } else {
                    console.log('❌ التقرير الرئيسي يحتوي على محتوى عام');
                }
                
                // النتائج النهائية
                console.log('');
                console.log('🏁 انتهى الاختبار الفعلي للتقارير');
                console.log('🎉 النظام الشامل التفصيلي v4.0 تم اختباره بنجاح!');
                console.log('✅ التقارير (الرئيسي والمنفصلة) تم إنشاؤها فعلياً');
                console.log('🔥 التعديلات تعمل بشكل صحيح');
                
            } else {
                throw new Error('فشل في إنشاء التقرير الرئيسي');
            }
        })
        .catch(error => {
            console.log('❌ خطأ في الاختبار:', error.message);
            console.log('❌ التعديلات قد تحتاج مراجعة');
        });
        
} catch (error) {
    console.log('❌ خطأ في تحميل النظام:', error.message);
    process.exit(1);
}
