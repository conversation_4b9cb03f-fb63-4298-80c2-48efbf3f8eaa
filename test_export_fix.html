<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار إصلاح مشكلة التصدير</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 اختبار إصلاح مشكلة التصدير - Maximum call stack size exceeded</h1>
    
    <button onclick="testExportProcess()">اختبار عملية التصدير الكاملة</button>
    <button onclick="testGeneratePageHTMLReportOnly()">اختبار generatePageHTMLReport فقط</button>
    <button onclick="clearLog()">مسح السجل</button>

    <div id="results"></div>
    <div id="log" class="log"></div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let logDiv = document.getElementById('log');
        let resultsDiv = document.getElementById('results');
        
        // تسجيل جميع الأخطاء
        window.addEventListener('error', function(e) {
            addLog(`❌ خطأ JavaScript: ${e.message}`);
            if (e.message.includes('Maximum call stack size exceeded')) {
                addResult('🚨 تم اكتشاف Maximum call stack size exceeded!', 'error');
            }
        });

        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function addLog(message) {
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            logDiv.innerHTML = '';
            resultsDiv.innerHTML = '';
        }

        async function testGeneratePageHTMLReportOnly() {
            addResult('🔄 بدء اختبار generatePageHTMLReport فقط...', 'info');
            addLog('بدء اختبار generatePageHTMLReport...');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    addLog('تم إنشاء مثيل BugBountyCore');
                }
                
                // إنشاء بيانات اختبار بسيطة
                const testPageResult = {
                    vulnerabilities: [{
                        name: 'Test XSS Vulnerability',
                        type: 'XSS',
                        severity: 'Medium',
                        description: 'Test vulnerability for export',
                        payload: '<script>alert("test")</script>',
                        url: 'https://example.com/test',
                        comprehensive_details: 'تفاصيل شاملة للاختبار',
                        dynamic_impact: 'تأثير ديناميكي للاختبار',
                        exploitation_steps: 'خطوات الاستغلال للاختبار',
                        dynamic_recommendations: 'توصيات ديناميكية للاختبار'
                    }]
                };
                
                addLog('تم إنشاء بيانات الاختبار');
                
                // محاولة استدعاء الدالة مع timeout قصير
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout after 15 seconds')), 15000);
                });
                
                addLog('بدء استدعاء generatePageHTMLReport...');
                const startTime = Date.now();
                
                const testPromise = bugBountyCore.generatePageHTMLReport(testPageResult, 'https://example.com', 1);
                
                const result = await Promise.race([testPromise, timeoutPromise]);
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                if (result && result.length > 100) {
                    addResult(`✅ نجح الاختبار! تم إنشاء HTML بحجم ${result.length} حرف في ${duration}ms`, 'success');
                    addLog(`نجح الاختبار - HTML بحجم ${result.length} حرف في ${duration}ms`);
                    addResult('🎉 تم حل مشكلة Maximum call stack size exceeded!', 'success');
                } else {
                    addResult('⚠️ الاختبار أنتج نتيجة فارغة أو قصيرة', 'error');
                    addLog('الاختبار أنتج نتيجة فارغة أو قصيرة');
                }
                
            } catch (error) {
                addResult(`❌ فشل الاختبار: ${error.message}`, 'error');
                addLog(`فشل الاختبار: ${error.message}`);
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 المشكلة لا تزال موجودة!', 'error');
                } else if (error.message.includes('Timeout')) {
                    addResult('⏰ انتهت مهلة الاختبار - قد تكون هناك حلقة لا نهائية', 'error');
                }
            }
        }

        async function testExportProcess() {
            addResult('🔄 بدء اختبار عملية التصدير الكاملة...', 'info');
            addLog('بدء اختبار عملية التصدير الكاملة...');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    addLog('تم إنشاء مثيل BugBountyCore');
                }
                
                // محاكاة عملية التصدير
                addLog('💾 المرحلة 1/4: تحضير البيانات...');
                
                const testPageResult = {
                    vulnerabilities: [{
                        name: 'Test SQL Injection',
                        type: 'SQL Injection',
                        severity: 'High',
                        description: 'Test SQL injection vulnerability',
                        payload: "' OR 1=1 --",
                        url: 'https://example.com/login',
                        comprehensive_details: 'تفاصيل شاملة لثغرة SQL',
                        dynamic_impact: 'تأثير ديناميكي خطير',
                        exploitation_steps: 'خطوات الاستغلال المفصلة',
                        dynamic_recommendations: 'توصيات الإصلاح العاجل'
                    }]
                };
                
                addLog('📄 المرحلة 2/4: إنشاء تقرير HTML...');
                const startTime = Date.now();
                
                const pageReport = await bugBountyCore.generatePageHTMLReport(testPageResult, 'https://example.com', 1);
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                addLog('💾 المرحلة 3/4: إنشاء ملف التحميل...');
                
                if (pageReport && pageReport.length > 100) {
                    const blob = new Blob([pageReport], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    
                    addLog('⬇️ المرحلة 4/4: تحميل التقرير...');
                    
                    // محاكاة التحميل
                    setTimeout(() => URL.revokeObjectURL(url), 1000);
                    
                    addResult(`✅ نجحت عملية التصدير الكاملة! HTML بحجم ${pageReport.length} حرف في ${duration}ms`, 'success');
                    addResult('🎉 تم حل مشكلة التصدير بالكامل!', 'success');
                } else {
                    addResult('❌ فشلت عملية إنشاء HTML', 'error');
                }
                
            } catch (error) {
                addResult(`❌ فشلت عملية التصدير: ${error.message}`, 'error');
                addLog(`فشلت عملية التصدير: ${error.message}`);
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 المشكلة لا تزال موجودة في عملية التصدير!', 'error');
                }
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة اختبار التصدير بنجاح', 'success');
            addLog('تم تحميل صفحة اختبار التصدير');
        };
    </script>
</body>
</html>
