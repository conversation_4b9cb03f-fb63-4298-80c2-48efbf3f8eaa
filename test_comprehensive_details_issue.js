// 🔥 اختبار شامل لفحص مشكلة عدم ظهور التفاصيل الشاملة التفصيلية
console.log('🔥 اختبار شامل لفحص مشكلة التفاصيل الشاملة التفصيلية');
console.log('=======================================================');

const fs = require('fs');

async function testComprehensiveDetailsIssue() {
    try {
        console.log('📖 تحميل النظام v4.0...');
        
        // قراءة ملف BugBountyCore
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        const code = fs.readFileSync(filePath, 'utf8');
        
        // تنفيذ الكود
        const modifiedCode = code + `
        if (typeof module !== 'undefined' && module.exports) {
            module.exports = { BugBountyCore };
        } else if (typeof global !== 'undefined') {
            global.BugBountyCore = BugBountyCore;
        }
        `;
        
        eval(modifiedCode);
        console.log('✅ تم تحميل النظام بنجاح');
        
        // إنشاء مثيل من النظام
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل النظام');
        
        // ثغرة تجريبية للاختبار
        const testVulnerability = {
            name: 'SQL Injection في نظام إدارة المستخدمين',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'http://testphp.vulnweb.com/admin/users.php',
            parameter: 'user_id',
            payload: "1' UNION SELECT user(),database(),version() --",
            response: 'MySQL error: Database version disclosed',
            evidence: 'تم كشف إصدار قاعدة البيانات',
            location: 'http://testphp.vulnweb.com/admin/users.php',
            business_impact: 'عالي - إمكانية الوصول لجميع بيانات النظام',
            affected_components: ['قاعدة البيانات', 'نظام المصادقة']
        };
        
        console.log('🔥 اختبار الدوال الشاملة التفصيلية...');
        
        // اختبار 1: generateComprehensiveDetailsFromRealData
        console.log('\n📋 اختبار 1: generateComprehensiveDetailsFromRealData');
        console.log('================================================');
        
        const realData = {
            url: testVulnerability.url,
            parameter: testVulnerability.parameter,
            payload: testVulnerability.payload,
            response: testVulnerability.response,
            evidence: testVulnerability.evidence
        };
        
        const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVulnerability, realData);
        
        console.log('🔍 نوع النتيجة:', typeof comprehensiveDetails);
        console.log('🔍 هل النتيجة null؟', comprehensiveDetails === null);
        
        if (comprehensiveDetails) {
            console.log('✅ تم إنتاج التفاصيل الشاملة');
            console.log('📊 أقسام التفاصيل:', Object.keys(comprehensiveDetails));
            
            // فحص المحتوى التفصيلي
            if (comprehensiveDetails.technical_details) {
                console.log('🔍 التفاصيل التقنية موجودة');
                console.log('📋 الوصف الشامل:', comprehensiveDetails.technical_details.comprehensive_description ? 'موجود' : 'مفقود');
                
                if (comprehensiveDetails.technical_details.comprehensive_description) {
                    console.log('📄 طول الوصف الشامل:', comprehensiveDetails.technical_details.comprehensive_description.length);
                    console.log('📄 أول 200 حرف من الوصف:');
                    console.log(comprehensiveDetails.technical_details.comprehensive_description.substring(0, 200));
                }
            }
            
            if (comprehensiveDetails.impact_analysis) {
                console.log('🔍 تحليل التأثير موجود');
                console.log('📋 التأثير المفصل:', comprehensiveDetails.impact_analysis.detailed_impact ? 'موجود' : 'مفقود');
            }
            
            if (comprehensiveDetails.exploitation_results) {
                console.log('🔍 نتائج الاستغلال موجودة');
                console.log('📋 الخطوات المفصلة:', comprehensiveDetails.exploitation_results.detailed_steps ? 'موجود' : 'مفقود');
            }
            
        } else {
            console.log('❌ فشل في إنتاج التفاصيل الشاملة');
        }
        
        // اختبار 2: generateDynamicImpactForAnyVulnerability
        console.log('\n📋 اختبار 2: generateDynamicImpactForAnyVulnerability');
        console.log('=================================================');
        
        const dynamicImpact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVulnerability, realData);
        console.log('🔍 نوع نتيجة التأثير الديناميكي:', typeof dynamicImpact);
        console.log('🔍 طول نتيجة التأثير:', dynamicImpact ? dynamicImpact.length : 0);
        
        if (dynamicImpact && dynamicImpact.length > 100) {
            console.log('✅ تم إنتاج التأثير الديناميكي');
            console.log('📄 أول 200 حرف من التأثير:');
            console.log(dynamicImpact.substring(0, 200));
        } else {
            console.log('❌ فشل في إنتاج التأثير الديناميكي أو النتيجة قصيرة');
        }
        
        // اختبار 3: generateRealExploitationStepsForVulnerabilityComprehensive
        console.log('\n📋 اختبار 3: generateRealExploitationStepsForVulnerabilityComprehensive');
        console.log('==============================================================');
        
        const exploitationSteps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVulnerability, realData);
        console.log('🔍 نوع نتيجة خطوات الاستغلال:', typeof exploitationSteps);
        console.log('🔍 طول نتيجة خطوات الاستغلال:', exploitationSteps ? exploitationSteps.length : 0);
        
        if (exploitationSteps && exploitationSteps.length > 100) {
            console.log('✅ تم إنتاج خطوات الاستغلال الشاملة');
            console.log('📄 أول 200 حرف من خطوات الاستغلال:');
            console.log(exploitationSteps.substring(0, 200));
        } else {
            console.log('❌ فشل في إنتاج خطوات الاستغلال أو النتيجة قصيرة');
        }
        
        // اختبار 4: generateDynamicRecommendationsForVulnerability
        console.log('\n📋 اختبار 4: generateDynamicRecommendationsForVulnerability');
        console.log('======================================================');
        
        const recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVulnerability);
        console.log('🔍 نوع نتيجة التوصيات:', typeof recommendations);
        console.log('🔍 طول نتيجة التوصيات:', recommendations ? recommendations.length : 0);
        
        if (recommendations && recommendations.length > 100) {
            console.log('✅ تم إنتاج التوصيات الديناميكية');
            console.log('📄 أول 200 حرف من التوصيات:');
            console.log(recommendations.substring(0, 200));
        } else {
            console.log('❌ فشل في إنتاج التوصيات أو النتيجة قصيرة');
        }
        
        // اختبار 5: محاكاة إنتاج HTML
        console.log('\n📋 اختبار 5: محاكاة إنتاج HTML');
        console.log('===============================');
        
        // محاكاة ما يحدث في generateVulnerabilitiesHTML
        const vuln = { ...testVulnerability };
        vuln.comprehensive_details = comprehensiveDetails;
        vuln.dynamic_impact = dynamicImpact;
        vuln.exploitation_steps = exploitationSteps;
        vuln.dynamic_recommendations = recommendations;
        
        console.log('🔍 فحص البيانات المُعدة للـ HTML:');
        console.log('  comprehensive_details:', typeof vuln.comprehensive_details);
        console.log('  dynamic_impact:', typeof vuln.dynamic_impact);
        console.log('  exploitation_steps:', typeof vuln.exploitation_steps);
        console.log('  dynamic_recommendations:', typeof vuln.dynamic_recommendations);
        
        // محاكاة عرض HTML
        console.log('\n📄 محاكاة عرض HTML:');
        console.log('====================');
        
        const htmlContent = `
        <div class="comprehensive-section">
            <h3>📋 الوصف الشامل التفصيلي:</h3>
            <div class="content">${vuln.comprehensive_details || 'تفاصيل شاملة تفصيلية'}</div>
        </div>
        `;
        
        console.log('📄 HTML المُنتج:');
        console.log(htmlContent.substring(0, 500));
        
        // فحص المشكلة
        console.log('\n🚨 تحليل المشكلة:');
        console.log('==================');
        
        if (typeof vuln.comprehensive_details === 'object' && vuln.comprehensive_details !== null) {
            console.log('❌ المشكلة: comprehensive_details هو كائن، لكن HTML يتوقع string');
            console.log('🔧 الحل: يجب تحويل الكائن إلى HTML أو استخراج النص المطلوب');
            
            // اقتراح الحل
            const fixedContent = vuln.comprehensive_details.technical_details?.comprehensive_description || 
                               JSON.stringify(vuln.comprehensive_details, null, 2);
            
            console.log('✅ الحل المقترح - استخراج النص:');
            console.log(fixedContent.substring(0, 300));
        }
        
        return {
            success: true,
            comprehensiveDetails: !!comprehensiveDetails,
            dynamicImpact: !!dynamicImpact,
            exploitationSteps: !!exploitationSteps,
            recommendations: !!recommendations,
            issue: typeof vuln.comprehensive_details === 'object' ? 'object_instead_of_string' : 'unknown'
        };
        
    } catch (error) {
        console.log('❌ خطأ في الاختبار:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testComprehensiveDetailsIssue().then(result => {
    console.log('\n🏁 نتائج الاختبار الشامل:');
    console.log('==========================');
    
    if (result.success) {
        console.log('🎉 الاختبار نجح!');
        console.log(`📊 التفاصيل الشاملة: ${result.comprehensiveDetails ? 'تعمل' : 'لا تعمل'}`);
        console.log(`📊 التأثير الديناميكي: ${result.dynamicImpact ? 'يعمل' : 'لا يعمل'}`);
        console.log(`📊 خطوات الاستغلال: ${result.exploitationSteps ? 'تعمل' : 'لا تعمل'}`);
        console.log(`📊 التوصيات: ${result.recommendations ? 'تعمل' : 'لا تعمل'}`);
        console.log(`🚨 المشكلة المكتشفة: ${result.issue}`);
        
        if (result.issue === 'object_instead_of_string') {
            console.log('\n🔧 الحل المطلوب:');
            console.log('================');
            console.log('1. تعديل generateVulnerabilitiesHTML لاستخراج النص من الكائن');
            console.log('2. أو تعديل الدوال لترجع نص HTML مباشرة');
            console.log('3. أو إنشاء دالة تحويل من كائن إلى HTML');
        }
        
    } else {
        console.log('❌ الاختبار فشل:', result.error);
    }
    
    console.log('\n🔥 اختبار فحص المشكلة مكتمل!');
});
