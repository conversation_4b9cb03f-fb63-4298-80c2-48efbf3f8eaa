# Real Report Generation Test - Testing ACTUAL usage in generated reports
# This test will generate actual reports and verify the content

Write-Host "=== REAL REPORT GENERATION TEST ===" -ForegroundColor Red
Write-Host "Testing ACTUAL usage of 36 functions and files in generated reports" -ForegroundColor Yellow

$TestResults = @{
    MainReportGenerated = $false
    SeparateReportGenerated = $false
    Functions36InMainReport = 0
    Functions36InSeparateReport = 0
    FilesUsedInMainReport = 0
    FilesUsedInSeparateReport = 0
    ComprehensiveContentInMain = 0
    ComprehensiveContentInSeparate = 0
    DynamicContentInMain = 0
    DynamicContentInSeparate = 0
}

function Test-ReportContent {
    param($ReportPath, $SearchTexts)
    if (Test-Path $ReportPath) {
        $content = Get-Content $ReportPath -Raw -Encoding UTF8
        $matches = 0
        foreach ($text in $SearchTexts) {
            if ($content -like "*$text*") { $matches++ }
        }
        return $matches
    }
    return 0
}

Write-Host "`n1. Starting Node.js server for testing..." -ForegroundColor Cyan

# Start Node.js server in background
$serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -WorkingDirectory "." -WindowStyle Hidden -PassThru
Start-Sleep -Seconds 3

Write-Host "  [OK] Server started (PID: $($serverProcess.Id))" -ForegroundColor Green

Write-Host "`n2. Generating ACTUAL Main Report..." -ForegroundColor Cyan

# Create test HTML file to generate main report
$testMainReportHTML = @"
<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Main Report Test</title></head>
<body>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
async function generateMainReport() {
    try {
        const bugBountyCore = new BugBountyCore();
        
        const testVuln = {
            name: 'SQL Injection Test Vulnerability',
            type: 'SQL Injection',
            severity: 'Critical',
            url: 'http://testphp.vulnweb.com/admin/users.php',
            parameter: 'user_id',
            payload: "1' UNION SELECT user(),database(),version() --",
            response: 'MySQL error: You have an error in your SQL syntax; Database version: MySQL 5.7.31',
            evidence: 'Database version disclosure detected',
            confidence_level: 95
        };
        
        console.log('Generating main report with all 36 functions...');
        const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
        
        // Save report to file
        const blob = new Blob([mainReport], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'test_main_report_actual.html';
        a.click();
        
        console.log('Main report generated and saved');
        document.body.innerHTML = '<h1>Main Report Generated Successfully</h1><p>Size: ' + mainReport.length + ' characters</p>';
        
    } catch (error) {
        console.error('Error generating main report:', error);
        document.body.innerHTML = '<h1>Error: ' + error.message + '</h1>';
    }
}
generateMainReport();
</script>
</body>
</html>
"@

$testMainReportHTML | Out-File -FilePath "test_main_report_generation.html" -Encoding UTF8

# Open browser to generate main report
Start-Process "http://localhost:3000/test_main_report_generation.html"
Start-Sleep -Seconds 8

# Check if main report was generated
$mainReportPath = Get-ChildItem -Path "." -Filter "test_main_report_actual*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($mainReportPath) {
    $TestResults.MainReportGenerated = $true
    Write-Host "  [OK] Main report generated: $($mainReportPath.Name)" -ForegroundColor Green
    
    # Test content of main report
    Write-Host "`n3. Testing Main Report Content..." -ForegroundColor Cyan
    
    # Test 36 functions usage in main report
    $functions36Indicators = @(
        "comprehensive_details",
        "dynamic_impact", 
        "exploitation_steps",
        "dynamic_recommendations",
        "visual_changes",
        "screenshot_data",
        "security_metrics",
        "remediation_plan",
        "comprehensive_documentation",
        "technical_report",
        "executive_summary",
        "compliance_report",
        "forensic_analysis"
    )
    
    $TestResults.Functions36InMainReport = Test-ReportContent $mainReportPath.FullName $functions36Indicators
    Write-Host "  Functions (36) in main report: $($TestResults.Functions36InMainReport)/13 indicators found" -ForegroundColor $(if ($TestResults.Functions36InMainReport -ge 10) { "Green" } else { "Yellow" })
    
    # Test files usage in main report
    $filesUsageIndicators = @(
        "impact_visualizer.js",
        "textual_impact_analyzer.js",
        "visual_impact_data",
        "textual_impact_analysis",
        "business_impact_analysis",
        "exploitation_screenshots"
    )
    
    $TestResults.FilesUsedInMainReport = Test-ReportContent $mainReportPath.FullName $filesUsageIndicators
    Write-Host "  Files usage in main report: $($TestResults.FilesUsedInMainReport)/6 indicators found" -ForegroundColor $(if ($TestResults.FilesUsedInMainReport -ge 4) { "Green" } else { "Yellow" })
    
    # Test comprehensive content in main report
    $comprehensiveContentIndicators = @(
        "comprehensive-section",
        "content-section",
        "vulnerability-header"
    )
    
    $TestResults.ComprehensiveContentInMain = Test-ReportContent $mainReportPath.FullName $comprehensiveContentIndicators
    Write-Host "  Comprehensive content in main report: $($TestResults.ComprehensiveContentInMain)/3 indicators found" -ForegroundColor $(if ($TestResults.ComprehensiveContentInMain -ge 2) { "Green" } else { "Yellow" })
    
    # Test dynamic content in main report
    $dynamicContentIndicators = @(
        "SQL Injection Test Vulnerability",
        "1' UNION SELECT user(),database(),version() --",
        "MySQL error: You have an error in your SQL syntax",
        "Database version disclosure detected"
    )
    
    $TestResults.DynamicContentInMain = Test-ReportContent $mainReportPath.FullName $dynamicContentIndicators
    Write-Host "  Dynamic content in main report: $($TestResults.DynamicContentInMain)/4 vulnerability-specific content found" -ForegroundColor $(if ($TestResults.DynamicContentInMain -ge 3) { "Green" } else { "Yellow" })
    
} else {
    Write-Host "  [ERROR] Main report was not generated" -ForegroundColor Red
}

Write-Host "`n4. Generating ACTUAL Separate Report..." -ForegroundColor Cyan

# Create test HTML file to generate separate report
$testSeparateReportHTML = @"
<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Separate Report Test</title></head>
<body>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
async function generateSeparateReport() {
    try {
        const bugBountyCore = new BugBountyCore();
        
        const pageData = {
            page_name: 'Test Security Page',
            page_url: 'http://testphp.vulnweb.com',
            vulnerabilities: [{
                name: 'XSS Test Vulnerability',
                type: 'XSS',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/search.php',
                parameter: 'query',
                payload: '<script>alert("XSS Test")</script>',
                response: 'Script executed in page',
                evidence: 'JavaScript code execution confirmed',
                confidence_level: 90
            }]
        };
        
        console.log('Generating separate report with all 36 functions and files...');
        const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, pageData.page_url, 1);
        
        // Save report to file
        const blob = new Blob([separateReport], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'test_separate_report_actual.html';
        a.click();
        
        console.log('Separate report generated and saved');
        document.body.innerHTML = '<h1>Separate Report Generated Successfully</h1><p>Size: ' + separateReport.length + ' characters</p>';
        
    } catch (error) {
        console.error('Error generating separate report:', error);
        document.body.innerHTML = '<h1>Error: ' + error.message + '</h1>';
    }
}
generateSeparateReport();
</script>
</body>
</html>
"@

$testSeparateReportHTML | Out-File -FilePath "test_separate_report_generation.html" -Encoding UTF8

# Open browser to generate separate report
Start-Process "http://localhost:3000/test_separate_report_generation.html"
Start-Sleep -Seconds 8

# Check if separate report was generated
$separateReportPath = Get-ChildItem -Path "." -Filter "test_separate_report_actual*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1

if ($separateReportPath) {
    $TestResults.SeparateReportGenerated = $true
    Write-Host "  [OK] Separate report generated: $($separateReportPath.Name)" -ForegroundColor Green
    
    # Test content of separate report
    Write-Host "`n5. Testing Separate Report Content..." -ForegroundColor Cyan
    
    # Test 36 functions usage in separate report
    $TestResults.Functions36InSeparateReport = Test-ReportContent $separateReportPath.FullName $functions36Indicators
    Write-Host "  Functions (36) in separate report: $($TestResults.Functions36InSeparateReport)/13 indicators found" -ForegroundColor $(if ($TestResults.Functions36InSeparateReport -ge 10) { "Green" } else { "Yellow" })
    
    # Test files usage in separate report
    $TestResults.FilesUsedInSeparateReport = Test-ReportContent $separateReportPath.FullName $filesUsageIndicators
    Write-Host "  Files usage in separate report: $($TestResults.FilesUsedInSeparateReport)/6 indicators found" -ForegroundColor $(if ($TestResults.FilesUsedInSeparateReport -ge 4) { "Green" } else { "Yellow" })
    
    # Test comprehensive content in separate report
    $TestResults.ComprehensiveContentInSeparate = Test-ReportContent $separateReportPath.FullName $comprehensiveContentIndicators
    Write-Host "  Comprehensive content in separate report: $($TestResults.ComprehensiveContentInSeparate)/3 indicators found" -ForegroundColor $(if ($TestResults.ComprehensiveContentInSeparate -ge 2) { "Green" } else { "Yellow" })
    
    # Test dynamic content in separate report
    $dynamicContentSeparateIndicators = @(
        "XSS Test Vulnerability",
        "<script>alert(\"XSS Test\")</script>",
        "Script executed in page",
        "JavaScript code execution confirmed"
    )
    
    $TestResults.DynamicContentInSeparate = Test-ReportContent $separateReportPath.FullName $dynamicContentSeparateIndicators
    Write-Host "  Dynamic content in separate report: $($TestResults.DynamicContentInSeparate)/4 vulnerability-specific content found" -ForegroundColor $(if ($TestResults.DynamicContentInSeparate -ge 3) { "Green" } else { "Yellow" })
    
} else {
    Write-Host "  [ERROR] Separate report was not generated" -ForegroundColor Red
}

Write-Host "`n6. Calculating ACTUAL Usage Score..." -ForegroundColor Cyan

# Calculate actual usage score
$totalScore = 0
$maxScore = 100

# Report generation (20 points)
if ($TestResults.MainReportGenerated) { $totalScore += 10 }
if ($TestResults.SeparateReportGenerated) { $totalScore += 10 }

# Functions usage in reports (30 points)
$totalScore += [math]::Min(($TestResults.Functions36InMainReport + $TestResults.Functions36InSeparateReport) * 1.15, 30)

# Files usage in reports (25 points)
$totalScore += [math]::Min(($TestResults.FilesUsedInMainReport + $TestResults.FilesUsedInSeparateReport) * 2.1, 25)

# Comprehensive content (15 points)
$totalScore += [math]::Min(($TestResults.ComprehensiveContentInMain + $TestResults.ComprehensiveContentInSeparate) * 2.5, 15)

# Dynamic content (10 points)
$totalScore += [math]::Min(($TestResults.DynamicContentInMain + $TestResults.DynamicContentInSeparate) * 1.25, 10)

$actualUsagePercentage = [math]::Round($totalScore)

Write-Host "`n=== ACTUAL USAGE VERIFICATION RESULTS ===" -ForegroundColor Yellow
Write-Host "Main Report Generated: $(if ($TestResults.MainReportGenerated) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($TestResults.MainReportGenerated) { "Green" } else { "Red" })
Write-Host "Separate Report Generated: $(if ($TestResults.SeparateReportGenerated) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($TestResults.SeparateReportGenerated) { "Green" } else { "Red" })
Write-Host "36 Functions Used in Main Report: $($TestResults.Functions36InMainReport)/13 indicators" -ForegroundColor White
Write-Host "36 Functions Used in Separate Report: $($TestResults.Functions36InSeparateReport)/13 indicators" -ForegroundColor White
Write-Host "Files Used in Main Report: $($TestResults.FilesUsedInMainReport)/6 indicators" -ForegroundColor White
Write-Host "Files Used in Separate Report: $($TestResults.FilesUsedInSeparateReport)/6 indicators" -ForegroundColor White
Write-Host "Dynamic Content in Main Report: $($TestResults.DynamicContentInMain)/4 vulnerability-specific" -ForegroundColor White
Write-Host "Dynamic Content in Separate Report: $($TestResults.DynamicContentInSeparate)/4 vulnerability-specific" -ForegroundColor White

Write-Host "`nACTUAL USAGE SCORE: $actualUsagePercentage/100" -ForegroundColor $(if ($actualUsagePercentage -ge 80) { "Green" } elseif ($actualUsagePercentage -ge 60) { "Yellow" } else { "Red" })

if ($actualUsagePercentage -ge 80) {
    Write-Host "`n[EXCELLENT] All 36 functions and files are ACTUALLY USED in report generation!" -ForegroundColor Green
    Write-Host "- Reports are generated with comprehensive content" -ForegroundColor Green
    Write-Host "- Dynamic content based on discovered vulnerabilities" -ForegroundColor Green
    Write-Host "- All system files are integrated and used" -ForegroundColor Green
} elseif ($actualUsagePercentage -ge 60) {
    Write-Host "`n[GOOD] Most functions and files are used in reports with some improvements needed" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] Functions and files are not being used effectively in reports" -ForegroundColor Red
}

# Cleanup
Write-Host "`n7. Cleaning up..." -ForegroundColor Cyan
Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
Remove-Item "test_main_report_generation.html" -ErrorAction SilentlyContinue
Remove-Item "test_separate_report_generation.html" -ErrorAction SilentlyContinue

Write-Host "`n=== ACTUAL USAGE VERIFICATION COMPLETE ===" -ForegroundColor Green
