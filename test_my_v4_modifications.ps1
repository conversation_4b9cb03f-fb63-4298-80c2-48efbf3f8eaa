# Test MY v4.0 System Modifications - Automatic Dynamic Content Generation
Write-Host "=== TESTING MY v4.0 SYSTEM MODIFICATIONS ===" -ForegroundColor Red
Write-Host "Testing that reports generate DYNAMIC content based on discovered vulnerabilities" -ForegroundColor Yellow
Write-Host "NOT manual, default, or generic content - FULLY AUTOMATIC" -ForegroundColor Yellow

# Start Node.js server
Write-Host "`n1. Starting Node.js server..." -ForegroundColor Cyan
$serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -WorkingDirectory "." -WindowStyle Hidden -PassThru
Start-Sleep -Seconds 3
Write-Host "  [OK] Server started (PID: $($serverProcess.Id))" -ForegroundColor Green

# Test 1: Generate Main Report with Real Vulnerabilities
Write-Host "`n2. Testing Main Report Generation with Real Vulnerabilities..." -ForegroundColor Cyan

$mainReportTest = @"
<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Test Main Report Dynamic</title></head>
<body>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
async function testMainReportDynamic() {
    try {
        const bugBountyCore = new BugBountyCore();
        
        // Load all system files
        await bugBountyCore.loadAndActivateAllSystemFiles();
        
        // Create different vulnerability types to test dynamic generation
        const realVulnerabilities = [
            {
                name: 'SQL Injection in Login Form',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --",
                response: 'Login successful without password',
                evidence: 'Authentication bypass confirmed',
                confidence_level: 95
            },
            {
                name: 'Cross-Site Scripting in Search',
                type: 'XSS',
                severity: 'High', 
                url: 'http://testphp.vulnweb.com/search.php',
                parameter: 'query',
                payload: '<script>alert("XSS_CONFIRMED_TEST")</script>',
                response: 'Script executed in browser',
                evidence: 'JavaScript execution confirmed',
                confidence_level: 90
            },
            {
                name: 'Local File Inclusion in View Page',
                type: 'LFI',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/view.php',
                parameter: 'file',
                payload: '../../../../etc/passwd',
                response: 'root:x:0:0:root:/root:/bin/bash',
                evidence: 'System file access confirmed',
                confidence_level: 85
            }
        ];
        
        console.log('Generating main report with 3 different vulnerability types...');
        const mainReport = await bugBountyCore.generateVulnerabilitiesHTML(realVulnerabilities);
        
        // Save main report
        const blob = new Blob([mainReport], { type: 'text/html' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'test_main_dynamic_' + Date.now() + '.html';
        a.click();
        
        console.log('Main report generated with dynamic content');
        document.body.innerHTML = '<h1>Main Report Generated</h1><p>Size: ' + mainReport.length + ' chars</p>';
        
    } catch (error) {
        console.error('Error:', error);
        document.body.innerHTML = '<h1>Error: ' + error.message + '</h1>';
    }
}
testMainReportDynamic();
</script>
</body>
</html>
"@

$mainReportTest | Out-File -FilePath "test_main_dynamic.html" -Encoding UTF8

# Open browser to generate main report
Start-Process "http://localhost:3000/test_main_dynamic.html"
Start-Sleep -Seconds 10

# Test 2: Generate Separate Reports for Each Vulnerability
Write-Host "`n3. Testing Separate Report Generation..." -ForegroundColor Cyan

$separateReportTest = @"
<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"><title>Test Separate Reports Dynamic</title></head>
<body>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
async function testSeparateReportsDynamic() {
    try {
        const bugBountyCore = new BugBountyCore();
        
        // Load all system files
        await bugBountyCore.loadAndActivateAllSystemFiles();
        
        // Test different pages with different vulnerabilities
        const pageTests = [
            {
                page_name: 'SQL Injection Test Page',
                page_url: 'http://testphp.vulnweb.com/admin',
                vulnerabilities: [{
                    name: 'Advanced SQL Injection',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://testphp.vulnweb.com/admin/users.php',
                    parameter: 'user_id',
                    payload: "1' UNION SELECT user(),database(),version() --",
                    response: 'MySQL version 5.7.31 disclosed',
                    evidence: 'Database information extracted',
                    confidence_level: 98
                }]
            },
            {
                page_name: 'XSS Test Page',
                page_url: 'http://testphp.vulnweb.com/comments',
                vulnerabilities: [{
                    name: 'Stored XSS in Comments',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://testphp.vulnweb.com/comments.php',
                    parameter: 'comment',
                    payload: '<img src=x onerror=alert("STORED_XSS")>',
                    response: 'Malicious script stored permanently',
                    evidence: 'Persistent XSS confirmed',
                    confidence_level: 92
                }]
            }
        ];
        
        let reportCount = 0;
        for (const pageData of pageTests) {
            console.log('Generating separate report for: ' + pageData.page_name);
            const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, pageData.page_url, reportCount + 1);
            
            // Save separate report
            const blob = new Blob([separateReport], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'test_separate_' + (reportCount + 1) + '_' + Date.now() + '.html';
            a.click();
            
            reportCount++;
        }
        
        console.log('All separate reports generated with dynamic content');
        document.body.innerHTML = '<h1>Separate Reports Generated</h1><p>Count: ' + reportCount + '</p>';
        
    } catch (error) {
        console.error('Error:', error);
        document.body.innerHTML = '<h1>Error: ' + error.message + '</h1>';
    }
}
testSeparateReportsDynamic();
</script>
</body>
</html>
"@

$separateReportTest | Out-File -FilePath "test_separate_dynamic.html" -Encoding UTF8

# Open browser to generate separate reports
Start-Process "http://localhost:3000/test_separate_dynamic.html"
Start-Sleep -Seconds 12

# Test 3: Analyze Generated Reports for Dynamic Content
Write-Host "`n4. Analyzing Generated Reports for Dynamic Content..." -ForegroundColor Cyan

# Find the newly generated reports
$newMainReports = Get-ChildItem -Path "." -Filter "test_main_dynamic_*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 1
$newSeparateReports = Get-ChildItem -Path "." -Filter "test_separate_*_*.html" | Sort-Object LastWriteTime -Descending | Select-Object -First 2

$TestResults = @{
    MainReportGenerated = $false
    SeparateReportsGenerated = 0
    DynamicContentInMain = 0
    DynamicContentInSeparate = 0
    VulnSpecificContentMain = 0
    VulnSpecificContentSeparate = 0
    FilesUsedInMain = 0
    FilesUsedInSeparate = 0
    Functions36InMain = 0
    Functions36InSeparate = 0
}

function Test-DynamicContentInReport {
    param($ReportPath, $ExpectedVulnData)
    if (Test-Path $ReportPath) {
        $content = Get-Content $ReportPath -Raw -Encoding UTF8
        $matches = 0
        
        foreach ($data in $ExpectedVulnData) {
            if ($content -like "*$data*") { $matches++ }
        }
        
        return $matches
    }
    return 0
}

# Test Main Report
if ($newMainReports) {
    $TestResults.MainReportGenerated = $true
    Write-Host "  [OK] Main report generated: $($newMainReports.Name)" -ForegroundColor Green
    
    # Expected dynamic content from the vulnerabilities we created
    $expectedMainContent = @(
        "admin' OR '1'='1' --",
        "Login successful without password",
        "Authentication bypass confirmed",
        "<script>alert(`"XSS_CONFIRMED_TEST`")</script>",
        "Script executed in browser",
        "JavaScript execution confirmed",
        "../../../../etc/passwd",
        "root:x:0:0:root:/root:/bin/bash",
        "System file access confirmed",
        "testphp.vulnweb.com",
        "confidence_level: 95",
        "confidence_level: 90",
        "confidence_level: 85"
    )
    
    $TestResults.DynamicContentInMain = Test-DynamicContentInReport $newMainReports.FullName $expectedMainContent
    Write-Host "    Dynamic content in main report: $($TestResults.DynamicContentInMain)/13 expected items found" -ForegroundColor $(if ($TestResults.DynamicContentInMain -ge 10) { "Green" } else { "Yellow" })
    
    # Test for 36 functions usage
    $functions36Indicators = @(
        "comprehensive_details",
        "dynamic_impact",
        "exploitation_steps", 
        "visual_changes",
        "screenshot_data",
        "security_metrics"
    )
    
    $TestResults.Functions36InMain = Test-DynamicContentInReport $newMainReports.FullName $functions36Indicators
    Write-Host "    36 Functions usage in main report: $($TestResults.Functions36InMain)/6 indicators found" -ForegroundColor $(if ($TestResults.Functions36InMain -ge 4) { "Green" } else { "Yellow" })
    
    # Test for system files usage
    $filesIndicators = @(
        "impact_visualizer.js",
        "textual_impact_analyzer.js",
        "visual_impact_data",
        "textual_impact_analysis"
    )
    
    $TestResults.FilesUsedInMain = Test-DynamicContentInReport $newMainReports.FullName $filesIndicators
    Write-Host "    System files usage in main report: $($TestResults.FilesUsedInMain)/4 indicators found" -ForegroundColor $(if ($TestResults.FilesUsedInMain -ge 2) { "Green" } else { "Yellow" })
    
} else {
    Write-Host "  [ERROR] Main report was not generated" -ForegroundColor Red
}

# Test Separate Reports
if ($newSeparateReports.Count -gt 0) {
    $TestResults.SeparateReportsGenerated = $newSeparateReports.Count
    Write-Host "  [OK] $($newSeparateReports.Count) separate reports generated" -ForegroundColor Green
    
    # Expected dynamic content from separate reports
    $expectedSeparateContent = @(
        "1' UNION SELECT user(),database(),version() --",
        "MySQL version 5.7.31 disclosed",
        "Database information extracted",
        "<img src=x onerror=alert(`"STORED_XSS`")>",
        "Malicious script stored permanently",
        "Persistent XSS confirmed",
        "confidence_level: 98",
        "confidence_level: 92"
    )
    
    $totalSeparateDynamic = 0
    $totalSeparateFunctions = 0
    $totalSeparateFiles = 0
    
    foreach ($report in $newSeparateReports) {
        $dynamicContent = Test-DynamicContentInReport $report.FullName $expectedSeparateContent
        $functionsUsage = Test-DynamicContentInReport $report.FullName $functions36Indicators
        $filesUsage = Test-DynamicContentInReport $report.FullName $filesIndicators
        
        $totalSeparateDynamic += $dynamicContent
        $totalSeparateFunctions += $functionsUsage
        $totalSeparateFiles += $filesUsage
        
        Write-Host "    $($report.Name): $dynamicContent dynamic, $functionsUsage functions, $filesUsage files" -ForegroundColor Green
    }
    
    $TestResults.DynamicContentInSeparate = $totalSeparateDynamic
    $TestResults.Functions36InSeparate = $totalSeparateFunctions
    $TestResults.FilesUsedInSeparate = $totalSeparateFiles
    
} else {
    Write-Host "  [ERROR] No separate reports were generated" -ForegroundColor Red
}

# Calculate Overall Score
Write-Host "`n5. Calculating Dynamic Content Score..." -ForegroundColor Cyan

$maxScore = 100
$actualScore = 0

# Report generation (20 points)
if ($TestResults.MainReportGenerated) { $actualScore += 10 }
$actualScore += [math]::Min($TestResults.SeparateReportsGenerated * 5, 10)

# Dynamic content (40 points)
$actualScore += [math]::Min($TestResults.DynamicContentInMain * 2, 20)
$actualScore += [math]::Min($TestResults.DynamicContentInSeparate * 2.5, 20)

# Functions usage (25 points)
$actualScore += [math]::Min($TestResults.Functions36InMain * 2, 12)
$actualScore += [math]::Min($TestResults.Functions36InSeparate * 2, 13)

# Files usage (15 points)
$actualScore += [math]::Min($TestResults.FilesUsedInMain * 2, 8)
$actualScore += [math]::Min($TestResults.FilesUsedInSeparate * 2, 7)

$finalScore = [math]::Round($actualScore)

Write-Host "`n=== MY v4.0 MODIFICATIONS TEST RESULTS ===" -ForegroundColor Yellow
Write-Host "Main Report Generated: $(if ($TestResults.MainReportGenerated) { 'YES' } else { 'NO' })" -ForegroundColor $(if ($TestResults.MainReportGenerated) { "Green" } else { "Red" })
Write-Host "Separate Reports Generated: $($TestResults.SeparateReportsGenerated)" -ForegroundColor $(if ($TestResults.SeparateReportsGenerated -gt 0) { "Green" } else { "Red" })
Write-Host "Dynamic Content in Main: $($TestResults.DynamicContentInMain)/13" -ForegroundColor White
Write-Host "Dynamic Content in Separate: $($TestResults.DynamicContentInSeparate)/8" -ForegroundColor White
Write-Host "36 Functions in Main: $($TestResults.Functions36InMain)/6" -ForegroundColor White
Write-Host "36 Functions in Separate: $($TestResults.Functions36InSeparate)/6" -ForegroundColor White
Write-Host "System Files in Main: $($TestResults.FilesUsedInMain)/4" -ForegroundColor White
Write-Host "System Files in Separate: $($TestResults.FilesUsedInSeparate)/4" -ForegroundColor White

Write-Host "`nFINAL DYNAMIC SCORE: $finalScore/100" -ForegroundColor $(if ($finalScore -ge 80) { "Green" } elseif ($finalScore -ge 60) { "Yellow" } else { "Red" })

if ($finalScore -ge 80) {
    Write-Host "`n[EXCELLENT] MY v4.0 MODIFICATIONS ARE WORKING PERFECTLY!" -ForegroundColor Green
    Write-Host "- Reports generate DYNAMIC content based on discovered vulnerabilities" -ForegroundColor Green
    Write-Host "- Content is AUTOMATIC and vulnerability-specific" -ForegroundColor Green
    Write-Host "- All 36 functions and system files are used" -ForegroundColor Green
    Write-Host "- NO manual, default, or generic content" -ForegroundColor Green
} elseif ($finalScore -ge 60) {
    Write-Host "`n[GOOD] MY v4.0 modifications are working well with minor improvements needed" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] MY v4.0 modifications need fixes" -ForegroundColor Red
}

# Cleanup
Write-Host "`n6. Cleaning up..." -ForegroundColor Cyan
Stop-Process -Id $serverProcess.Id -Force -ErrorAction SilentlyContinue
Remove-Item "test_main_dynamic.html" -ErrorAction SilentlyContinue
Remove-Item "test_separate_dynamic.html" -ErrorAction SilentlyContinue

Write-Host "`n=== MY v4.0 MODIFICATIONS TEST COMPLETE ===" -ForegroundColor Green
