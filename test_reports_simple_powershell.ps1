# اختبار بسيط لإنتاج التقارير من PowerShell
Write-Host "SIMPLE REPORT GENERATION TEST FROM POWERSHELL" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Yellow

# بدء خادم Python
Write-Host ""
Write-Host "STARTING PYTHON SERVER..." -ForegroundColor Cyan
$pythonProcess = Start-Process -FilePath "python" -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
Write-Host "Python server started (PID: $($pythonProcess.Id))" -ForegroundColor Green
Start-Sleep -Seconds 3

# التحقق من الخادم
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "Server is running!" -ForegroundColor Green
} catch {
    Write-Host "Server failed to start!" -ForegroundColor Red
    Stop-Process -Id $pythonProcess.Id -Force
    exit 1
}

Write-Host ""
Write-Host "CREATING SIMPLE TEST HTML..." -ForegroundColor Cyan

# إنشاء ملف HTML بسيط للاختبار
$testHtml = @'
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Simple Report Test</title>
</head>
<body>
    <h1>Testing Report Generation</h1>
    <div id="results"></div>
    
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        async function testReportGeneration() {
            console.log('Starting report generation test...');
            
            try {
                // Initialize system
                const bugBountyCore = new BugBountyCore();
                console.log('System initialized successfully');
                
                // Create test vulnerability
                const testVuln = {
                    name: 'SQL Injection Test',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://test.example.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1",
                    response: 'Login successful without valid credentials',
                    evidence: 'Authentication bypass confirmed'
                };
                
                console.log('Test vulnerability created');
                
                // Extract real data
                const realData = {
                    url: testVuln.url,
                    parameter: testVuln.parameter,
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence
                };
                
                // Apply comprehensive functions
                console.log('Applying comprehensive functions...');
                
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                console.log('comprehensive_details:', testVuln.comprehensive_details ? 'Generated' : 'Failed');
                
                testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
                console.log('dynamic_impact:', testVuln.dynamic_impact ? 'Generated' : 'Failed');
                
                testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
                console.log('exploitation_steps:', testVuln.exploitation_steps ? 'Generated' : 'Failed');
                
                testVuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln);
                console.log('dynamic_recommendations:', testVuln.dynamic_recommendations ? 'Generated' : 'Failed');
                
                // Generate main report
                console.log('Generating main report...');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                
                // Check report content
                console.log('Checking report content...');
                
                const hasObjectObject = mainReport.includes('[object Object]');
                const hasComprehensiveContent = mainReport.includes('comprehensive');
                const hasRealData = mainReport.includes('SQL Injection');
                const hasExploitationSteps = mainReport.includes('exploitation');
                
                console.log('Object Object issue:', hasObjectObject ? 'FOUND' : 'FIXED');
                console.log('Comprehensive content:', hasComprehensiveContent ? 'PRESENT' : 'MISSING');
                console.log('Real data:', hasRealData ? 'PRESENT' : 'MISSING');
                console.log('Exploitation steps:', hasExploitationSteps ? 'PRESENT' : 'MISSING');
                
                // Save main report
                const blob = new Blob([mainReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'powershell_test_main_report.html';
                a.click();
                
                console.log('Main report saved: powershell_test_main_report.html');
                console.log('Report size:', Math.round(mainReport.length / 1024), 'KB');
                
                // Generate separate report
                console.log('Generating separate report...');
                const pageData = {
                    page_name: 'Test Page',
                    page_url: testVuln.url,
                    vulnerabilities: [testVuln]
                };
                
                const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, testVuln.url, 1);
                
                const separateBlob = new Blob([separateReport], { type: 'text/html' });
                const separateUrl = URL.createObjectURL(separateBlob);
                const separateA = document.createElement('a');
                separateA.href = separateUrl;
                separateA.download = 'powershell_test_separate_report.html';
                separateA.click();
                
                console.log('Separate report saved: powershell_test_separate_report.html');
                console.log('Separate report size:', Math.round(separateReport.length / 1024), 'KB');
                
                // Final assessment
                const successCount = [hasComprehensiveContent, hasRealData, hasExploitationSteps].filter(Boolean).length;
                const successRate = Math.round((successCount / 3) * 100);
                
                console.log('');
                console.log('FINAL TEST RESULTS:');
                console.log('==================');
                console.log('Object Object issue:', hasObjectObject ? 'FAILED' : 'PASSED');
                console.log('Success rate:', successRate + '%');
                
                if (!hasObjectObject && successRate >= 66) {
                    console.log('TEST RESULT: SUCCESS!');
                    console.log('All fixes are working correctly!');
                } else {
                    console.log('TEST RESULT: PARTIAL - needs review');
                }
                
                // Update results div
                document.getElementById('results').innerHTML = `
                    <h2>Test Results:</h2>
                    <p><strong>Object Object Issue:</strong> ${hasObjectObject ? 'FAILED' : 'PASSED'}</p>
                    <p><strong>Comprehensive Content:</strong> ${hasComprehensiveContent ? 'PRESENT' : 'MISSING'}</p>
                    <p><strong>Real Data:</strong> ${hasRealData ? 'PRESENT' : 'MISSING'}</p>
                    <p><strong>Exploitation Steps:</strong> ${hasExploitationSteps ? 'PRESENT' : 'MISSING'}</p>
                    <p><strong>Success Rate:</strong> ${successRate}%</p>
                    <p><strong>Main Report:</strong> powershell_test_main_report.html (${Math.round(mainReport.length / 1024)} KB)</p>
                    <p><strong>Separate Report:</strong> powershell_test_separate_report.html (${Math.round(separateReport.length / 1024)} KB)</p>
                    <p><strong>Final Result:</strong> ${!hasObjectObject && successRate >= 66 ? 'SUCCESS!' : 'PARTIAL'}</p>
                `;
                
            } catch (error) {
                console.error('Test failed:', error.message);
                console.error('Error details:', error.stack);
                
                document.getElementById('results').innerHTML = `
                    <h2>Test Failed:</h2>
                    <p><strong>Error:</strong> ${error.message}</p>
                `;
            }
        }
        
        // Run test when page loads
        window.onload = function() {
            console.log('Page loaded, starting test...');
            testReportGeneration();
        };
    </script>
</body>
</html>
'@

$testHtml | Out-File -FilePath "powershell_test.html" -Encoding UTF8
Write-Host "Test HTML created: powershell_test.html" -ForegroundColor Green

Write-Host ""
Write-Host "OPENING TEST PAGE..." -ForegroundColor Cyan

# فتح صفحة الاختبار
$testUrl = "http://localhost:3000/powershell_test.html"
Start-Process $testUrl

Write-Host "Test page opened: $testUrl" -ForegroundColor Green
Write-Host "Check browser console for detailed results" -ForegroundColor Yellow

Write-Host ""
Write-Host "WAITING FOR TEST COMPLETION..." -ForegroundColor Cyan

# انتظار لإكمال الاختبار
Start-Sleep -Seconds 15

Write-Host ""
Write-Host "CHECKING FOR GENERATED REPORTS..." -ForegroundColor Cyan

# فحص التقارير المُنتجة في مجلد التحميلات
$downloadsPath = [Environment]::GetFolderPath("UserProfile") + "\Downloads"
$reportFiles = Get-ChildItem -Path $downloadsPath -Filter "*powershell_test*report*.html" -ErrorAction SilentlyContinue

if ($reportFiles.Count -gt 0) {
    Write-Host "Generated reports found in Downloads:" -ForegroundColor Green
    foreach ($file in $reportFiles) {
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "  $($file.Name) ($size KB)" -ForegroundColor Green
        
        # فحص محتوى التقرير
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            $hasObjectObject = $content -match '\[object Object\]'
            $hasComprehensive = $content -match 'comprehensive'
            $hasSQLInjection = $content -match 'SQL Injection'
            
            Write-Host "    [object Object] issue: $(if ($hasObjectObject) { 'FOUND' } else { 'FIXED' })" -ForegroundColor $(if ($hasObjectObject) { "Red" } else { "Green" })
            Write-Host "    Comprehensive content: $(if ($hasComprehensive) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasComprehensive) { "Green" } else { "Red" })
            Write-Host "    Real data: $(if ($hasSQLInjection) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasSQLInjection) { "Green" } else { "Red" })
        }
    }
} else {
    Write-Host "No reports found in Downloads folder" -ForegroundColor Yellow
    Write-Host "Reports may have been saved elsewhere or test may have failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "CHECKING SYSTEM FILES..." -ForegroundColor Cyan

# فحص ملفات النظام
$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$impactVisualizerFile = "assets/modules/bugbounty/impact_visualizer.js"
$textualAnalyzerFile = "assets/modules/bugbounty/textual_impact_analyzer.js"

$filesOk = $true
$requiredFiles = @($bugBountyFile, $impactVisualizerFile, $textualAnalyzerFile)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        $size = [math]::Round((Get-Item $file).Length / 1KB, 2)
        Write-Host "FOUND: $file ($size KB)" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $file" -ForegroundColor Red
        $filesOk = $false
    }
}

Write-Host ""
Write-Host "FINAL ASSESSMENT:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

Write-Host "System files: $(if ($filesOk) { 'OK' } else { 'MISSING' })" -ForegroundColor $(if ($filesOk) { "Green" } else { "Red" })
Write-Host "Reports generated: $(if ($reportFiles.Count -gt 0) { $reportFiles.Count } else { '0' })" -ForegroundColor $(if ($reportFiles.Count -gt 0) { "Green" } else { "Red" })
Write-Host "Test page: OPENED" -ForegroundColor Green

if ($filesOk -and $reportFiles.Count -gt 0) {
    Write-Host ""
    Write-Host "SUCCESS! Reports were generated successfully!" -ForegroundColor Green
    Write-Host "Check the downloaded reports to verify all fixes are working!" -ForegroundColor Green
} elseif ($filesOk) {
    Write-Host ""
    Write-Host "PARTIAL SUCCESS! System files are OK but reports may not have downloaded" -ForegroundColor Yellow
    Write-Host "Check browser console for detailed test results" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "ISSUES DETECTED! Some system files are missing" -ForegroundColor Red
}

Write-Host ""
Write-Host "CLEANING UP..." -ForegroundColor Cyan

# تنظيف
Remove-Item "powershell_test.html" -ErrorAction SilentlyContinue

# إيقاف خادم Python
Write-Host "Stopping Python server..." -ForegroundColor Yellow
Stop-Process -Id $pythonProcess.Id -Force
Write-Host "Python server stopped." -ForegroundColor Green

Write-Host ""
Write-Host "POWERSHELL REPORT GENERATION TEST COMPLETE!" -ForegroundColor Green
