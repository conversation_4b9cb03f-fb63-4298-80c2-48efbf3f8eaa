<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار النظام الشامل التفصيلي v4.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار النظام الشامل التفصيلي v4.0</h1>
        
        <button class="btn" onclick="testSystem()">🚀 اختبار النظام</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testSystem() {
            clearResults();
            log('🚀 بدء اختبار النظام الشامل التفصيلي v4.0', 'info');
            
            try {
                // تحميل ملف BugBountyCore.js
                log('📂 تحميل ملف BugBountyCore.js...', 'info');
                
                const script = document.createElement('script');
                script.src = './assets/modules/bugbounty/BugBountyCore.js';
                
                script.onload = async () => {
                    try {
                        log('✅ تم تحميل ملف BugBountyCore.js بنجاح', 'success');
                        
                        // تهيئة النظام
                        log('🔄 تهيئة النظام...', 'info');
                        const bugBountyCore = new BugBountyCore();
                        log('✅ تم تهيئة النظام بنجاح', 'success');
                        
                        // إنشاء بيانات اختبار
                        const testVuln = {
                            name: 'XSS Test Vulnerability',
                            type: 'XSS',
                            severity: 'High',
                            url: 'http://example.com/test',
                            payload: '<script>alert("test")</script>',
                            evidence: 'Script executed successfully'
                        };
                        
                        const testRealData = {
                            payload: '<script>alert("test")</script>',
                            response: 'Script executed',
                            evidence: 'Alert appeared',
                            url: 'http://example.com/test'
                        };
                        
                        log('🔥 اختبار الدالة الشاملة التفصيلية...', 'info');
                        
                        // اختبار الدالة الرئيسية
                        const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                        
                        if (comprehensiveDetails && typeof comprehensiveDetails === 'object') {
                            const keys = Object.keys(comprehensiveDetails);
                            log(`✅ الدالة الشاملة التفصيلية تعمل بنجاح (${keys.length} مفتاح)`, 'success');
                            log(`🔑 المفاتيح: ${keys.join(', ')}`, 'info');
                            
                            // فحص المحتوى
                            if (comprehensiveDetails.technical_details) {
                                log('✅ technical_details موجود', 'success');
                            }
                            if (comprehensiveDetails.impact_analysis) {
                                log('✅ impact_analysis موجود', 'success');
                            }
                            if (comprehensiveDetails.exploitation_results) {
                                log('✅ exploitation_results موجود', 'success');
                            }
                            
                            // اختبار التقرير المنفصل
                            log('📋 اختبار التقرير المنفصل...', 'info');
                            const testPageData = {
                                page_name: 'Test Page',
                                page_url: 'http://example.com/test',
                                vulnerabilities: [testVuln]
                            };
                            
                            const separateReport = await bugBountyCore.generatePageHTMLReport(testPageData, 'http://example.com/test', 1);
                            
                            if (separateReport && separateReport.length > 1000) {
                                log(`✅ التقرير المنفصل تم إنشاؤه بنجاح (${(separateReport.length / 1024).toFixed(1)} KB)`, 'success');
                                
                                // فحص عدم وجود محتوى عام
                                const hasGenericContent = 
                                    separateReport.includes('تم اكتشاف ثغرة أمنية') ||
                                    separateReport.includes('قد يؤثر على أمان الموقع') ||
                                    separateReport.includes('يُنصح بإصلاح هذه الثغرة');
                                
                                if (!hasGenericContent) {
                                    log('🎉 التقرير المنفصل لا يحتوي على محتوى عام!', 'success');
                                    log('✅ النظام الشامل التفصيلي v4.0 يعمل بكفاءة عالية', 'success');
                                } else {
                                    log('⚠️ التقرير المنفصل قد يحتوي على بعض المحتوى العام', 'warning');
                                }
                            } else {
                                log('❌ فشل في إنشاء التقرير المنفصل', 'error');
                            }
                            
                            // اختبار التقرير الرئيسي
                            log('📊 اختبار التقرير الرئيسي...', 'info');
                            const testAnalysis = {
                                vulnerabilities: [testVuln],
                                summary: {
                                    total_vulnerabilities: 1,
                                    high_severity: 1
                                }
                            };
                            
                            const mainReport = await bugBountyCore.generateFinalComprehensiveReport(testAnalysis, [], 'http://example.com');
                            
                            if (mainReport && mainReport.length > 1000) {
                                log(`✅ التقرير الرئيسي تم إنشاؤه بنجاح (${(mainReport.length / 1024).toFixed(1)} KB)`, 'success');
                                
                                // فحص عدم وجود محتوى عام
                                const hasGenericContent = 
                                    mainReport.includes('تم اكتشاف ثغرة أمنية') ||
                                    mainReport.includes('قد يؤثر على أمان الموقع') ||
                                    mainReport.includes('يُنصح بإصلاح هذه الثغرة');
                                
                                if (!hasGenericContent) {
                                    log('🎉 التقرير الرئيسي لا يحتوي على محتوى عام!', 'success');
                                } else {
                                    log('⚠️ التقرير الرئيسي قد يحتوي على بعض المحتوى العام', 'warning');
                                }
                            } else {
                                log('❌ فشل في إنشاء التقرير الرئيسي', 'error');
                            }
                            
                            log('🏁 انتهى الاختبار', 'info');
                            log('🔥 النظام الشامل التفصيلي v4.0 جاهز للاستخدام!', 'success');
                            
                        } else {
                            log('❌ الدالة الشاملة التفصيلية لا تعمل بشكل صحيح', 'error');
                        }
                        
                    } catch (error) {
                        log(`❌ خطأ في تنفيذ الاختبار: ${error.message}`, 'error');
                        console.error('تفاصيل الخطأ:', error);
                    }
                };
                
                script.onerror = () => {
                    log('❌ فشل في تحميل ملف BugBountyCore.js', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ خطأ عام في الاختبار: ${error.message}`, 'error');
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testSystem, 1000);
        });
    </script>
</body>
</html>
