# اختبار تلقائي مباشر للتصدير الفعلي - بدون تدخل يدوي
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "🤖 اختبار تلقائي مباشر للتصدير الفعلي" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

$testStartTime = Get-Date
$testResults = @{
    CodeCheck = $false
    FileCreation = $false
    BrowserTest = $false
    ExportSuccess = $false
    ErrorFound = $false
    ErrorMessage = ""
    Duration = 0
    HTMLSize = 0
}

# المرحلة 1: فحص الكود
Write-Host "🔍 المرحلة 1: فحص الكود..." -ForegroundColor Cyan
try {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
    $pageHTMLCount = ($content | Select-String "async generatePageHTMLReport").Count
    $extractCalls = ($content | Select-String "this\.extractRealDataFromDiscoveredVulnerability").Count
    
    Write-Host "   - دوال generatePageHTMLReport: $pageHTMLCount" -ForegroundColor White
    Write-Host "   - استدعاءات extractRealDataFromDiscoveredVulnerability: $extractCalls" -ForegroundColor White
    
    if ($pageHTMLCount -eq 1 -and $extractCalls -lt 40) {
        Write-Host "   ✅ فحص الكود نجح" -ForegroundColor Green
        $testResults.CodeCheck = $true
    } else {
        Write-Host "   ❌ فحص الكود فشل" -ForegroundColor Red
    }
} catch {
    Write-Host "   ❌ خطأ في فحص الكود: $($_.Exception.Message)" -ForegroundColor Red
}

# المرحلة 2: إنشاء ملف اختبار تلقائي
Write-Host ""
Write-Host "📝 المرحلة 2: إنشاء ملف اختبار تلقائي..." -ForegroundColor Cyan

$automatedTestHTML = @"
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار تلقائي للتصدير</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .loading { background: #fff3cd; border: 1px solid #ffeaa7; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; }
        .result { font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <h1>🤖 اختبار تلقائي للتصدير</h1>
    <div id="status" class="status loading">جاري التحميل...</div>
    <div id="progress"></div>
    <div id="result" class="result"></div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        const testResult = {
            status: 'INITIALIZING',
            stage: 0,
            totalStages: 4,
            error: null,
            success: false,
            duration: 0,
            htmlSize: 0,
            startTime: Date.now()
        };

        function updateStatus(status, stage = null) {
            testResult.status = status;
            if (stage !== null) testResult.stage = stage;
            
            document.getElementById('status').textContent = 
                'المرحلة ' + testResult.stage + '/' + testResult.totalStages + ': ' + status;
            
            document.getElementById('progress').innerHTML = 
                '<div style="width: 100%; background: #e9ecef; border-radius: 10px;">' +
                '<div style="width: ' + (testResult.stage / testResult.totalStages * 100) + '%; ' +
                'background: #007bff; height: 20px; border-radius: 10px; transition: width 0.3s;"></div></div>';
        }

        function logResult(message) {
            document.getElementById('result').textContent += new Date().toLocaleTimeString() + ': ' + message + '\\n';
        }

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            testResult.error = e.message;
            testResult.success = false;
            
            if (e.message.includes('Maximum call stack size exceeded')) {
                testResult.error = 'STACK_OVERFLOW_ERROR';
                updateStatus('❌ خطأ: Maximum call stack size exceeded');
                document.getElementById('status').className = 'status error';
                logResult('❌ تم اكتشاف Maximum call stack size exceeded - الإصلاحات لم تعمل!');
            } else {
                updateStatus('❌ خطأ: ' + e.message);
                document.getElementById('status').className = 'status error';
                logResult('❌ خطأ JavaScript: ' + e.message);
            }
            
            // كتابة النتيجة النهائية
            setTimeout(writeResults, 1000);
        });

        async function runAutomatedExportTest() {
            try {
                // المرحلة 1: تحضير البيانات
                updateStatus('تحضير البيانات...', 1);
                logResult('🔄 بدء الاختبار التلقائي للتصدير');
                
                const bugBountyCore = new BugBountyCore();
                logResult('✅ تم إنشاء مثيل BugBountyCore');

                const testData = {
                    vulnerabilities: [{
                        name: 'اختبار SQL Injection تلقائي',
                        type: 'SQL Injection',
                        severity: 'Critical',
                        description: 'ثغرة اختبار للتصدير التلقائي',
                        payload: "admin' OR '1'='1' --",
                        url: 'https://example.com/login',
                        parameter: 'username',
                        method: 'POST',
                        response: 'تم تسجيل الدخول بنجاح',
                        evidence: 'تم الوصول للوحة الإدارة',
                        confidence_level: 95,
                        extracted_real_data: {
                            payload: "admin' OR '1'='1' --",
                            url: 'https://example.com/login',
                            parameter: 'username',
                            response: 'تم تسجيل الدخول بنجاح',
                            method: 'POST'
                        },
                        comprehensive_details: 'تفاصيل شاملة تلقائية لثغرة SQL Injection',
                        dynamic_impact: 'تأثير خطير - إمكانية الوصول لقاعدة البيانات',
                        exploitation_steps: 'خطوات الاستغلال التلقائية المفصلة',
                        dynamic_recommendations: 'توصيات الإصلاح التلقائية العاجلة'
                    }]
                };
                
                logResult('✅ تم إنشاء بيانات الاختبار');

                // المرحلة 2: إنشاء تقرير HTML
                updateStatus('إنشاء تقرير HTML...', 2);
                logResult('📄 بدء إنشاء تقرير HTML...');

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('TIMEOUT_20_SECONDS')), 20000);
                });

                const exportPromise = bugBountyCore.generatePageHTMLReport(testData, 'https://example.com', 1);
                
                const htmlResult = await Promise.race([exportPromise, timeoutPromise]);
                
                testResult.duration = Date.now() - testResult.startTime;
                testResult.htmlSize = htmlResult ? htmlResult.length : 0;
                
                logResult('✅ تم إنشاء HTML بحجم: ' + testResult.htmlSize + ' حرف');
                logResult('⏱️ وقت التنفيذ: ' + testResult.duration + 'ms');

                // المرحلة 3: إنشاء ملف التحميل
                updateStatus('إنشاء ملف التحميل...', 3);
                logResult('💾 إنشاء ملف التحميل...');

                if (htmlResult && htmlResult.length > 100) {
                    const blob = new Blob([htmlResult], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    
                    logResult('✅ تم إنشاء ملف التحميل');

                    // المرحلة 4: تحميل التقرير
                    updateStatus('تحميل التقرير...', 4);
                    logResult('⬇️ تحميل التقرير...');

                    const a = document.createElement('a');
                    a.href = url;
                    a.download = 'automated_test_report_' + Date.now() + '.html';
                    a.click();
                    
                    logResult('✅ تم تشغيل التحميل');
                    
                    // تنظيف الذاكرة
                    setTimeout(() => URL.revokeObjectURL(url), 1000);
                    
                    testResult.success = true;
                    updateStatus('✅ اكتمل بنجاح!', 4);
                    document.getElementById('status').className = 'status success';
                    logResult('🎉 نجح الاختبار التلقائي للتصدير!');
                    logResult('✅ تم حل مشكلة Maximum call stack size exceeded');
                    
                } else {
                    throw new Error('HTML_EMPTY_OR_TOO_SHORT');
                }

            } catch (error) {
                testResult.duration = Date.now() - testResult.startTime;
                testResult.error = error.message;
                testResult.success = false;
                
                updateStatus('❌ فشل: ' + error.message);
                document.getElementById('status').className = 'status error';
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    logResult('🚨 المشكلة لا تزال موجودة: Maximum call stack size exceeded');
                } else if (error.message.includes('TIMEOUT')) {
                    logResult('⏰ انتهت مهلة الاختبار - قد تكون هناك حلقة لا نهائية');
                } else {
                    logResult('❌ خطأ في التصدير: ' + error.message);
                }
            }
            
            // كتابة النتيجة النهائية
            setTimeout(writeResults, 2000);
        }

        function writeResults() {
            // كتابة النتائج في العنوان للقراءة من PowerShell
            document.title = 'TEST_RESULT:' + JSON.stringify(testResult);
            
            logResult('');
            logResult('=== النتيجة النهائية ===');
            logResult('الحالة: ' + (testResult.success ? 'نجح' : 'فشل'));
            logResult('الخطأ: ' + (testResult.error || 'لا يوجد'));
            logResult('المدة: ' + testResult.duration + 'ms');
            logResult('حجم HTML: ' + testResult.htmlSize + ' حرف');
        }

        // بدء الاختبار عند تحميل الصفحة
        window.onload = function() {
            setTimeout(runAutomatedExportTest, 1000);
        };
    </script>
</body>
</html>
"@

try {
    $automatedTestHTML | Out-File -FilePath "automated_export_test.html" -Encoding UTF8
    Write-Host "   ✅ تم إنشاء ملف الاختبار التلقائي" -ForegroundColor Green
    $testResults.FileCreation = $true
} catch {
    Write-Host "   ❌ فشل في إنشاء ملف الاختبار: $($_.Exception.Message)" -ForegroundColor Red
}

# المرحلة 3: تشغيل الاختبار التلقائي
if ($testResults.FileCreation) {
    Write-Host ""
    Write-Host "🌐 المرحلة 3: تشغيل الاختبار التلقائي..." -ForegroundColor Cyan
    
    try {
        $process = Start-Process -FilePath "automated_export_test.html" -PassThru
        Write-Host "   ✅ تم تشغيل الاختبار في المتصفح" -ForegroundColor Green
        $testResults.BrowserTest = $true
        
        # انتظار 30 ثانية لإكمال الاختبار
        Write-Host "   ⏱️ انتظار 30 ثانية لإكمال الاختبار..." -ForegroundColor Yellow
        
        for ($i = 1; $i -le 30; $i++) {
            Write-Progress -Activity "تشغيل الاختبار التلقائي" -Status "الثانية $i من 30" -PercentComplete (($i / 30) * 100)
            Start-Sleep -Seconds 1
        }
        Write-Progress -Activity "تشغيل الاختبار التلقائي" -Completed
        
        Write-Host "   ✅ انتهى وقت الاختبار" -ForegroundColor Green
        
        # محاولة قراءة النتائج من عنوان النافذة
        Write-Host "   🔍 محاولة قراءة النتائج..." -ForegroundColor Cyan
        
        # إغلاق المتصفح
        if ($process -and !$process.HasExited) {
            $process.CloseMainWindow()
            Start-Sleep -Seconds 2
            if (!$process.HasExited) {
                $process.Kill()
            }
        }
        Write-Host "   ✅ تم إغلاق المتصفح" -ForegroundColor Green
        
        # فحص وجود ملف تم تحميله
        $downloadFiles = Get-ChildItem -Path $env:USERPROFILE\Downloads -Filter "automated_test_report_*.html" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 1
        
        if ($downloadFiles) {
            Write-Host "   ✅ تم العثور على ملف محمل: $($downloadFiles.Name)" -ForegroundColor Green
            Write-Host "   📊 حجم الملف: $([math]::Round($downloadFiles.Length / 1024, 1)) KB" -ForegroundColor Green
            $testResults.ExportSuccess = $true
            $testResults.HTMLSize = $downloadFiles.Length
        } else {
            Write-Host "   ⚠️ لم يتم العثور على ملف محمل" -ForegroundColor Yellow
        }
        
    } catch {
        Write-Host "   ❌ فشل في تشغيل الاختبار: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# المرحلة 4: النتائج النهائية
$testResults.Duration = ((Get-Date) - $testStartTime).TotalSeconds

Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "📊 النتائج النهائية للاختبار التلقائي" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow

Write-Host "🔍 نتائج الاختبارات:" -ForegroundColor Cyan
Write-Host "   - فحص الكود: $(if($testResults.CodeCheck){'✅ نجح'}else{'❌ فشل'})" -ForegroundColor $(if($testResults.CodeCheck){'Green'}else{'Red'})
Write-Host "   - إنشاء ملف الاختبار: $(if($testResults.FileCreation){'✅ نجح'}else{'❌ فشل'})" -ForegroundColor $(if($testResults.FileCreation){'Green'}else{'Red'})
Write-Host "   - تشغيل المتصفح: $(if($testResults.BrowserTest){'✅ نجح'}else{'❌ فشل'})" -ForegroundColor $(if($testResults.BrowserTest){'Green'}else{'Red'})
Write-Host "   - نجاح التصدير: $(if($testResults.ExportSuccess){'✅ نجح'}else{'❌ فشل'})" -ForegroundColor $(if($testResults.ExportSuccess){'Green'}else{'Red'})

Write-Host ""
Write-Host "📈 إحصائيات:" -ForegroundColor Cyan
Write-Host "   - مدة الاختبار الإجمالية: $([math]::Round($testResults.Duration, 1)) ثانية" -ForegroundColor White
if ($testResults.HTMLSize -gt 0) {
    Write-Host "   - حجم HTML المُنتج: $([math]::Round($testResults.HTMLSize / 1024, 1)) KB" -ForegroundColor White
}

Write-Host ""
Write-Host "🎯 النتيجة النهائية:" -ForegroundColor Yellow

$allTestsPassed = $testResults.CodeCheck -and $testResults.FileCreation -and $testResults.BrowserTest -and $testResults.ExportSuccess

if ($allTestsPassed) {
    Write-Host "🎉 نجح الاختبار التلقائي للتصدير بالكامل!" -ForegroundColor Green
    Write-Host "✅ تم حل مشكلة Maximum call stack size exceeded" -ForegroundColor Green
    Write-Host "✅ التصدير يعمل بشكل صحيح وتلقائي" -ForegroundColor Green
    Write-Host "✅ النظام جاهز للاستخدام الفعلي" -ForegroundColor Green
} else {
    Write-Host "❌ فشل الاختبار التلقائي للتصدير" -ForegroundColor Red
    if (-not $testResults.ExportSuccess) {
        Write-Host "🚨 مشكلة Maximum call stack size exceeded قد تكون لا تزال موجودة" -ForegroundColor Red
    }
    Write-Host "🔧 يحتاج إصلاح إضافي" -ForegroundColor Red
}

# تنظيف الملفات
Write-Host ""
Write-Host "🧹 تنظيف الملفات المؤقتة..." -ForegroundColor Cyan
try {
    Remove-Item "automated_export_test.html" -ErrorAction SilentlyContinue
    Write-Host "✅ تم تنظيف الملفات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ فشل في تنظيف بعض الملفات" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
