<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط لإنتاج التقارير</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-button { background: #4caf50; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 10px; }
        .test-button:hover { background: #45a049; }
        .results { background: #f5f5f5; border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .success { color: #4caf50; font-weight: bold; }
        .error { color: #f44336; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار بسيط لإنتاج التقارير مع التعديلات الجديدة</h1>
        
        <button class="test-button" onclick="generateSimpleReport()">🔥 إنتاج تقرير تجريبي</button>
        <button class="test-button" onclick="testComprehensiveFunctions()">🛠️ اختبار الدوال الشاملة</button>
        
        <div class="results" id="results">
            <h3>📊 النتائج:</h3>
            <p>اضغط على الأزرار لبدء الاختبار...</p>
        </div>
    </div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>

    <script>
        let bugBountyCore = null;
        let resultsDiv = document.getElementById('results');

        function log(message, type = 'info') {
            const p = document.createElement('p');
            p.className = type;
            p.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(p);
        }

        function clearResults() {
            resultsDiv.innerHTML = '<h3>📊 النتائج:</h3>';
        }

        async function generateSimpleReport() {
            clearResults();
            log('🔥 بدء إنتاج تقرير تجريبي بسيط...', 'info');

            try {
                // تهيئة النظام
                bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام بنجاح', 'success');

                // إنشاء ثغرة تجريبية
                const testVuln = {
                    name: 'SQL Injection تجريبي',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://test.example.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1",
                    response: 'Login successful without valid credentials',
                    evidence: 'تم تجاوز نظام المصادقة بنجاح'
                };

                log('📊 تم إنشاء ثغرة تجريبية', 'info');

                // استخراج البيانات الحقيقية
                const realData = {
                    url: testVuln.url,
                    parameter: testVuln.parameter,
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence
                };

                log('🔧 تطبيق الدوال الشاملة التفصيلية...', 'info');

                // تطبيق الدوال الشاملة مع الإصلاحات الجديدة
                testVuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                log(`   comprehensive_details: ${testVuln.comprehensive_details ? '✅ تم إنتاجها' : '❌ فشل'}`, testVuln.comprehensive_details ? 'success' : 'error');

                testVuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData);
                log(`   dynamic_impact: ${testVuln.dynamic_impact ? '✅ تم إنتاجها' : '❌ فشل'}`, testVuln.dynamic_impact ? 'success' : 'error');

                testVuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData);
                log(`   exploitation_steps: ${testVuln.exploitation_steps ? '✅ تم إنتاجها' : '❌ فشل'}`, testVuln.exploitation_steps ? 'success' : 'error');

                testVuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln);
                log(`   dynamic_recommendations: ${testVuln.dynamic_recommendations ? '✅ تم إنتاجها' : '❌ فشل'}`, testVuln.dynamic_recommendations ? 'success' : 'error');

                // إنتاج التقرير الرئيسي
                log('📄 إنتاج التقرير الرئيسي...', 'info');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);

                // فحص التقرير
                log('🔍 فحص محتوى التقرير...', 'info');
                
                const hasObjectObject = mainReport.includes('[object Object]');
                const hasComprehensiveContent = mainReport.includes('تحليل شامل تفصيلي للثغرة');
                const hasRealData = mainReport.includes('تفاصيل الاكتشاف الحقيقية');
                const hasExploitationSteps = mainReport.includes('خطوات الاستغلال');

                log(`   🔧 مشكلة [object Object]: ${hasObjectObject ? '❌ موجودة' : '✅ مُصلحة'}`, hasObjectObject ? 'error' : 'success');
                log(`   📋 محتوى شامل تفصيلي: ${hasComprehensiveContent ? '✅ موجود' : '❌ مفقود'}`, hasComprehensiveContent ? 'success' : 'error');
                log(`   🔍 بيانات حقيقية: ${hasRealData ? '✅ موجودة' : '❌ مفقودة'}`, hasRealData ? 'success' : 'error');
                log(`   🎯 خطوات الاستغلال: ${hasExploitationSteps ? '✅ موجودة' : '❌ مفقودة'}`, hasExploitationSteps ? 'success' : 'error');

                // حفظ التقرير
                const blob = new Blob([mainReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `simple_test_report_${Date.now()}.html`;
                a.click();

                log(`📊 حجم التقرير: ${Math.round(mainReport.length / 1024)} KB`, 'info');
                log('✅ تم إنتاج وحفظ التقرير بنجاح!', 'success');

                // تقييم النجاح
                const successCount = [hasComprehensiveContent, hasRealData, hasExploitationSteps].filter(Boolean).length;
                const successRate = Math.round((successCount / 3) * 100);
                
                if (!hasObjectObject && successRate >= 66) {
                    log('🎉 الاختبار نجح! التعديلات تعمل بشكل صحيح!', 'success');
                } else {
                    log('⚠️ الاختبار جزئي - قد تحتاج مراجعة إضافية', 'error');
                }

                // إنتاج تقرير منفصل
                log('📋 إنتاج تقرير منفصل...', 'info');
                const pageData = {
                    page_name: 'صفحة اختبار',
                    page_url: testVuln.url,
                    vulnerabilities: [testVuln]
                };

                const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, testVuln.url, 1);
                
                const separateBlob = new Blob([separateReport], { type: 'text/html' });
                const separateUrl = URL.createObjectURL(separateBlob);
                const separateA = document.createElement('a');
                separateA.href = separateUrl;
                separateA.download = `simple_separate_report_${Date.now()}.html`;
                separateA.click();

                log('✅ تم إنتاج وحفظ التقرير المنفصل أيضاً!', 'success');

            } catch (error) {
                log('❌ خطأ في إنتاج التقرير: ' + error.message, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }

        async function testComprehensiveFunctions() {
            clearResults();
            log('🛠️ اختبار الدوال الشاملة التفصيلية...', 'info');

            try {
                bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام', 'success');

                const testVuln = {
                    name: 'XSS تجريبي',
                    type: 'Cross-Site Scripting',
                    severity: 'High',
                    url: 'http://test.example.com/search.php',
                    parameter: 'query',
                    payload: '<script>alert("XSS")</script>',
                    response: 'Script executed in browser',
                    evidence: 'تم تنفيذ الكود JavaScript'
                };

                const realData = {
                    url: testVuln.url,
                    parameter: testVuln.parameter,
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence
                };

                log('🔧 اختبار الدوال الأساسية...', 'info');

                // اختبار الدوال واحدة تلو الأخرى
                const functions = [
                    { name: 'generateComprehensiveDetailsFromRealData', func: () => bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData) },
                    { name: 'generateDynamicImpactForAnyVulnerability', func: () => bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, realData) },
                    { name: 'generateRealExploitationStepsForVulnerabilityComprehensive', func: () => bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, realData) },
                    { name: 'generateDynamicRecommendationsForVulnerability', func: () => bugBountyCore.generateDynamicRecommendationsForVulnerability(testVuln) },
                    { name: 'generateVisualChangesForVulnerability', func: () => bugBountyCore.generateVisualChangesForVulnerability(testVuln, realData) }
                ];

                let successCount = 0;
                for (const funcTest of functions) {
                    try {
                        const result = await funcTest.func();
                        if (result) {
                            log(`   ✅ ${funcTest.name}: يعمل بشكل صحيح`, 'success');
                            successCount++;
                        } else {
                            log(`   ❌ ${funcTest.name}: لا يُرجع نتيجة`, 'error');
                        }
                    } catch (error) {
                        log(`   ❌ ${funcTest.name}: خطأ - ${error.message}`, 'error');
                    }
                }

                const successRate = Math.round((successCount / functions.length) * 100);
                log(`📊 معدل نجاح الدوال: ${successCount}/${functions.length} (${successRate}%)`, successRate >= 80 ? 'success' : 'error');

                if (successRate >= 80) {
                    log('🎉 جميع الدوال الأساسية تعمل بشكل ممتاز!', 'success');
                } else {
                    log('⚠️ بعض الدوال تحتاج مراجعة', 'error');
                }

            } catch (error) {
                log('❌ خطأ في اختبار الدوال: ' + error.message, 'error');
            }
        }

        // رسالة ترحيب
        window.onload = function() {
            log('🔥 مرحباً بك في اختبار النظام v4.0 البسيط', 'info');
            log('📋 اضغط على الأزرار لاختبار التعديلات الجديدة', 'info');
        };
    </script>
</body>
</html>
