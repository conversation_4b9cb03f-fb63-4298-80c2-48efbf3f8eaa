# التحقق الشامل من الإصلاحات واختبار التصدير الفعلي
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "🔧 التحقق الشامل من إصلاح مشكلة Maximum call stack size exceeded" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-CodeFix {
    param(
        [string]$TestName,
        [string]$Description,
        [scriptblock]$TestScript
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "   ✅ نجح الاختبار" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ فشل الاختبار" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

Write-Host "📋 المرحلة 1: فحص الكود والإصلاحات" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Gray

# اختبار 1: فحص عدد دوال generatePageHTMLReport
Test-CodeFix -TestName "عدد دوال generatePageHTMLReport" -Description "يجب أن يكون 1 فقط" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    $count = ($content | Select-String "async generatePageHTMLReport").Count
    Write-Host "      العدد الحالي: $count" -ForegroundColor White
    return ($count -eq 1)
}

# اختبار 2: فحص الاستدعاءات المتكررة الخطيرة
Test-CodeFix -TestName "الاستدعاءات المتكررة الخطيرة" -Description "يجب أن تكون 0" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    $pattern1 = $content | Select-String "generatePageHTMLReport.*generatePageHTMLReport"
    $pattern2 = $content | Select-String "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData"
    $pattern3 = $content | Select-String "generateDynamicImpactForAnyVulnerability.*generateDynamicImpactForAnyVulnerability"
    
    $totalRecursive = $pattern1.Count + $pattern2.Count + $pattern3.Count
    Write-Host "      الاستدعاءات المتكررة: $totalRecursive" -ForegroundColor White
    
    return ($totalRecursive -eq 0)
}

# اختبار 3: فحص تقليل استدعاءات extractRealDataFromDiscoveredVulnerability
Test-CodeFix -TestName "تقليل الاستدعاءات المتكررة" -Description "يجب أن يكون أقل من 40" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    $extractCalls = ($content | Select-String "this\.extractRealDataFromDiscoveredVulnerability").Count
    
    Write-Host "      عدد الاستدعاءات: $extractCalls (كان 43)" -ForegroundColor White
    
    return ($extractCalls -lt 40)
}

# اختبار 4: فحص استخدام البيانات المحفوظة
Test-CodeFix -TestName "استخدام البيانات المحفوظة" -Description "يجب استخدام البيانات المحفوظة" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    $savedDataUsage = ($content | Select-String "vuln\.extracted_real_data.*vuln\.realData").Count
    $directUsage = ($content | Select-String "realData \|\| \{\}").Count
    
    Write-Host "      استخدام البيانات المحفوظة: $savedDataUsage" -ForegroundColor White
    Write-Host "      استخدام البيانات المباشرة: $directUsage" -ForegroundColor White
    
    return ($savedDataUsage -gt 0 -or $directUsage -gt 0)
}

# اختبار 5: فحص الإصلاحات المحددة
Test-CodeFix -TestName "الإصلاحات المحددة" -Description "فحص الإصلاحات في المواضع المحددة" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # فحص السطر 36832 (تم إصلاحه)
    $line36832 = $content[36831]
    $fix1 = $line36832 -like "*realData*" -and $line36832 -notlike "*extractRealDataFromDiscoveredVulnerability*"
    
    # فحص السطر 5357 (تم إصلاحه)
    $line5357 = $content[5356]
    $fix2 = $line5357 -notlike "*generateDynamicImpactForAnyVulnerability*"
    
    Write-Host "      إصلاح السطر 36832: $(if($fix1){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "      إصلاح السطر 5357: $(if($fix2){'✅'}else{'❌'})" -ForegroundColor White
    
    return ($fix1 -and $fix2)
}

Write-Host ""
Write-Host "📋 المرحلة 2: اختبار التصدير الفعلي" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Gray

# اختبار 6: فحص وجود الملفات المطلوبة
Test-CodeFix -TestName "وجود الملفات المطلوبة" -Description "فحص وجود الملفات الأساسية" -TestScript {
    $coreFile = Test-Path "assets\modules\bugbounty\BugBountyCore.js"
    $testFile = Test-Path "test_real_export.html"
    
    Write-Host "      BugBountyCore.js: $(if($coreFile){'✅'}else{'❌'})" -ForegroundColor White
    Write-Host "      test_real_export.html: $(if($testFile){'✅'}else{'❌'})" -ForegroundColor White
    
    return ($coreFile -and $testFile)
}

# اختبار 7: فحص حجم الملف
Test-CodeFix -TestName "حجم ملف BugBountyCore.js" -Description "فحص أن الملف ليس فارغ أو تالف" -TestScript {
    $fileInfo = Get-Item "assets\modules\bugbounty\BugBountyCore.js"
    $sizeKB = [math]::Round($fileInfo.Length / 1024, 2)
    
    Write-Host "      حجم الملف: $sizeKB KB" -ForegroundColor White
    
    # يجب أن يكون الملف أكبر من 1 MB
    return ($fileInfo.Length -gt 1048576)
}

Write-Host ""
Write-Host "📋 المرحلة 3: فتح اختبار التصدير الفعلي" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Gray

# فتح صفحة الاختبار
try {
    $testPath = Resolve-Path "test_real_export.html"
    Write-Host "🌐 فتح صفحة اختبار التصدير الفعلي..." -ForegroundColor Yellow
    Start-Process $testPath.Path
    Write-Host "✅ تم فتح صفحة الاختبار في المتصفح" -ForegroundColor Green
    Write-Host "📋 يرجى اتباع التعليمات في الصفحة لاختبار التصدير" -ForegroundColor Cyan
    $global:passedTests++
} catch {
    Write-Host "❌ فشل في فتح صفحة الاختبار: $($_.Exception.Message)" -ForegroundColor Red
    $global:failedTests++
}
$global:totalTests++

Write-Host ""
Write-Host "📋 المرحلة 4: النتائج النهائية" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Gray

Write-Host "📊 إجمالي الاختبارات: $totalTests" -ForegroundColor White
Write-Host "✅ الاختبارات الناجحة: $passedTests" -ForegroundColor Green  
Write-Host "❌ الاختبارات الفاشلة: $failedTests" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "📈 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host ""
if ($failedTests -eq 0) {
    Write-Host "🎉 جميع الاختبارات نجحت! الإصلاحات تعمل بشكل صحيح" -ForegroundColor Green
    Write-Host "✅ مشكلة Maximum call stack size exceeded تم حلها" -ForegroundColor Green
    Write-Host "🚀 النظام جاهز للاستخدام الفعلي" -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host "⚠️ معظم الاختبارات نجحت، لكن هناك بعض المشاكل البسيطة" -ForegroundColor Yellow
    Write-Host "🔧 يُنصح بمراجعة الاختبارات الفاشلة" -ForegroundColor Yellow
} else {
    Write-Host "🚨 هناك مشاكل كبيرة تحتاج إصلاح فوري" -ForegroundColor Red
    Write-Host "❌ الإصلاحات لم تعمل بشكل صحيح" -ForegroundColor Red
}

Write-Host ""
Write-Host "📋 التعليمات التالية:" -ForegroundColor Cyan
Write-Host "1. تحقق من صفحة الاختبار المفتوحة في المتصفح" -ForegroundColor White
Write-Host "2. اضغط على 'بدء اختبار التصدير الكامل' أو 'اختبار generatePageHTMLReport فقط'" -ForegroundColor White
Write-Host "3. راقب النتائج والأخطاء" -ForegroundColor White
Write-Host "4. إذا ظهرت رسالة 'Maximum call stack size exceeded' فالإصلاحات لم تعمل" -ForegroundColor White
Write-Host "5. إذا تم التصدير بنجاح فالإصلاحات تعمل بشكل صحيح" -ForegroundColor White

Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
