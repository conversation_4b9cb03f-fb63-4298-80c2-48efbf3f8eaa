<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار التقارير المُصلحة v4.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        .report-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار التقارير المُصلحة v4.0</h1>
        <p>اختبار أن التقارير تستخدم الآن التفاصيل الشاملة التفصيلية الحقيقية</p>
        
        <button class="btn" onclick="testFixedReports()">🚀 اختبار التقارير المُصلحة</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
        
        <div id="reportPreview" class="report-preview" style="display: none;">
            <h3>📋 معاينة التقرير:</h3>
            <div id="previewContent"></div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('reportPreview').style.display = 'none';
        }
        
        function showReportPreview(reportContent) {
            const previewDiv = document.getElementById('reportPreview');
            const contentDiv = document.getElementById('previewContent');
            
            // استخراج جزء من التقرير للمعاينة
            const preview = reportContent.substring(0, 2000) + (reportContent.length > 2000 ? '...' : '');
            contentDiv.innerHTML = preview;
            previewDiv.style.display = 'block';
        }
        
        async function testFixedReports() {
            clearResults();
            log('🚀 بدء اختبار التقارير المُصلحة v4.0', 'info');
            
            try {
                // تهيئة النظام
                log('🔄 تهيئة النظام...', 'info');
                const bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام بنجاح', 'success');
                
                // إنشاء بيانات اختبار
                const testVuln = {
                    name: 'API Authentication Bypass',
                    type: 'Authentication Bypass',
                    severity: 'High',
                    url: 'http://example.com/api/test',
                    payload: 'bypass_token=admin',
                    evidence: 'Authentication bypassed successfully',
                    description: 'Critical authentication bypass vulnerability',
                    location: 'http://example.com/api/auth'
                };
                
                log('🔥 اختبار التقرير المنفصل المُصلح...', 'info');
                
                // اختبار التقرير المنفصل
                const testPageData = {
                    page_name: 'API Test Page',
                    page_url: 'http://example.com/api',
                    vulnerabilities: [testVuln]
                };
                
                const separateReport = await bugBountyCore.generatePageHTMLReport(testPageData, 'http://example.com/api', 1);
                
                if (separateReport && separateReport.length > 1000) {
                    log(`✅ التقرير المنفصل تم إنشاؤه (${(separateReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        separateReport.includes('الوصف الشامل التفصيلي') ||
                        separateReport.includes('خطوات الاستغلال الشاملة التفصيلية') ||
                        separateReport.includes('تحليل التأثير الشامل التفصيلي') ||
                        separateReport.includes('النظام الشامل التفصيلي v4.0');
                    
                    const hasGenericContent = 
                        separateReport.includes('حدث خطأ في تحميل التفاصيل الشاملة') ||
                        separateReport.includes('تم اكتشاف ثغرة أمنية') ||
                        separateReport.includes('قد يؤثر على أمان الموقع');
                    
                    if (hasComprehensiveContent && !hasGenericContent) {
                        log('🎉 التقرير المنفصل يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو رسائل خطأ', 'success');
                    } else if (hasGenericContent) {
                        log('❌ التقرير المنفصل لا يزال يحتوي على محتوى عام أو رسائل خطأ', 'error');
                    } else {
                        log('⚠️ التقرير المنفصل قد لا يحتوي على المحتوى الشامل التفصيلي', 'warning');
                    }
                    
                    showReportPreview(separateReport);
                    
                } else {
                    log('❌ فشل في إنشاء التقرير المنفصل', 'error');
                }
                
                log('🔥 اختبار التقرير الرئيسي المُصلح...', 'info');
                
                // اختبار التقرير الرئيسي
                const testAnalysis = {
                    vulnerabilities: [testVuln],
                    summary: {
                        total_vulnerabilities: 1,
                        high_severity: 1
                    }
                };
                
                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(testAnalysis, [], 'http://example.com');
                
                if (mainReport && mainReport.length > 1000) {
                    log(`✅ التقرير الرئيسي تم إنشاؤه (${(mainReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        mainReport.includes('الوصف الشامل التفصيلي v4.0') ||
                        mainReport.includes('خطوات الاستغلال الشاملة التفصيلية') ||
                        mainReport.includes('تحليل التأثير الشامل التفصيلي') ||
                        mainReport.includes('النظام الشامل التفصيلي v4.0');
                    
                    const hasGenericContent = 
                        mainReport.includes('ثغرة مكتشفة تحتاج مراجعة') ||
                        mainReport.includes('تم اكتشاف ثغرة أمنية') ||
                        mainReport.includes('قد يؤثر على أمان الموقع');
                    
                    if (hasComprehensiveContent && !hasGenericContent) {
                        log('🎉 التقرير الرئيسي يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                    } else if (hasGenericContent) {
                        log('❌ التقرير الرئيسي لا يزال يحتوي على محتوى عام', 'error');
                    } else {
                        log('⚠️ التقرير الرئيسي قد لا يحتوي على المحتوى الشامل التفصيلي', 'warning');
                    }
                    
                } else {
                    log('❌ فشل في إنشاء التقرير الرئيسي', 'error');
                }
                
                log('🏁 انتهى اختبار التقارير المُصلحة', 'info');
                log('🔥 النظام الشامل التفصيلي v4.0 مُطبق بنجاح!', 'success');
                
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testFixedReports, 1000);
        });
    </script>
</body>
</html>
