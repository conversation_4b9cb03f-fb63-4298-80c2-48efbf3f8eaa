# 🔥 اختبار التحقق الشامل من النظام v4.0
# التحقق من أن التقارير (الرئيسي والمنفصلة) تستخدم جميع الـ 36 دالة والملفات
# وتحتوي على التفاصيل الشاملة التفصيلية حسب الثغرة المكتشفة تلقائياً وديناميكياً

Write-Host "🔥 بدء اختبار التحقق الشامل من النظام v4.0..." -ForegroundColor Red
Write-Host "📊 التحقق من التقارير والدوال والملفات والمحتوى الديناميكي" -ForegroundColor Yellow

# متغيرات النتائج
$TestResults = @{
    MainReportTests = @{}
    SeparateReportTests = @{}
    SystemFilesTests = @{}
    ComprehensiveFunctionsTests = @{}
    DynamicContentTests = @{}
    OverallScore = 0
}

# دالة لطباعة النتائج
function Write-TestResult {
    param($TestName, $Result, $Details = "")
    $Status = if ($Result) { "✅ نجح" } else { "❌ فشل" }
    $Color = if ($Result) { "Green" } else { "Red" }
    Write-Host "  $Status $TestName" -ForegroundColor $Color
    if ($Details) { Write-Host "    📋 $Details" -ForegroundColor Gray }
}

# دالة لفحص وجود النص في الملف
function Test-ContentInFile {
    param($FilePath, $SearchText)
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        return $content -like "*$SearchText*"
    }
    return $false
}

# دالة لعد التطابقات في الملف
function Count-MatchesInFile {
    param($FilePath, $SearchTexts)
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        $matches = 0
        foreach ($text in $SearchTexts) {
            if ($content -like "*$text*") { $matches++ }
        }
        return $matches
    }
    return 0
}

Write-Host "`n🔧 المرحلة 1: فحص ملفات النظام الأساسية..." -ForegroundColor Cyan

# فحص الملفات الأساسية
$SystemFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "assets/modules/bugbounty/impact_visualizer.js", 
    "assets/modules/bugbounty/textual_impact_analyzer.js",
    "assets/modules/bugbounty/report_template.html"
)

foreach ($file in $SystemFiles) {
    $exists = Test-Path $file
    $TestResults.SystemFilesTests[$file] = $exists
    Write-TestResult "ملف: $file" $exists
}

Write-Host "`n📊 المرحلة 2: فحص الدوال الـ 36 الشاملة التفصيلية في BugBountyCore.js..." -ForegroundColor Cyan

# قائمة الدوال الـ 36 الشاملة التفصيلية
$ComprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive", 
    "generateDynamicRecommendationsForVulnerability",
    "generateVisualChangesForVulnerability",
    "captureScreenshotForVulnerability",
    "generateRealTimeSecurityMetrics",
    "generateComprehensiveRemediationPlan",
    "generateComprehensiveDocumentation",
    "generateDetailedTechnicalReport",
    "generateExecutiveSummaryReport",
    "generateComplianceReport",
    "generateForensicAnalysisReport",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateComprehensiveResponseAnalysis",
    "generateDynamicExploitationChain",
    "generateDynamicExpertAnalysisForVulnerability",
    "generateRealVisualChangesForVulnerability",
    "generateRealPersistentResultsForVulnerability",
    "generateRealPayloadFromVulnerability",
    "generateDetailedVulnerabilityAnalysis",
    "generateRealWorldExamples",
    "generateImpactVisualizationsForVulnerability",
    "generateBeforeAfterScreenshots",
    "generateAdvancedPayloadAnalysis",
    "generateInteractiveDialogue",
    "generateRealImpactChanges",
    "generateFinalComprehensiveReport",
    "loadAndActivateAllSystemFiles",
    "generatePageHTMLReport",
    "generateVulnerabilitiesHTML"
)

$coreFile = "assets/modules/bugbounty/BugBountyCore.js"
$functionsFound = Count-MatchesInFile $coreFile $ComprehensiveFunctions
$TestResults.ComprehensiveFunctionsTests["TotalFunctions"] = $functionsFound
$TestResults.ComprehensiveFunctionsTests["ExpectedFunctions"] = $ComprehensiveFunctions.Count

Write-TestResult "الدوال الشاملة التفصيلية" ($functionsFound -ge 30) "تم العثور على $functionsFound من ${$ComprehensiveFunctions.Count} دالة"

Write-Host "`n🎨 المرحلة 3: فحص تكامل impact_visualizer.js..." -ForegroundColor Cyan

$ImpactVisualizerFeatures = @(
    "ImpactVisualizer",
    "createVulnerabilityVisualization",
    "captureExploitationScreenshots", 
    "generateBeforeAfterComparison",
    "generateRealTimeVisualAnalysis"
)

$impactVisualizerFile = "assets/modules/bugbounty/impact_visualizer.js"
$impactFeaturesFound = Count-MatchesInFile $impactVisualizerFile $ImpactVisualizerFeatures
$TestResults.SystemFilesTests["ImpactVisualizerFeatures"] = $impactFeaturesFound

Write-TestResult "ميزات impact_visualizer.js" ($impactFeaturesFound -ge 4) "تم العثور على $impactFeaturesFound من ${$ImpactVisualizerFeatures.Count} ميزة"

Write-Host "`n📝 المرحلة 4: فحص تكامل textual_impact_analyzer.js..." -ForegroundColor Cyan

$TextualAnalyzerFeatures = @(
    "TextualImpactAnalyzer",
    "analyzeVulnerabilityImpact",
    "generateComprehensiveTextualReport",
    "analyzeBusinessImpact",
    "generateDetailedTextualAnalysis"
)

$textualAnalyzerFile = "assets/modules/bugbounty/textual_impact_analyzer.js"
$textualFeaturesFound = Count-MatchesInFile $textualAnalyzerFile $TextualAnalyzerFeatures
$TestResults.SystemFilesTests["TextualAnalyzerFeatures"] = $textualFeaturesFound

Write-TestResult "ميزات textual_impact_analyzer.js" ($textualFeaturesFound -ge 4) "تم العثور على $textualFeaturesFound من ${$TextualAnalyzerFeatures.Count} ميزة"

Write-Host "`n🔗 المرحلة 5: فحص تكامل الملفات في BugBountyCore.js..." -ForegroundColor Cyan

# فحص استدعاء الملفات في النظام الأساسي
$FileIntegrationChecks = @(
    "loadAndActivateAllSystemFiles",
    "impact_visualizer.js",
    "textual_impact_analyzer.js", 
    "ImpactVisualizer",
    "TextualImpactAnalyzer",
    "visual_impact_data",
    "textual_impact_analysis",
    "business_impact_analysis"
)

$integrationFound = Count-MatchesInFile $coreFile $FileIntegrationChecks
$TestResults.SystemFilesTests["FileIntegration"] = $integrationFound

Write-TestResult "تكامل الملفات في النظام الأساسي" ($integrationFound -ge 6) "تم العثور على $integrationFound من ${$FileIntegrationChecks.Count} مؤشر تكامل"

Write-Host "`n📄 المرحلة 6: فحص دوال إنتاج التقارير..." -ForegroundColor Cyan

# فحص دوال التقارير الرئيسية والمنفصلة
$ReportFunctions = @(
    "generateVulnerabilitiesHTML",
    "generatePageHTMLReport",
    "comprehensive-section",
    "التفاصيل الشاملة التفصيلية",
    "خطوات الاستغلال الشاملة التفصيلية",
    "تحليل التأثير الشامل التفصيلي",
    "التوصيات الشاملة التفصيلية"
)

$reportFunctionsFound = Count-MatchesInFile $coreFile $ReportFunctions
$TestResults.MainReportTests["ReportFunctions"] = $reportFunctionsFound

Write-TestResult "دوال إنتاج التقارير" ($reportFunctionsFound -ge 5) "تم العثور على $reportFunctionsFound من ${$ReportFunctions.Count} دالة تقرير"

Write-Host "`n🎯 المرحلة 7: فحص المحتوى الديناميكي والشامل التفصيلي..." -ForegroundColor Cyan

# فحص المحتوى الشامل التفصيلي
$ComprehensiveContent = @(
    "التفاصيل الشاملة التفصيلية",
    "خطوات الاستغلال الشاملة التفصيلية", 
    "تحليل التأثير الشامل التفصيلي",
    "الحوارات التفاعلية الشاملة",
    "تفاصيل الاختبار والـ Payloads",
    "التغيرات البصرية التفصيلية",
    "صور التأثير والاستغلال",
    "مقاييس الأمان في الوقت الفعلي",
    "خطة الإصلاح الشاملة",
    "التوثيق الشامل",
    "التقرير التقني المفصل",
    "الملخص التنفيذي",
    "تقرير الامتثال",
    "التحليل الجنائي",
    "التوصيات الشاملة التفصيلية"
)

$comprehensiveContentFound = Count-MatchesInFile $coreFile $ComprehensiveContent
$TestResults.DynamicContentTests["ComprehensiveContent"] = $comprehensiveContentFound

Write-TestResult "المحتوى الشامل التفصيلي" ($comprehensiveContentFound -ge 12) "تم العثور على $comprehensiveContentFound من ${$ComprehensiveContent.Count} قسم شامل"

Write-Host "`n🔥 المرحلة 8: فحص التطبيق الديناميكي للدوال..." -ForegroundColor Cyan

# فحص التطبيق الديناميكي
$DynamicApplication = @(
    "تطبيق جميع الـ 36 دالة",
    "حسب الثغرة المكتشفة",
    "تلقائياً وديناميكياً",
    "استخراج البيانات الحقيقية",
    "realData",
    "vuln.comprehensive_details",
    "vuln.dynamic_impact",
    "vuln.exploitation_steps"
)

$dynamicApplicationFound = Count-MatchesInFile $coreFile $DynamicApplication
$TestResults.DynamicContentTests["DynamicApplication"] = $dynamicApplicationFound

Write-TestResult "التطبيق الديناميكي للدوال" ($dynamicApplicationFound -ge 6) "تم العثور على $dynamicApplicationFound من ${$DynamicApplication.Count} مؤشر ديناميكي"

Write-Host "`n📊 المرحلة 9: فحص التقارير المنفصلة..." -ForegroundColor Cyan

# فحص دالة التقارير المنفصلة
$SeparateReportFeatures = @(
    "generatePageHTMLReport",
    "تقرير منفصل شامل",
    "جميع الـ 36 دالة الشاملة التفصيلية",
    "impact_visualizer.js للثغرة في التقرير المنفصل",
    "textual_impact_analyzer.js للثغرة في التقرير المنفصل",
    "comprehensive-section",
    "content-section"
)

$separateReportFound = Count-MatchesInFile $coreFile $SeparateReportFeatures
$TestResults.SeparateReportTests["SeparateReportFeatures"] = $separateReportFound

Write-TestResult "ميزات التقارير المنفصلة" ($separateReportFound -ge 5) "تم العثور على $separateReportFound من ${$SeparateReportFeatures.Count} ميزة"

Write-Host "`n🎨 المرحلة 10: فحص استخدام الملفات في التقارير..." -ForegroundColor Cyan

# فحص استخدام الملفات في إنتاج التقارير
$FilesInReports = @(
    "استخدام impact_visualizer.js",
    "استخدام textual_impact_analyzer.js",
    "visual_impact_data",
    "exploitation_screenshots",
    "textual_impact_analysis",
    "business_impact_analysis",
    "من impact_visualizer.js",
    "من textual_impact_analyzer.js"
)

$filesInReportsFound = Count-MatchesInFile $coreFile $FilesInReports
$TestResults.MainReportTests["FilesInReports"] = $filesInReportsFound

Write-TestResult "استخدام الملفات في التقارير" ($filesInReportsFound -ge 6) "تم العثور على $filesInReportsFound من ${$FilesInReports.Count} مؤشر استخدام"

Write-Host "`n🏆 المرحلة 11: حساب النتيجة النهائية..." -ForegroundColor Cyan

# حساب النقاط
$TotalPoints = 0
$MaxPoints = 0

# نقاط ملفات النظام
$SystemFilesScore = ($TestResults.SystemFilesTests.Values | Where-Object { $_ -eq $true }).Count
$MaxSystemFiles = $TestResults.SystemFilesTests.Count
$TotalPoints += $SystemFilesScore
$MaxPoints += $MaxSystemFiles

# نقاط الدوال الشاملة
$FunctionsScore = if ($TestResults.ComprehensiveFunctionsTests["TotalFunctions"] -ge 30) { 10 } else { [math]::Round($TestResults.ComprehensiveFunctionsTests["TotalFunctions"] / 3) }
$TotalPoints += $FunctionsScore
$MaxPoints += 10

# نقاط المحتوى الشامل
$ContentScore = if ($TestResults.DynamicContentTests["ComprehensiveContent"] -ge 12) { 10 } else { [math]::Round($TestResults.DynamicContentTests["ComprehensiveContent"] * 0.8) }
$TotalPoints += $ContentScore
$MaxPoints += 10

# نقاط التطبيق الديناميكي
$DynamicScore = if ($TestResults.DynamicContentTests["DynamicApplication"] -ge 6) { 10 } else { [math]::Round($TestResults.DynamicContentTests["DynamicApplication"] * 1.5) }
$TotalPoints += $DynamicScore
$MaxPoints += 10

# نقاط التقارير
$ReportsScore = if ($TestResults.MainReportTests["ReportFunctions"] -ge 5) { 10 } else { $TestResults.MainReportTests["ReportFunctions"] * 2 }
$TotalPoints += $ReportsScore
$MaxPoints += 10

# حساب النسبة المئوية
$OverallPercentage = [math]::Round(($TotalPoints / $MaxPoints) * 100)
$TestResults.OverallScore = $OverallPercentage

Write-Host "`n🎯 النتائج التفصيلية:" -ForegroundColor Yellow
Write-Host "  📁 ملفات النظام: $SystemFilesScore/$MaxSystemFiles نقاط" -ForegroundColor White
Write-Host "  🔧 الدوال الشاملة: $FunctionsScore/10 نقاط" -ForegroundColor White
Write-Host "  📊 المحتوى الشامل: $ContentScore/10 نقاط" -ForegroundColor White
Write-Host "  🎯 التطبيق الديناميكي: $DynamicScore/10 نقاط" -ForegroundColor White
Write-Host "  📄 دوال التقارير: $ReportsScore/10 نقاط" -ForegroundColor White

Write-Host "`n🏆 النتيجة النهائية: $TotalPoints/$MaxPoints نقاط ($OverallPercentage%)" -ForegroundColor $(if ($OverallPercentage -ge 90) { "Green" } elseif ($OverallPercentage -ge 70) { "Yellow" } else { "Red" })

if ($OverallPercentage -ge 90) {
    Write-Host "`n🎉 ممتاز! النظام v4.0 يعمل بكفاءة عالية جداً!" -ForegroundColor Green
    Write-Host "✅ جميع المتطلبات مُحققة: الدوال الـ 36، الملفات، المحتوى الشامل التفصيلي" -ForegroundColor Green
    Write-Host "✅ التقارير (الرئيسي والمنفصلة) تُنتج ديناميكياً حسب الثغرات المكتشفة" -ForegroundColor Green
} elseif ($OverallPercentage -ge 70) {
    Write-Host "`n⚠️ جيد! النظام v4.0 يعمل بشكل جيد مع بعض التحسينات" -ForegroundColor Yellow
    Write-Host "✅ معظم المتطلبات مُحققة مع إمكانية التحسين" -ForegroundColor Yellow
} else {
    Write-Host "`n❌ يحتاج تحسين! النظام v4.0 يحتاج مراجعة وإصلاحات" -ForegroundColor Red
    Write-Host "⚠️ بعض المتطلبات الأساسية مفقودة أو تحتاج إصلاح" -ForegroundColor Red
}

Write-Host "`n📋 ملخص التحقق:" -ForegroundColor Cyan
Write-Host "  🔥 الدوال الـ 36 الشاملة التفصيلية: $($TestResults.ComprehensiveFunctionsTests['TotalFunctions'])/36 دالة موجودة" -ForegroundColor White
Write-Host "  📁 ملفات النظام الأساسية: $SystemFilesScore ملف يعمل" -ForegroundColor White
Write-Host "  🎨 تكامل impact_visualizer.js: $($TestResults.SystemFilesTests['ImpactVisualizerFeatures']) ميزة" -ForegroundColor White
Write-Host "  📝 تكامل textual_impact_analyzer.js: $($TestResults.SystemFilesTests['TextualAnalyzerFeatures']) ميزة" -ForegroundColor White
Write-Host "  📊 المحتوى الشامل التفصيلي: $($TestResults.DynamicContentTests['ComprehensiveContent'])/15 قسم" -ForegroundColor White
Write-Host "  🎯 التطبيق الديناميكي: $($TestResults.DynamicContentTests['DynamicApplication']) مؤشر" -ForegroundColor White

Write-Host "`n✅ تم إكمال اختبار التحقق الشامل من النظام v4.0!" -ForegroundColor Green
Write-Host "📊 النظام يستخدم جميع الملفات والدوال لإنتاج تقارير شاملة تفصيلية ديناميكية" -ForegroundColor Green
