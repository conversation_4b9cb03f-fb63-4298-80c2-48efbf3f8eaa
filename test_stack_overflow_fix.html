<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار إصلاح مشكلة Stack Overflow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
    </style>
</head>
<body>
    <h1>🔧 اختبار إصلاح مشكلة Maximum call stack size exceeded</h1>
    
    <div class="info">
        <h3>المشاكل التي تم إصلاحها:</h3>
        <ul>
            <li>✅ إزالة استدعاء generateComprehensiveDetailsFromRealData بدون await في السطر 11724</li>
            <li>✅ إزالة استدعاء generatePageHTMLReport داخل generateVulnerabilitiesHTML في السطر 11304</li>
            <li>✅ إزالة استدعاء generateFinalComprehensiveReport داخل generatePageHTMLReport في السطر 45730</li>
            <li>✅ إزالة استدعاء generateFinalComprehensiveReport داخل generateVulnerabilitiesHTML في السطر 11273</li>
            <li>✅ إضافة await للاستدعاء في السطر 25074</li>
        </ul>
    </div>

    <button onclick="testBasicFunctionality()">اختبار الوظائف الأساسية</button>
    <button onclick="testReportGeneration()">اختبار إنشاء التقارير</button>
    <button onclick="testVulnerabilityProcessing()">اختبار معالجة الثغرات</button>

    <div id="results"></div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        
        function addResult(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function testBasicFunctionality() {
            addResult('🔄 بدء اختبار الوظائف الأساسية...', 'info');
            
            try {
                // تهيئة النظام
                bugBountyCore = new BugBountyCore();
                addResult('✅ تم إنشاء مثيل BugBountyCore بنجاح', 'success');
                
                // اختبار دالة تنظيف الأسماء
                const testName = bugBountyCore.cleanVulnerabilityName('SQL Injection SQL Injection');
                addResult(`✅ اختبار تنظيف الأسماء: ${testName}`, 'success');
                
                // اختبار استخراج البيانات
                const testVuln = { name: 'Test XSS', type: 'XSS', payload: 'test' };
                const extractedData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
                addResult(`✅ اختبار استخراج البيانات: ${Object.keys(extractedData).length} خاصية`, 'success');
                
            } catch (error) {
                addResult(`❌ خطأ في الوظائف الأساسية: ${error.message}`, 'error');
                console.error('خطأ في الاختبار الأساسي:', error);
            }
        }

        async function testReportGeneration() {
            addResult('🔄 بدء اختبار إنشاء التقارير...', 'info');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                }
                
                // إنشاء ثغرة اختبار
                const testVuln = {
                    name: 'Test SQL Injection',
                    type: 'SQL Injection',
                    severity: 'High',
                    payload: "' OR 1=1 --",
                    url: 'https://example.com/test',
                    parameter: 'id'
                };
                
                // اختبار إنشاء التفاصيل الشاملة
                addResult('🔄 اختبار generateComprehensiveDetailsFromRealData...', 'info');
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, {});
                
                if (comprehensiveDetails && comprehensiveDetails.technical_details) {
                    addResult('✅ تم إنشاء التفاصيل الشاملة بنجاح', 'success');
                } else {
                    addResult('⚠️ التفاصيل الشاملة فارغة أو غير مكتملة', 'error');
                }
                
                // اختبار إنشاء التأثير الديناميكي
                addResult('🔄 اختبار generateDynamicImpactForAnyVulnerability...', 'info');
                const dynamicImpact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, {});
                
                if (dynamicImpact && dynamicImpact.length > 50) {
                    addResult('✅ تم إنشاء التأثير الديناميكي بنجاح', 'success');
                } else {
                    addResult('⚠️ التأثير الديناميكي قصير أو فارغ', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في إنشاء التقارير: ${error.message}`, 'error');
                console.error('خطأ في اختبار التقارير:', error);
            }
        }

        async function testVulnerabilityProcessing() {
            addResult('🔄 بدء اختبار معالجة الثغرات...', 'info');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                }
                
                // إنشاء مجموعة ثغرات اختبار
                const testVulns = [
                    { name: 'XSS Test', type: 'XSS', severity: 'Medium' },
                    { name: 'SQL Test', type: 'SQL Injection', severity: 'High' }
                ];
                
                // اختبار معالجة HTML للثغرات
                addResult('🔄 اختبار generateVulnerabilitiesHTML...', 'info');
                const htmlResult = await bugBountyCore.generateVulnerabilitiesHTML(testVulns);
                
                if (htmlResult && htmlResult.length > 100) {
                    addResult(`✅ تم إنشاء HTML للثغرات بنجاح (${htmlResult.length} حرف)`, 'success');
                } else {
                    addResult('⚠️ HTML الثغرات قصير أو فارغ', 'error');
                }
                
                // اختبار إنشاء تقرير صفحة
                addResult('🔄 اختبار generatePageHTMLReport...', 'info');
                const pageReport = await bugBountyCore.generatePageHTMLReport(
                    { vulnerabilities: testVulns }, 
                    'https://example.com', 
                    1
                );
                
                if (pageReport && pageReport.length > 100) {
                    addResult(`✅ تم إنشاء تقرير الصفحة بنجاح (${pageReport.length} حرف)`, 'success');
                } else {
                    addResult('⚠️ تقرير الصفحة قصير أو فارغ', 'error');
                }
                
            } catch (error) {
                addResult(`❌ خطأ في معالجة الثغرات: ${error.message}`, 'error');
                console.error('خطأ في اختبار معالجة الثغرات:', error);
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة الاختبار بنجاح', 'success');
            addResult('📋 جاهز لتشغيل الاختبارات', 'info');
        };
    </script>
</body>
</html>
