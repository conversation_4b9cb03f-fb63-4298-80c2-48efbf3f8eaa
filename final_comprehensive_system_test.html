<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 اختبار النظام الشامل التفصيلي v4.0 النهائي</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 اختبار النظام الشامل التفصيلي v4.0 النهائي</h1>
            <p>التحقق من أن جميع التقارير تستخدم الدوال الشاملة التفصيلية تلقائياً وديناميكياً</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📊 حالة الاختبار</h3>
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <p id="status">جاهز للبدء...</p>
            </div>

            <div class="test-section">
                <h3>🎯 الاختبارات الشاملة</h3>
                <button class="btn" onclick="runComprehensiveSystemTest()">🚀 تشغيل الاختبار الشامل</button>
                <button class="btn" onclick="testMainReport()">📊 اختبار التقرير الرئيسي</button>
                <button class="btn" onclick="testSeparateReports()">📋 اختبار التقارير المنفصلة</button>
                <button class="btn" onclick="testComprehensiveFunctions()">🔧 اختبار الدوال الشاملة</button>
            </div>

            <div class="stats" id="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div>إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div>الاختبارات الناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div>معدل النجاح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="functionsVerified">0</div>
                    <div>الدوال المتحققة</div>
                </div>
            </div>

            <div class="test-section">
                <h3>📋 نتائج الاختبار</h3>
                <div id="results" class="result"></div>
            </div>

            <div class="test-section">
                <h3>🔍 تفاصيل التنفيذ</h3>
                <div id="executionDetails" class="result"></div>
            </div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = [];
        let executionLog = [];
        let totalTests = 0;
        let passedTests = 0;
        let functionsVerified = 0;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            executionLog.push(logEntry);
            
            const detailsDiv = document.getElementById('executionDetails');
            detailsDiv.textContent = executionLog.join('\n');
            detailsDiv.scrollTop = detailsDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('successRate').textContent = totalTests > 0 ? Math.round(passedTests / totalTests * 100) + '%' : '0%';
            document.getElementById('functionsVerified').textContent = functionsVerified;
        }

        function displayResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = testResults.join('\n');
        }

        async function initializeSystem() {
            try {
                log('✅ تم تهيئة BugBountyCore بنجاح');
                bugBountyCore = new BugBountyCore();
                return true;
            } catch (error) {
                log(`❌ فشل في تهيئة BugBountyCore: ${error.message}`, 'error');
                return false;
            }
        }

        async function testMainReport() {
            log('📊 بدء اختبار التقرير الرئيسي...');
            totalTests++;
            
            try {
                // إنشاء بيانات اختبار للتقرير الرئيسي
                const testAnalysis = {
                    vulnerabilities: [
                        {
                            name: 'Main Report XSS Test',
                            type: 'XSS',
                            severity: 'High',
                            url: 'http://example.com/main',
                            payload: '<script>alert("main")</script>',
                            evidence: 'Main report test evidence'
                        }
                    ],
                    summary: {
                        total_vulnerabilities: 1,
                        high_severity: 1
                    }
                };

                log('📝 اختبار generateFinalComprehensiveReport...');
                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(
                    testAnalysis, 
                    [], 
                    'http://example.com'
                );

                if (mainReport && typeof mainReport === 'string' && mainReport.length > 100) {
                    // فحص استخدام الدوال الشاملة التفصيلية
                    if (mainReport.includes('generateComprehensiveDetailsFromRealData') || 
                        mainReport.includes('التفاصيل الشاملة') ||
                        mainReport.includes('البيانات الحقيقية')) {
                        log('✅ التقرير الرئيسي يستخدم النظام الشامل التفصيلي');
                        testResults.push('✅ التقرير الرئيسي يستخدم النظام الشامل التفصيلي');
                        passedTests++;
                        functionsVerified++;
                        return true;
                    } else {
                        log('⚠️ التقرير الرئيسي قد لا يستخدم النظام الشامل التفصيلي بالكامل');
                        testResults.push('⚠️ التقرير الرئيسي قد لا يستخدم النظام الشامل التفصيلي بالكامل');
                        return false;
                    }
                } else {
                    log('❌ التقرير الرئيسي غير صالح أو فارغ');
                    testResults.push('❌ التقرير الرئيسي غير صالح أو فارغ');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
                testResults.push(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`);
                return false;
            }
        }

        async function testSeparateReports() {
            log('📋 بدء اختبار التقارير المنفصلة...');
            totalTests++;
            
            try {
                // إنشاء بيانات اختبار للتقرير المنفصل
                const testPageData = {
                    page_name: 'Separate Report Test Page',
                    page_url: 'http://example.com/separate',
                    vulnerabilities: [
                        {
                            name: 'Separate Report SQL Test',
                            type: 'SQL Injection',
                            severity: 'Critical',
                            url: 'http://example.com/separate',
                            payload: "' OR '1'='1' --",
                            evidence: 'Separate report test evidence'
                        }
                    ]
                };

                log('📝 اختبار formatSinglePageReport...');
                const separateReport = await bugBountyCore.formatSinglePageReport(testPageData);

                if (separateReport && typeof separateReport === 'string' && separateReport.length > 100) {
                    // فحص استخدام الدوال الشاملة التفصيلية
                    if (separateReport.includes('formatComprehensiveVulnerabilitySection') || 
                        separateReport.includes('التفاصيل الشاملة') ||
                        separateReport.includes('البيانات الحقيقية')) {
                        log('✅ التقارير المنفصلة تستخدم النظام الشامل التفصيلي');
                        testResults.push('✅ التقارير المنفصلة تستخدم النظام الشامل التفصيلي');
                        passedTests++;
                        functionsVerified++;
                        return true;
                    } else {
                        log('⚠️ التقارير المنفصلة قد لا تستخدم النظام الشامل التفصيلي بالكامل');
                        testResults.push('⚠️ التقارير المنفصلة قد لا تستخدم النظام الشامل التفصيلي بالكامل');
                        return false;
                    }
                } else {
                    log('❌ التقرير المنفصل غير صالح أو فارغ');
                    testResults.push('❌ التقرير المنفصل غير صالح أو فارغ');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقارير المنفصلة: ${error.message}`, 'error');
                testResults.push(`❌ خطأ في اختبار التقارير المنفصلة: ${error.message}`);
                return false;
            }
        }

        async function testComprehensiveFunctions() {
            log('🔧 بدء اختبار الدوال الشاملة التفصيلية...');
            
            const comprehensiveFunctions = [
                'generateComprehensiveDetailsFromRealData',
                'extractRealDataFromDiscoveredVulnerability',
                'formatComprehensiveVulnerabilitySection',
                'generateDynamicImpactForAnyVulnerability',
                'generateRealExploitationStepsForVulnerabilityComprehensive',
                'generateDynamicRecommendationsForVulnerability',
                'generateRealVisualChangesForVulnerability',
                'generateRealPersistentResultsForVulnerability'
            ];

            let functionsFound = 0;
            
            for (const funcName of comprehensiveFunctions) {
                totalTests++;
                if (typeof bugBountyCore[funcName] === 'function') {
                    log(`✅ الدالة ${funcName} موجودة وجاهزة`);
                    functionsFound++;
                    passedTests++;
                    functionsVerified++;
                } else {
                    log(`❌ الدالة ${funcName} غير موجودة`);
                    testResults.push(`❌ الدالة ${funcName} غير موجودة`);
                }
            }

            const functionSuccessRate = (functionsFound / comprehensiveFunctions.length * 100).toFixed(1);
            log(`📊 نتائج اختبار الدوال: ${functionsFound}/${comprehensiveFunctions.length} (${functionSuccessRate}%)`);
            
            if (functionsFound === comprehensiveFunctions.length) {
                testResults.push('✅ جميع الدوال الشاملة التفصيلية موجودة وجاهزة');
                return true;
            } else {
                testResults.push(`⚠️ ${functionsFound}/${comprehensiveFunctions.length} دالة شاملة متوفرة`);
                return false;
            }
        }

        async function runComprehensiveSystemTest() {
            testResults = [];
            executionLog = [];
            totalTests = 0;
            passedTests = 0;
            functionsVerified = 0;
            
            updateStatus('جاري تشغيل الاختبار الشامل...');
            updateProgress(0);

            log('🚀 بدء الاختبار الشامل للنظام v4.0');

            // تهيئة النظام
            updateProgress(10);
            const initSuccess = await initializeSystem();
            if (!initSuccess) {
                updateStatus('❌ فشل في التهيئة');
                updateStats();
                displayResults();
                return;
            }

            // اختبار الدوال الشاملة
            updateProgress(30);
            const functionsTest = await testComprehensiveFunctions();
            
            // اختبار التقرير الرئيسي
            updateProgress(60);
            const mainReportTest = await testMainReport();
            
            // اختبار التقارير المنفصلة
            updateProgress(90);
            const separateReportsTest = await testSeparateReports();

            updateProgress(100);

            // حساب النتائج النهائية
            const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

            if (passedTests === totalTests) {
                updateStatus(`🎉 نجح في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`🎉 النظام الشامل التفصيلي v4.0 يعمل بكفاءة 100%`);
                testResults.push(`✅ جميع التقارير تستخدم الدوال الشاملة التفصيلية تلقائياً وديناميكياً`);
                testResults.push(`🔥 لا يوجد محتوى عام أو افتراضي أو يدوي`);
            } else if (passedTests > totalTests * 0.8) {
                updateStatus(`✅ نجح في معظم الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`✅ النظام الشامل التفصيلي v4.0 يعمل بكفاءة عالية`);
            } else {
                updateStatus(`⚠️ نجح في بعض الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`⚠️ النظام الشامل التفصيلي v4.0 يحتاج مراجعة`);
            }

            updateStats();
            displayResults();
            log('🏁 انتهى الاختبار الشامل للنظام v4.0');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runComprehensiveSystemTest, 1000);
        });
    </script>
</body>
</html>
