# Bug Bounty v4.0 System Comprehensive Verification Test
# Verifying that reports use all 36 functions, system files, and comprehensive content

Write-Host "=== Bug Bounty v4.0 System Verification Test ===" -ForegroundColor Red
Write-Host "Testing reports, functions, files, and dynamic content" -ForegroundColor Yellow

$TestResults = @{
    SystemFiles = 0
    Functions36 = 0
    ComprehensiveContent = 0
    DynamicFeatures = 0
    FileIntegration = 0
    TotalScore = 0
}

function Test-FileContent {
    param($FilePath, $SearchTexts)
    if (Test-Path $FilePath) {
        $content = Get-Content $FilePath -Raw -Encoding UTF8
        $matches = 0
        foreach ($text in $SearchTexts) {
            if ($content -like "*$text*") { $matches++ }
        }
        return $matches
    }
    return 0
}

Write-Host "`n1. Testing System Files..." -ForegroundColor Cyan

# Test core system files
$SystemFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "assets/modules/bugbounty/impact_visualizer.js", 
    "assets/modules/bugbounty/textual_impact_analyzer.js",
    "assets/modules/bugbounty/report_template.html"
)

$filesFound = 0
foreach ($file in $SystemFiles) {
    if (Test-Path $file) {
        $filesFound++
        Write-Host "  [OK] $file" -ForegroundColor Green
    } else {
        Write-Host "  [MISSING] $file" -ForegroundColor Red
    }
}
$TestResults.SystemFiles = $filesFound

Write-Host "`n2. Testing 36 Comprehensive Functions..." -ForegroundColor Cyan

# Test comprehensive functions in BugBountyCore.js
$ComprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive", 
    "generateDynamicRecommendationsForVulnerability",
    "generateVisualChangesForVulnerability",
    "captureScreenshotForVulnerability",
    "generateRealTimeSecurityMetrics",
    "generateComprehensiveRemediationPlan",
    "generateComprehensiveDocumentation",
    "generateDetailedTechnicalReport",
    "generateExecutiveSummaryReport",
    "generateComplianceReport",
    "generateForensicAnalysisReport",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateComprehensiveResponseAnalysis",
    "generateDynamicExploitationChain",
    "generateDynamicExpertAnalysisForVulnerability",
    "generateRealVisualChangesForVulnerability",
    "generateRealPersistentResultsForVulnerability",
    "generateRealPayloadFromVulnerability",
    "generateDetailedVulnerabilityAnalysis",
    "generateRealWorldExamples",
    "generateImpactVisualizationsForVulnerability",
    "generateBeforeAfterScreenshots",
    "generateAdvancedPayloadAnalysis",
    "generateInteractiveDialogue",
    "generateRealImpactChanges",
    "generateFinalComprehensiveReport",
    "loadAndActivateAllSystemFiles",
    "generatePageHTMLReport",
    "generateVulnerabilitiesHTML"
)

$coreFile = "assets/modules/bugbounty/BugBountyCore.js"
$functionsFound = Test-FileContent $coreFile $ComprehensiveFunctions
$TestResults.Functions36 = $functionsFound

Write-Host "  Found $functionsFound/36 comprehensive functions" -ForegroundColor $(if ($functionsFound -ge 30) { "Green" } else { "Yellow" })

Write-Host "`n3. Testing File Integration..." -ForegroundColor Cyan

# Test file integration in reports
$FileIntegration = @(
    "loadAndActivateAllSystemFiles",
    "impact_visualizer.js",
    "textual_impact_analyzer.js", 
    "ImpactVisualizer",
    "TextualImpactAnalyzer",
    "visual_impact_data",
    "textual_impact_analysis",
    "business_impact_analysis"
)

$integrationFound = Test-FileContent $coreFile $FileIntegration
$TestResults.FileIntegration = $integrationFound

Write-Host "  Found $integrationFound/8 file integration features" -ForegroundColor $(if ($integrationFound -ge 6) { "Green" } else { "Yellow" })

Write-Host "`n4. Testing Comprehensive Content..." -ForegroundColor Cyan

# Test comprehensive content sections
$ComprehensiveContent = @(
    "comprehensive-section",
    "content-section",
    "vulnerability-header",
    "comprehensive_details",
    "dynamic_impact", 
    "exploitation_steps",
    "dynamic_recommendations",
    "visual_changes",
    "screenshot_data",
    "security_metrics",
    "remediation_plan",
    "comprehensive_documentation",
    "technical_report",
    "executive_summary",
    "compliance_report"
)

$contentFound = Test-FileContent $coreFile $ComprehensiveContent
$TestResults.ComprehensiveContent = $contentFound

Write-Host "  Found $contentFound/15 comprehensive content features" -ForegroundColor $(if ($contentFound -ge 12) { "Green" } else { "Yellow" })

Write-Host "`n5. Testing Dynamic Features..." -ForegroundColor Cyan

# Test dynamic application features
$DynamicFeatures = @(
    "realData",
    "extractRealDataFromDiscoveredVulnerability",
    "vuln.comprehensive_details",
    "vuln.dynamic_impact",
    "vuln.exploitation_steps",
    "vuln.visual_impact_data",
    "vuln.textual_impact_analysis",
    "vuln.business_impact_analysis"
)

$dynamicFound = Test-FileContent $coreFile $DynamicFeatures
$TestResults.DynamicFeatures = $dynamicFound

Write-Host "  Found $dynamicFound/8 dynamic features" -ForegroundColor $(if ($dynamicFound -ge 6) { "Green" } else { "Yellow" })

Write-Host "`n6. Calculating Overall Score..." -ForegroundColor Cyan

# Calculate total score
$MaxPoints = 50
$TotalPoints = 0

# System files (max 10 points)
$TotalPoints += [math]::Min($TestResults.SystemFiles * 2.5, 10)

# Functions (max 15 points) 
$TotalPoints += [math]::Min($TestResults.Functions36 * 0.4, 15)

# File integration (max 10 points)
$TotalPoints += [math]::Min($TestResults.FileIntegration * 1.25, 10)

# Comprehensive content (max 10 points)
$TotalPoints += [math]::Min($TestResults.ComprehensiveContent * 0.67, 10)

# Dynamic features (max 5 points)
$TotalPoints += [math]::Min($TestResults.DynamicFeatures * 0.625, 5)

$OverallPercentage = [math]::Round(($TotalPoints / $MaxPoints) * 100)
$TestResults.TotalScore = $OverallPercentage

Write-Host "`n=== VERIFICATION RESULTS ===" -ForegroundColor Yellow
Write-Host "System Files: $($TestResults.SystemFiles)/4" -ForegroundColor White
Write-Host "Comprehensive Functions: $($TestResults.Functions36)/36" -ForegroundColor White  
Write-Host "File Integration: $($TestResults.FileIntegration)/8" -ForegroundColor White
Write-Host "Comprehensive Content: $($TestResults.ComprehensiveContent)/15" -ForegroundColor White
Write-Host "Dynamic Features: $($TestResults.DynamicFeatures)/8" -ForegroundColor White

Write-Host "`nOVERALL SCORE: $TotalPoints/$MaxPoints points ($OverallPercentage%)" -ForegroundColor $(if ($OverallPercentage -ge 90) { "Green" } elseif ($OverallPercentage -ge 70) { "Yellow" } else { "Red" })

if ($OverallPercentage -ge 90) {
    Write-Host "`n[EXCELLENT] Bug Bounty v4.0 system is working perfectly!" -ForegroundColor Green
    Write-Host "- All 36 functions are implemented" -ForegroundColor Green
    Write-Host "- All system files are integrated" -ForegroundColor Green
    Write-Host "- Reports are generated dynamically with comprehensive content" -ForegroundColor Green
} elseif ($OverallPercentage -ge 70) {
    Write-Host "`n[GOOD] Bug Bounty v4.0 system is working well with minor improvements needed" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] Bug Bounty v4.0 system requires fixes" -ForegroundColor Red
}

Write-Host "`n=== VERIFICATION COMPLETE ===" -ForegroundColor Green
