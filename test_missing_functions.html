<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 فحص الدوال المفقودة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 فحص الدوال المفقودة</h1>
        
        <button class="btn" onclick="checkMissingFunctions()">🚀 فحص الدوال</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function checkMissingFunctions() {
            clearResults();
            log('🔍 بدء فحص الدوال المفقودة', 'info');
            
            try {
                // تهيئة النظام
                const bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام بنجاح', 'success');
                
                // قائمة الدوال المطلوبة
                const requiredFunctions = [
                    'extractRealDataFromDiscoveredVulnerability',
                    'generateComprehensiveDetailsFromRealData',
                    'generateDynamicImpactForAnyVulnerability',
                    'generateRealExploitationStepsForVulnerabilityComprehensive',
                    'generateDynamicRecommendationsForVulnerability',
                    'generateRealDetailedDialogueFromDiscoveredVulnerability',
                    'generateRealVisualChangesForVulnerability',
                    'generateRealPersistentResultsForVulnerability',
                    'extractRealRecommendationsFromDiscoveredVulnerability',
                    'extractParameterFromDiscoveredVulnerability',
                    'extractRealExploitationDataFromDiscoveredVulnerability',
                    'extractSpecializedImpactFromDiscoveredVulnerability',
                    'extractRealExpertAnalysisFromDiscoveredVulnerability',
                    'extractRealPersistentDataFromDiscoveredVulnerability'
                ];
                
                let foundFunctions = 0;
                let missingFunctions = [];
                
                for (const funcName of requiredFunctions) {
                    if (typeof bugBountyCore[funcName] === 'function') {
                        log(`✅ ${funcName} - موجودة`, 'success');
                        foundFunctions++;
                    } else {
                        log(`❌ ${funcName} - مفقودة`, 'error');
                        missingFunctions.push(funcName);
                    }
                }
                
                log(`📊 النتائج: ${foundFunctions}/${requiredFunctions.length} دالة موجودة`, 'info');
                
                if (missingFunctions.length > 0) {
                    log(`❌ الدوال المفقودة: ${missingFunctions.join(', ')}`, 'error');
                } else {
                    log('🎉 جميع الدوال المطلوبة موجودة!', 'success');
                }
                
                // اختبار بسيط للدالة الرئيسية
                log('🔥 اختبار الدالة الرئيسية...', 'info');
                
                const testVuln = {
                    name: 'Test Vulnerability',
                    type: 'XSS',
                    severity: 'High'
                };
                
                const testRealData = {
                    payload: '<script>alert("test")</script>',
                    response: 'Script executed'
                };
                
                try {
                    const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                    if (result && typeof result === 'object') {
                        log('✅ الدالة الرئيسية تعمل بشكل صحيح', 'success');
                        log(`📊 النتيجة تحتوي على ${Object.keys(result).length} مفتاح`, 'info');
                    } else {
                        log('❌ الدالة الرئيسية لا تعمل بشكل صحيح', 'error');
                    }
                } catch (error) {
                    log(`❌ خطأ في الدالة الرئيسية: ${error.message}`, 'error');
                    log(`تفاصيل الخطأ: ${error.stack}`, 'error');
                }
                
            } catch (error) {
                log(`❌ خطأ في الفحص: ${error.message}`, 'error');
                console.error('تفاصيل الخطأ:', error);
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(checkMissingFunctions, 1000);
        });
    </script>
</body>
</html>
