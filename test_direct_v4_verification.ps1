# Direct PowerShell Test - NO manual intervention, NO browser
Write-Host "=== DIRECT v4.0 SYSTEM VERIFICATION ===" -ForegroundColor Red
Write-Host "Testing AUTOMATICALLY without any manual intervention" -ForegroundColor Yellow

$TestResults = @{
    CoreFileExists = $false
    ImpactVisualizerExists = $false
    TextualAnalyzerExists = $false
    Functions36Found = 0
    FileIntegrationFound = 0
    DynamicContentGeneration = 0
    AutomaticReportGeneration = 0
    VulnerabilitySpecificContent = 0
    TotalScore = 0
}

Write-Host "`n1. Testing Core System Files..." -ForegroundColor Cyan

# Test core files existence
$coreFile = "assets/modules/bugbounty/BugBountyCore.js"
$impactFile = "assets/modules/bugbounty/impact_visualizer.js"
$textualFile = "assets/modules/bugbounty/textual_impact_analyzer.js"

$TestResults.CoreFileExists = Test-Path $coreFile
$TestResults.ImpactVisualizerExists = Test-Path $impactFile
$TestResults.TextualAnalyzerExists = Test-Path $textualFile

Write-Host "  BugBountyCore.js: $(if ($TestResults.CoreFileExists) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if ($TestResults.CoreFileExists) { "Green" } else { "Red" })
Write-Host "  impact_visualizer.js: $(if ($TestResults.ImpactVisualizerExists) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if ($TestResults.ImpactVisualizerExists) { "Green" } else { "Red" })
Write-Host "  textual_impact_analyzer.js: $(if ($TestResults.TextualAnalyzerExists) { 'EXISTS' } else { 'MISSING' })" -ForegroundColor $(if ($TestResults.TextualAnalyzerExists) { "Green" } else { "Red" })

if (-not $TestResults.CoreFileExists) {
    Write-Host "  [ERROR] Core file missing - cannot continue test" -ForegroundColor Red
    exit
}

Write-Host "`n2. Testing 36 Comprehensive Functions in Core File..." -ForegroundColor Cyan

# Read core file content
$coreContent = Get-Content $coreFile -Raw -Encoding UTF8

# Test for 36 comprehensive functions
$comprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateVisualChangesForVulnerability",
    "captureScreenshotForVulnerability",
    "generateRealTimeSecurityMetrics",
    "generateComprehensiveRemediationPlan",
    "generateComprehensiveDocumentation",
    "generateDetailedTechnicalReport",
    "generateExecutiveSummaryReport",
    "generateComplianceReport",
    "generateForensicAnalysisReport",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateComprehensiveResponseAnalysis",
    "generateDynamicExploitationChain",
    "generateDynamicExpertAnalysisForVulnerability",
    "generateRealVisualChangesForVulnerability",
    "generateRealPersistentResultsForVulnerability",
    "generateRealPayloadFromVulnerability",
    "generateDetailedVulnerabilityAnalysis",
    "generateRealWorldExamples",
    "generateImpactVisualizationsForVulnerability",
    "generateBeforeAfterScreenshots",
    "generateAdvancedPayloadAnalysis",
    "generateInteractiveDialogue",
    "generateRealImpactChanges",
    "generateFinalComprehensiveReport",
    "loadAndActivateAllSystemFiles",
    "generatePageHTMLReport",
    "generateVulnerabilitiesHTML"
)

$functionsFound = 0
foreach ($func in $comprehensiveFunctions) {
    if ($coreContent -like "*$func*") {
        $functionsFound++
    }
}

$TestResults.Functions36Found = $functionsFound
Write-Host "  Found $functionsFound/36 comprehensive functions" -ForegroundColor $(if ($functionsFound -ge 30) { "Green" } elseif ($functionsFound -ge 20) { "Yellow" } else { "Red" })

Write-Host "`n3. Testing File Integration in Core System..." -ForegroundColor Cyan

# Test for file integration indicators
$fileIntegrationIndicators = @(
    "loadAndActivateAllSystemFiles",
    "impact_visualizer.js",
    "textual_impact_analyzer.js",
    "ImpactVisualizer",
    "TextualImpactAnalyzer",
    "visual_impact_data",
    "textual_impact_analysis",
    "business_impact_analysis",
    "exploitation_screenshots",
    "before_after_comparison"
)

$integrationFound = 0
foreach ($indicator in $fileIntegrationIndicators) {
    if ($coreContent -like "*$indicator*") {
        $integrationFound++
    }
}

$TestResults.FileIntegrationFound = $integrationFound
Write-Host "  Found $integrationFound/10 file integration indicators" -ForegroundColor $(if ($integrationFound -ge 8) { "Green" } elseif ($integrationFound -ge 5) { "Yellow" } else { "Red" })

Write-Host "`n4. Testing Dynamic Content Generation..." -ForegroundColor Cyan

# Test for dynamic content generation indicators
$dynamicContentIndicators = @(
    "extractRealDataFromDiscoveredVulnerability",
    "realData",
    "vuln.comprehensive_details",
    "vuln.dynamic_impact",
    "vuln.exploitation_steps",
    "vuln.visual_impact_data",
    "vuln.textual_impact_analysis",
    "vuln.business_impact_analysis",
    "حسب الثغرة المكتشفة",
    "تلقائياً وديناميكياً",
    "automatically",
    "dynamically",
    "based on discovered",
    "vulnerability-specific"
)

$dynamicFound = 0
foreach ($indicator in $dynamicContentIndicators) {
    if ($coreContent -like "*$indicator*") {
        $dynamicFound++
    }
}

$TestResults.DynamicContentGeneration = $dynamicFound
Write-Host "  Found $dynamicFound/14 dynamic content indicators" -ForegroundColor $(if ($dynamicFound -ge 10) { "Green" } elseif ($dynamicFound -ge 7) { "Yellow" } else { "Red" })

Write-Host "`n5. Testing Automatic Report Generation..." -ForegroundColor Cyan

# Test for automatic report generation features
$automaticReportIndicators = @(
    "generateVulnerabilitiesHTML",
    "generatePageHTMLReport",
    "comprehensive-section",
    "content-section",
    "vulnerability-header",
    "comprehensive_details",
    "dynamic_impact",
    "exploitation_steps",
    "visual_changes",
    "screenshot_data"
)

$automaticFound = 0
foreach ($indicator in $automaticReportIndicators) {
    if ($coreContent -like "*$indicator*") {
        $automaticFound++
    }
}

$TestResults.AutomaticReportGeneration = $automaticFound
Write-Host "  Found $automaticFound/10 automatic report generation indicators" -ForegroundColor $(if ($automaticFound -ge 8) { "Green" } elseif ($automaticFound -ge 5) { "Yellow" } else { "Red" })

Write-Host "`n6. Testing Vulnerability-Specific Content Generation..." -ForegroundColor Cyan

# Test for vulnerability-specific content generation
$vulnSpecificIndicators = @(
    "SQL Injection",
    "XSS",
    "CSRF",
    "LFI",
    "RFI",
    "payload",
    "response",
    "evidence",
    "confidence_level",
    "parameter",
    "severity",
    "method",
    "url",
    "timestamp"
)

$vulnSpecificFound = 0
foreach ($indicator in $vulnSpecificIndicators) {
    if ($coreContent -like "*$indicator*") {
        $vulnSpecificFound++
    }
}

$TestResults.VulnerabilitySpecificContent = $vulnSpecificFound
Write-Host "  Found $vulnSpecificFound/14 vulnerability-specific content indicators" -ForegroundColor $(if ($vulnSpecificFound -ge 10) { "Green" } elseif ($vulnSpecificFound -ge 7) { "Yellow" } else { "Red" })

Write-Host "`n7. Testing Impact Visualizer File..." -ForegroundColor Cyan

if ($TestResults.ImpactVisualizerExists) {
    $impactContent = Get-Content $impactFile -Raw -Encoding UTF8
    
    $impactFeatures = @(
        "ImpactVisualizer",
        "createVulnerabilityVisualization",
        "captureExploitationScreenshots",
        "generateBeforeAfterComparison",
        "generateRealTimeVisualAnalysis"
    )
    
    $impactFeaturesFound = 0
    foreach ($feature in $impactFeatures) {
        if ($impactContent -like "*$feature*") {
            $impactFeaturesFound++
        }
    }
    
    Write-Host "  Impact Visualizer features: $impactFeaturesFound/5 found" -ForegroundColor $(if ($impactFeaturesFound -ge 4) { "Green" } else { "Yellow" })
} else {
    Write-Host "  Impact Visualizer file missing" -ForegroundColor Red
}

Write-Host "`n8. Testing Textual Impact Analyzer File..." -ForegroundColor Cyan

if ($TestResults.TextualAnalyzerExists) {
    $textualContent = Get-Content $textualFile -Raw -Encoding UTF8
    
    $textualFeatures = @(
        "TextualImpactAnalyzer",
        "analyzeVulnerabilityImpact",
        "generateComprehensiveTextualReport",
        "analyzeBusinessImpact",
        "generateDetailedTextualAnalysis"
    )
    
    $textualFeaturesFound = 0
    foreach ($feature in $textualFeatures) {
        if ($textualContent -like "*$feature*") {
            $textualFeaturesFound++
        }
    }
    
    Write-Host "  Textual Impact Analyzer features: $textualFeaturesFound/5 found" -ForegroundColor $(if ($textualFeaturesFound -ge 4) { "Green" } else { "Yellow" })
} else {
    Write-Host "  Textual Impact Analyzer file missing" -ForegroundColor Red
}

Write-Host "`n9. Calculating Final Score..." -ForegroundColor Cyan

# Calculate total score
$maxScore = 100
$totalScore = 0

# Core files (15 points)
if ($TestResults.CoreFileExists) { $totalScore += 5 }
if ($TestResults.ImpactVisualizerExists) { $totalScore += 5 }
if ($TestResults.TextualAnalyzerExists) { $totalScore += 5 }

# 36 Functions (25 points)
$totalScore += [math]::Min(($TestResults.Functions36Found / 36) * 25, 25)

# File Integration (20 points)
$totalScore += [math]::Min(($TestResults.FileIntegrationFound / 10) * 20, 20)

# Dynamic Content (20 points)
$totalScore += [math]::Min(($TestResults.DynamicContentGeneration / 14) * 20, 20)

# Automatic Reports (10 points)
$totalScore += [math]::Min(($TestResults.AutomaticReportGeneration / 10) * 10, 10)

# Vulnerability-Specific (10 points)
$totalScore += [math]::Min(($TestResults.VulnerabilitySpecificContent / 14) * 10, 10)

$TestResults.TotalScore = [math]::Round($totalScore)

Write-Host "`n=== DIRECT v4.0 VERIFICATION RESULTS ===" -ForegroundColor Yellow
Write-Host "Core Files Present: $(if ($TestResults.CoreFileExists -and $TestResults.ImpactVisualizerExists -and $TestResults.TextualAnalyzerExists) { 'ALL 3' } else { 'MISSING SOME' })" -ForegroundColor $(if ($TestResults.CoreFileExists -and $TestResults.ImpactVisualizerExists -and $TestResults.TextualAnalyzerExists) { "Green" } else { "Red" })
Write-Host "36 Comprehensive Functions: $($TestResults.Functions36Found)/36" -ForegroundColor White
Write-Host "File Integration: $($TestResults.FileIntegrationFound)/10" -ForegroundColor White
Write-Host "Dynamic Content Generation: $($TestResults.DynamicContentGeneration)/14" -ForegroundColor White
Write-Host "Automatic Report Generation: $($TestResults.AutomaticReportGeneration)/10" -ForegroundColor White
Write-Host "Vulnerability-Specific Content: $($TestResults.VulnerabilitySpecificContent)/14" -ForegroundColor White

Write-Host "`nFINAL VERIFICATION SCORE: $($TestResults.TotalScore)/100" -ForegroundColor $(if ($TestResults.TotalScore -ge 90) { "Green" } elseif ($TestResults.TotalScore -ge 70) { "Yellow" } else { "Red" })

if ($TestResults.TotalScore -ge 90) {
    Write-Host "`n[EXCELLENT] v4.0 system modifications are PERFECT!" -ForegroundColor Green
    Write-Host "- All 36 functions implemented and integrated" -ForegroundColor Green
    Write-Host "- All system files present and integrated" -ForegroundColor Green
    Write-Host "- Dynamic content generation based on vulnerabilities" -ForegroundColor Green
    Write-Host "- Automatic report generation without manual intervention" -ForegroundColor Green
    Write-Host "- Vulnerability-specific content, NOT generic templates" -ForegroundColor Green
} elseif ($TestResults.TotalScore -ge 70) {
    Write-Host "`n[GOOD] v4.0 system is working well with minor improvements needed" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] v4.0 system requires significant fixes" -ForegroundColor Red
}

Write-Host "`n=== DIRECT VERIFICATION COMPLETE ===" -ForegroundColor Green
