$filePath = "assets/modules/bugbounty/BugBountyCore.js"
$content = Get-Content $filePath -Raw

$allFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "extractRealDataFromDiscoveredVulnerability",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealDetailedDialogueFromDiscoveredVulnerability",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateVisualChangesForVulnerability",
    "generatePersistentResultsForVulnerability",
    "generateImpactVisualizationsForVulnerability",
    "captureScreenshotForVulnerability",
    "generateBeforeAfterScreenshots",
    "generateAdvancedPayloadAnalysis",
    "generateComprehensiveResponseAnalysis",
    "generateDynamicExploitationChain",
    "generateRealTimeSecurityMetrics",
    "generateComprehensiveRemediationPlan",
    "generateComprehensiveDocumentation",
    "generateDetailedTechnicalReport",
    "generateExecutiveSummaryReport",
    "generateComplianceReport",
    "generateForensicAnalysisReport",
    "extractParameterFromDiscoveredVulnerability",
    "extractParametersFromUrl",
    "extractParameterFromPayload",
    "generateRealPayloadFromVulnerability",
    "analyzeVulnerabilityContext",
    "generateInteractiveDialogue",
    "generatePageHTMLReport",
    "generateFinalComprehensiveReport",
    "generateComprehensiveVulnerabilitiesContentUsingExistingFunctions"
)

Write-Host "CHECKING ALL 36 COMPREHENSIVE FUNCTIONS" -ForegroundColor Red
Write-Host "=======================================" -ForegroundColor Yellow

$foundFunctions = 0
foreach ($func in $allFunctions) {
    if ($content -match $func) {
        Write-Host "Found: $func" -ForegroundColor Green
        $foundFunctions++
    } else {
        Write-Host "Missing: $func" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RESULT: Found $foundFunctions out of $($allFunctions.Count) functions" -ForegroundColor Green

if ($foundFunctions -eq $allFunctions.Count) {
    Write-Host "ALL 36 FUNCTIONS ARE PRESENT!" -ForegroundColor Green
} else {
    Write-Host "SOME FUNCTIONS ARE MISSING" -ForegroundColor Yellow
}

# Check comprehensive usage
Write-Host ""
Write-Host "CHECKING COMPREHENSIVE USAGE:" -ForegroundColor Cyan

$usageChecks = @(
    "comprehensive_details",
    "comprehensive-section",
    "generatePageHTMLReport"
)

$usageFound = 0
foreach ($usage in $usageChecks) {
    if ($content -match $usage) {
        Write-Host "Found usage: $usage" -ForegroundColor Green
        $usageFound++
    } else {
        Write-Host "Missing usage: $usage" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "USAGE FOUND: $usageFound out of $($usageChecks.Count)" -ForegroundColor Green
Write-Host "VERIFICATION COMPLETE!" -ForegroundColor Green
