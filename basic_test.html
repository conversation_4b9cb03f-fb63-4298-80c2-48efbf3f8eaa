<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>🔥 اختبار بسيط للتقارير v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; direction: rtl; }
        .result { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border-left: 4px solid #28a745; }
        .error { background: #f8d7da; border-left: 4px solid #dc3545; }
        .info { background: #d1ecf1; border-left: 4px solid #17a2b8; }
        button { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 5px; }
        button:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>🔥 اختبار بسيط للتقارير الشاملة التفصيلية v4.0</h1>
    
    <button onclick="testReports()">🚀 اختبار التقارير</button>
    <button onclick="clearResults()">🗑️ مسح النتائج</button>
    
    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
            console.log(message);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testReports() {
            clearResults();
            log('🚀 بدء اختبار التقارير البسيط', 'info');

            try {
                // تحميل النظام
                log('📂 تحميل النظام...', 'info');
                
                // إنشاء script tag لتحميل BugBountyCore
                const script = document.createElement('script');
                script.src = './assets/modules/bugbounty/BugBountyCore.js';
                
                script.onload = async () => {
                    try {
                        log('✅ تم تحميل الملف', 'success');
                        
                        // إنشاء النظام
                        const bugBountyCore = new BugBountyCore();
                        log('✅ تم إنشاء النظام', 'success');
                        
                        // بيانات اختبار بسيطة
                        const testVuln = {
                            name: 'XSS Test',
                            type: 'XSS',
                            severity: 'High',
                            payload: '<script>alert("test")</script>',
                            url: 'http://example.com/test'
                        };
                        
                        log('🔧 اختبار الدالة الشاملة...', 'info');
                        
                        // اختبار الدالة الرئيسية
                        const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, {});
                        
                        if (result && typeof result === 'object') {
                            log('✅ الدالة الشاملة تعمل', 'success');
                            log(`📊 النتيجة: ${Object.keys(result).length} أقسام`, 'info');
                        } else {
                            log('❌ الدالة الشاملة لا تعمل', 'error');
                        }
                        
                        log('📊 اختبار التقرير الرئيسي...', 'info');
                        
                        // اختبار التقرير الرئيسي
                        const analysis = {
                            vulnerabilities: [testVuln],
                            summary: { total_vulnerabilities: 1 }
                        };
                        
                        const mainReport = await bugBountyCore.generateFinalComprehensiveReport(analysis, [], 'http://example.com');
                        
                        if (mainReport && mainReport.length > 1000) {
                            log(`✅ التقرير الرئيسي تم إنشاؤه (${(mainReport.length/1024).toFixed(1)} KB)`, 'success');
                            
                            // فحص المحتوى
                            if (mainReport.includes('الوصف الشامل التفصيلي') || 
                                mainReport.includes('comprehensive-vulnerability') ||
                                mainReport.includes('النظام الشامل التفصيلي v4.0')) {
                                log('🎉 التقرير يحتوي على المحتوى الشامل!', 'success');
                            } else {
                                log('⚠️ التقرير قد لا يحتوي على المحتوى الشامل', 'error');
                            }
                            
                            if (mainReport.includes('تم اكتشاف ثغرة أمنية') || 
                                mainReport.includes('حدث خطأ في تحميل التفاصيل الشاملة')) {
                                log('❌ التقرير يحتوي على محتوى عام', 'error');
                            } else {
                                log('✅ لا يوجد محتوى عام', 'success');
                            }
                        } else {
                            log('❌ التقرير الرئيسي فاشل', 'error');
                        }
                        
                        log('📋 اختبار التقرير المنفصل...', 'info');
                        
                        // اختبار التقرير المنفصل
                        const pageData = {
                            page_name: 'Test Page',
                            page_url: 'http://example.com/test',
                            vulnerabilities: [testVuln]
                        };
                        
                        const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, 'http://example.com/test', 1);
                        
                        if (separateReport && separateReport.length > 1000) {
                            log(`✅ التقرير المنفصل تم إنشاؤه (${(separateReport.length/1024).toFixed(1)} KB)`, 'success');
                            
                            // فحص المحتوى
                            if (separateReport.includes('الوصف الشامل التفصيلي') || 
                                separateReport.includes('comprehensive-vulnerability') ||
                                separateReport.includes('النظام الشامل التفصيلي v4.0')) {
                                log('🎉 التقرير المنفصل يحتوي على المحتوى الشامل!', 'success');
                            } else {
                                log('⚠️ التقرير المنفصل قد لا يحتوي على المحتوى الشامل', 'error');
                            }
                            
                            if (separateReport.includes('تم اكتشاف ثغرة أمنية') || 
                                separateReport.includes('حدث خطأ في تحميل التفاصيل الشاملة')) {
                                log('❌ التقرير المنفصل يحتوي على محتوى عام', 'error');
                            } else {
                                log('✅ التقرير المنفصل لا يحتوي على محتوى عام', 'success');
                            }
                        } else {
                            log('❌ التقرير المنفصل فاشل', 'error');
                        }
                        
                        log('🏁 انتهى الاختبار', 'info');
                        
                    } catch (error) {
                        log(`❌ خطأ في الاختبار: ${error.message}`, 'error');
                    }
                };
                
                script.onerror = () => {
                    log('❌ فشل في تحميل الملف', 'error');
                };
                
                document.head.appendChild(script);
                
            } catch (error) {
                log(`❌ خطأ عام: ${error.message}`, 'error');
            }
        }

        // تشغيل تلقائي
        window.addEventListener('load', () => {
            setTimeout(testReports, 500);
        });
    </script>
</body>
</html>
