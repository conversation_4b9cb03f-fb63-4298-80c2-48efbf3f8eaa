<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار التقارير الشاملة التفصيلية v4.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; background: #d4edda; }
        .error { border-left: 4px solid #dc3545; background: #f8d7da; }
        .warning { border-left: 4px solid #ffc107; background: #fff3cd; }
        .info { border-left: 4px solid #17a2b8; background: #d1ecf1; }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .stat {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار التقارير الشاملة التفصيلية v4.0</h1>
        <p>اختبار أن جميع التقارير تستخدم الدوال الشاملة التفصيلية الحقيقية</p>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="totalTests">0</div>
                <div>إجمالي الاختبارات</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="passedTests">0</div>
                <div>الاختبارات الناجحة</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="successRate">0%</div>
                <div>معدل النجاح</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="reportsGenerated">0</div>
                <div>التقارير المُنشأة</div>
            </div>
        </div>
        
        <button class="btn" onclick="testComprehensiveReports()">🚀 اختبار التقارير الشاملة</button>
        <button class="btn" onclick="testMainReport()">📊 اختبار التقرير الرئيسي</button>
        <button class="btn" onclick="testSeparateReports()">📋 اختبار التقارير المنفصلة</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let totalTests = 0;
        let passedTests = 0;
        let reportsGenerated = 0;
        let bugBountyCore;
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('successRate').textContent = totalTests > 0 ? Math.round(passedTests / totalTests * 100) + '%' : '0%';
            document.getElementById('reportsGenerated').textContent = reportsGenerated;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            totalTests = 0;
            passedTests = 0;
            reportsGenerated = 0;
            updateStats();
        }
        
        async function initializeSystem() {
            try {
                log('🔄 تهيئة النظام...', 'info');
                bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام بنجاح', 'success');
                return true;
            } catch (error) {
                log(`❌ فشل في تهيئة النظام: ${error.message}`, 'error');
                return false;
            }
        }
        
        function createTestVulnerability() {
            return {
                name: 'XSS Test Vulnerability',
                type: 'XSS',
                severity: 'High',
                url: 'http://example.com/test',
                location: 'http://example.com/test?param=value',
                payload: '<script>alert("comprehensive test")</script>',
                evidence: 'Script executed successfully in comprehensive test',
                description: 'Comprehensive XSS vulnerability discovered during testing',
                impact: 'High impact XSS vulnerability',
                recommendation: 'Implement comprehensive input validation'
            };
        }
        
        async function testMainReport() {
            log('📊 اختبار التقرير الرئيسي...', 'info');
            totalTests++;
            
            try {
                const testVuln = createTestVulnerability();
                const testAnalysis = {
                    vulnerabilities: [testVuln],
                    summary: {
                        total_vulnerabilities: 1,
                        high_severity: 1
                    }
                };
                
                log('📝 إنشاء التقرير الرئيسي...', 'info');
                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(
                    testAnalysis, 
                    [], 
                    'http://example.com'
                );
                
                if (mainReport && typeof mainReport === 'string' && mainReport.length > 1000) {
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        !mainReport.includes('تم اكتشاف ثغرة أمنية') &&
                        !mainReport.includes('قد يؤثر على أمان الموقع') &&
                        !mainReport.includes('يُنصح بإصلاح هذه الثغرة') &&
                        mainReport.length > 5000; // التقرير الشامل يجب أن يكون أطول
                    
                    if (hasComprehensiveContent) {
                        log('✅ التقرير الرئيسي يستخدم المحتوى الشامل التفصيلي', 'success');
                        log(`📊 حجم التقرير: ${(mainReport.length / 1024).toFixed(1)} KB`, 'info');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else {
                        log('❌ التقرير الرئيسي لا يزال يحتوي على محتوى عام', 'error');
                        log(`⚠️ حجم التقرير: ${(mainReport.length / 1024).toFixed(1)} KB (قد يكون صغير جداً)`, 'warning');
                        return false;
                    }
                } else {
                    log('❌ التقرير الرئيسي غير صالح أو فارغ', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testSeparateReports() {
            log('📋 اختبار التقارير المنفصلة...', 'info');
            totalTests++;
            
            try {
                const testVuln = createTestVulnerability();
                const testPageData = {
                    page_name: 'Test Page',
                    page_url: 'http://example.com/test',
                    vulnerabilities: [testVuln]
                };
                
                log('📝 إنشاء التقرير المنفصل...', 'info');
                const separateReport = await bugBountyCore.formatSinglePageReport(testPageData);
                
                if (separateReport && typeof separateReport === 'string' && separateReport.length > 1000) {
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        !separateReport.includes('تم اكتشاف ثغرة أمنية') &&
                        !separateReport.includes('قد يؤثر على أمان الموقع') &&
                        !separateReport.includes('يُنصح بإصلاح هذه الثغرة') &&
                        separateReport.length > 3000; // التقرير الشامل يجب أن يكون أطول
                    
                    if (hasComprehensiveContent) {
                        log('✅ التقرير المنفصل يستخدم المحتوى الشامل التفصيلي', 'success');
                        log(`📊 حجم التقرير: ${(separateReport.length / 1024).toFixed(1)} KB`, 'info');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else {
                        log('❌ التقرير المنفصل لا يزال يحتوي على محتوى عام', 'error');
                        log(`⚠️ حجم التقرير: ${(separateReport.length / 1024).toFixed(1)} KB (قد يكون صغير جداً)`, 'warning');
                        return false;
                    }
                } else {
                    log('❌ التقرير المنفصل غير صالح أو فارغ', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير المنفصل: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testComprehensiveFunctionDirectly() {
            log('🔥 اختبار الدالة الشاملة التفصيلية مباشرة...', 'info');
            totalTests++;
            
            try {
                const testVuln = createTestVulnerability();
                const testRealData = {
                    payload: '<script>alert("comprehensive test")</script>',
                    response: 'Script executed successfully',
                    evidence: 'Alert dialog appeared',
                    url: 'http://example.com/test'
                };
                
                log('📝 استدعاء generateComprehensiveDetailsFromRealData...', 'info');
                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                
                if (result && typeof result === 'object' && Object.keys(result).length > 5) {
                    log('✅ الدالة الشاملة التفصيلية تعمل بشكل صحيح', 'success');
                    log(`📊 الكائن يحتوي على ${Object.keys(result).length} مفتاح`, 'info');
                    passedTests++;
                    return true;
                } else {
                    log('❌ الدالة الشاملة التفصيلية لا تعمل بشكل صحيح', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الدالة الشاملة: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testComprehensiveReports() {
            clearResults();
            log('🚀 بدء اختبار التقارير الشاملة التفصيلية v4.0', 'info');
            
            // تهيئة النظام
            const initSuccess = await initializeSystem();
            if (!initSuccess) {
                updateStats();
                return;
            }
            
            // اختبار الدالة الشاملة مباشرة
            await testComprehensiveFunctionDirectly();
            
            // اختبار التقرير الرئيسي
            await testMainReport();
            
            // اختبار التقارير المنفصلة
            await testSeparateReports();
            
            // النتائج النهائية
            const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
            
            log('🏁 انتهى اختبار التقارير الشاملة', 'info');
            log(`📊 النتائج النهائية: ${passedTests}/${totalTests} (${successRate}%)`, 'info');
            
            if (passedTests === totalTests) {
                log('🎉 جميع التقارير تستخدم النظام الشامل التفصيلي v4.0!', 'success');
                log('✅ لا يوجد محتوى عام أو افتراضي في التقارير', 'success');
                log('🔥 جميع التفاصيل ديناميكية ومخصصة حسب الثغرة المكتشفة', 'success');
            } else if (passedTests > 0) {
                log('⚠️ بعض التقارير تستخدم النظام الشامل التفصيلي', 'warning');
            } else {
                log('❌ التقارير لا تستخدم النظام الشامل التفصيلي', 'error');
            }
            
            updateStats();
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testComprehensiveReports, 1000);
        });
    </script>
</body>
</html>
