# اختبار نهائي شامل للتحقق من حل مشكلة Maximum call stack size exceeded
param()

Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "🔧 اختبار نهائي شامل - التحقق من حل مشكلة Maximum call stack size exceeded" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

$testsPassed = 0
$testsTotal = 0

function Test-Fix {
    param([string]$Name, [scriptblock]$Test)
    $global:testsTotal++
    Write-Host "🔍 اختبار: $Name" -ForegroundColor Cyan
    try {
        $result = & $Test
        if ($result) {
            Write-Host "   ✅ نجح" -ForegroundColor Green
            $global:testsPassed++
        } else {
            Write-Host "   ❌ فشل" -ForegroundColor Red
        }
        return $result
    } catch {
        Write-Host "   ❌ خطأ: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# اختبار 1: فحص وجود الملف
Test-Fix "وجود ملف BugBountyCore.js" {
    Test-Path "assets\modules\bugbounty\BugBountyCore.js"
}

# اختبار 2: قراءة محتوى الملف
$content = $null
Test-Fix "قراءة محتوى الملف" {
    $global:content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction SilentlyContinue
    return ($content -ne $null -and $content.Count -gt 1000)
}

if ($content) {
    # اختبار 3: عدد دوال generatePageHTMLReport
    Test-Fix "عدد دوال generatePageHTMLReport (يجب أن يكون 1)" {
        $count = ($content | Select-String "async generatePageHTMLReport").Count
        Write-Host "      العدد الفعلي: $count" -ForegroundColor White
        return ($count -eq 1)
    }

    # اختبار 4: عدد استدعاءات extractRealDataFromDiscoveredVulnerability
    Test-Fix "عدد استدعاءات extractRealDataFromDiscoveredVulnerability (يجب أن يكون أقل من 40)" {
        $count = ($content | Select-String "this\.extractRealDataFromDiscoveredVulnerability").Count
        Write-Host "      العدد الفعلي: $count (كان 43)" -ForegroundColor White
        return ($count -lt 40)
    }

    # اختبار 5: استخدام البيانات المحفوظة
    Test-Fix "استخدام البيانات المحفوظة" {
        $count = ($content | Select-String "vuln\.extracted_real_data").Count
        Write-Host "      العدد الفعلي: $count" -ForegroundColor White
        return ($count -gt 0)
    }

    # اختبار 6: عدم وجود استدعاءات متكررة خطيرة
    Test-Fix "عدم وجود استدعاءات متكررة خطيرة" {
        $pattern1 = ($content | Select-String "generatePageHTMLReport.*generatePageHTMLReport").Count
        $pattern2 = ($content | Select-String "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData").Count
        $pattern3 = ($content | Select-String "generateDynamicImpactForAnyVulnerability.*generateDynamicImpactForAnyVulnerability").Count
        
        $total = $pattern1 + $pattern2 + $pattern3
        Write-Host "      الاستدعاءات المتكررة: $total" -ForegroundColor White
        
        return ($total -eq 0)
    }

    # اختبار 7: فحص الإصلاحات المحددة
    Test-Fix "فحص الإصلاحات المحددة" {
        # فحص السطر 36832
        $line36832 = $content[36831]
        $fix1 = $line36832 -like "*realData*" -and $line36832 -notlike "*extractRealDataFromDiscoveredVulnerability*"
        
        # فحص السطر 5357
        $line5357 = $content[5356]
        $fix2 = $line5357 -notlike "*generateDynamicImpactForAnyVulnerability*"
        
        Write-Host "      إصلاح السطر 36832: $(if($fix1){'✅'}else{'❌'})" -ForegroundColor White
        Write-Host "      إصلاح السطر 5357: $(if($fix2){'✅'}else{'❌'})" -ForegroundColor White
        
        return ($fix1 -and $fix2)
    }
}

# إنشاء ملف اختبار بسيط
Write-Host ""
Write-Host "📝 إنشاء ملف اختبار للتصدير..." -ForegroundColor Cyan

$testHTML = @"
<!DOCTYPE html>
<html lang="ar">
<head>
    <meta charset="UTF-8">
    <title>اختبار التصدير النهائي</title>
</head>
<body>
    <div id="status">جاري التحميل...</div>
    <div id="result"></div>
    
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let testResult = {
            status: 'LOADING',
            error: null,
            success: false,
            duration: 0,
            htmlSize: 0
        };

        // تسجيل الأخطاء
        window.addEventListener('error', function(e) {
            testResult.status = 'ERROR';
            testResult.error = e.message;
            if (e.message.includes('Maximum call stack size exceeded')) {
                testResult.error = 'STACK_OVERFLOW';
            }
            updateDisplay();
        });

        function updateDisplay() {
            document.getElementById('status').textContent = testResult.status;
            document.getElementById('result').textContent = JSON.stringify(testResult, null, 2);
        }

        async function runExportTest() {
            const startTime = Date.now();
            testResult.status = 'TESTING';
            updateDisplay();
            
            try {
                // إنشاء مثيل BugBountyCore
                const bugBountyCore = new BugBountyCore();
                
                // بيانات اختبار
                const testData = {
                    vulnerabilities: [{
                        name: 'Test SQL Injection Export',
                        type: 'SQL Injection',
                        severity: 'Critical',
                        description: 'Test vulnerability for export testing',
                        payload: "admin' OR '1'='1' --",
                        url: 'https://example.com/login',
                        extracted_real_data: {
                            payload: "admin' OR '1'='1' --",
                            url: 'https://example.com/login',
                            response: 'Login successful',
                            parameter: 'username'
                        },
                        comprehensive_details: 'Comprehensive test details',
                        dynamic_impact: 'Critical security impact',
                        exploitation_steps: 'Detailed exploitation steps',
                        dynamic_recommendations: 'Security recommendations'
                    }]
                };

                // اختبار generatePageHTMLReport مع timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('TIMEOUT_15_SECONDS')), 15000);
                });

                testResult.status = 'GENERATING_HTML';
                updateDisplay();

                const testPromise = bugBountyCore.generatePageHTMLReport(testData, 'https://example.com', 1);
                
                const result = await Promise.race([testPromise, timeoutPromise]);
                
                testResult.duration = Date.now() - startTime;
                testResult.htmlSize = result ? result.length : 0;
                testResult.success = result && result.length > 100;
                testResult.status = testResult.success ? 'SUCCESS' : 'FAILED';
                
            } catch (error) {
                testResult.duration = Date.now() - startTime;
                testResult.error = error.message;
                testResult.success = false;
                testResult.status = 'ERROR';
            }
            
            updateDisplay();
        }

        // تشغيل الاختبار عند تحميل الصفحة
        window.onload = function() {
            setTimeout(runExportTest, 1000);
        };
    </script>
</body>
</html>
"@

$testHTML | Out-File -FilePath "export_test.html" -Encoding UTF8
Write-Host "✅ تم إنشاء ملف export_test.html" -ForegroundColor Green

# تشغيل الاختبار
Write-Host "🌐 تشغيل اختبار التصدير..." -ForegroundColor Cyan
try {
    $process = Start-Process -FilePath "export_test.html" -PassThru
    Write-Host "✅ تم تشغيل اختبار التصدير في المتصفح" -ForegroundColor Green
    
    # انتظار 20 ثانية
    Write-Host "⏱️ انتظار 20 ثانية لإكمال الاختبار..." -ForegroundColor Yellow
    Start-Sleep -Seconds 20
    
    # إغلاق المتصفح
    if ($process -and !$process.HasExited) {
        $process.CloseMainWindow()
        Start-Sleep -Seconds 2
        if (!$process.HasExited) {
            $process.Kill()
        }
    }
    Write-Host "✅ تم إغلاق المتصفح" -ForegroundColor Green
    
} catch {
    Write-Host "⚠️ فشل في تشغيل اختبار المتصفح: $($_.Exception.Message)" -ForegroundColor Yellow
}

# النتائج النهائية
Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "📊 النتائج النهائية" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow

Write-Host "📈 إحصائيات الاختبارات:" -ForegroundColor Cyan
Write-Host "   - إجمالي الاختبارات: $testsTotal" -ForegroundColor White
Write-Host "   - الاختبارات الناجحة: $testsPassed" -ForegroundColor Green
Write-Host "   - الاختبارات الفاشلة: $($testsTotal - $testsPassed)" -ForegroundColor Red

$successRate = if ($testsTotal -gt 0) { [math]::Round(($testsPassed / $testsTotal) * 100, 1) } else { 0 }
Write-Host "   - معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host ""
Write-Host "🎯 النتيجة النهائية:" -ForegroundColor Yellow

if ($testsPassed -eq $testsTotal -and $testsTotal -gt 0) {
    Write-Host "🎉 تم حل مشكلة Maximum call stack size exceeded بالكامل!" -ForegroundColor Green
    Write-Host "✅ جميع الإصلاحات مطبقة بنجاح" -ForegroundColor Green
    Write-Host "✅ النظام جاهز للاستخدام الفعلي" -ForegroundColor Green
    Write-Host "✅ التصدير سيعمل بدون مشاكل" -ForegroundColor Green
} elseif ($successRate -ge 80) {
    Write-Host "⚠️ معظم الإصلاحات تعمل، لكن هناك بعض المشاكل البسيطة" -ForegroundColor Yellow
    Write-Host "🔧 قد تحتاج مراجعة إضافية" -ForegroundColor Yellow
} else {
    Write-Host "❌ هناك مشاكل كبيرة في الإصلاحات" -ForegroundColor Red
    Write-Host "🚨 المشكلة لم يتم حلها بالكامل" -ForegroundColor Red
}

# تنظيف الملفات
Write-Host ""
Write-Host "🧹 تنظيف الملفات المؤقتة..." -ForegroundColor Cyan
try {
    Remove-Item "export_test.html" -ErrorAction SilentlyContinue
    Write-Host "✅ تم تنظيف الملفات" -ForegroundColor Green
} catch {
    Write-Host "⚠️ فشل في تنظيف بعض الملفات" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
