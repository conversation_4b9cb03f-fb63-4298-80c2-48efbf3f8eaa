<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>اختبار تكامل ملفات النظام v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { background: #dc3545; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .results { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; min-height: 300px; }
        .log { margin: 5px 0; padding: 8px; border-left: 3px solid #007bff; background: #f8f9fa; font-family: monospace; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        button { background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 10px; }
        button:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار تكامل ملفات النظام v4.0</h1>
            <p>اختبار استخدام impact_visualizer.js و textual_impact_analyzer.js في التقارير</p>
        </div>
        
        <button onclick="testSystemFilesIntegration()">🚀 اختبار تكامل الملفات</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div class="results" id="results">
            <h3>📊 نتائج الاختبار:</h3>
            <p>اضغط على الزر لبدء الاختبار...</p>
        </div>
    </div>

    <!-- تحميل النظام v4.0 -->
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            document.getElementById('results').appendChild(logEntry);
            console.log(`[${timestamp}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '<h3>📊 نتائج الاختبار:</h3>';
        }

        async function testSystemFilesIntegration() {
            clearResults();
            log('🔥 بدء اختبار تكامل ملفات النظام v4.0...', 'info');
            
            try {
                // إنشاء النظام
                const bugBountyCore = new BugBountyCore();
                log('✅ تم تحميل BugBountyCore', 'success');
                
                // اختبار تحميل الملفات
                log('🔄 اختبار تحميل وتفعيل جميع ملفات النظام...', 'info');
                const filesLoaded = await bugBountyCore.loadAndActivateAllSystemFiles();
                log(`📊 نتيجة تحميل الملفات: ${filesLoaded ? '✅ نجح' : '❌ فشل'}`, filesLoaded ? 'success' : 'error');
                
                // فحص الملفات المحملة
                log('🔍 فحص الملفات المحملة...', 'info');
                
                const impactVisualizerLoaded = typeof window.ImpactVisualizer !== 'undefined';
                log(`🎨 impact_visualizer.js: ${impactVisualizerLoaded ? '✅ محمل' : '❌ غير محمل'}`, impactVisualizerLoaded ? 'success' : 'error');
                
                const textualAnalyzerLoaded = typeof window.TextualImpactAnalyzer !== 'undefined';
                log(`📝 textual_impact_analyzer.js: ${textualAnalyzerLoaded ? '✅ محمل' : '❌ غير محمل'}`, textualAnalyzerLoaded ? 'success' : 'error');
                
                // إنشاء ثغرة تجريبية
                const testVuln = {
                    name: 'Cross-Site Scripting في نموذج التعليقات',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://testphp.vulnweb.com/comments.php',
                    parameter: 'comment',
                    payload: '<script>alert("XSS Test")</script>',
                    response: 'تم عرض الـ script في الصفحة',
                    evidence: 'تم تنفيذ الكود JavaScript',
                    confidence_level: 90
                };
                
                log('📊 إنشاء ثغرة تجريبية للاختبار...', 'info');
                
                // اختبار استخدام impact_visualizer.js
                if (impactVisualizerLoaded) {
                    log('🎨 اختبار استخدام impact_visualizer.js...', 'info');
                    try {
                        const impactVisualizer = new window.ImpactVisualizer(bugBountyCore);
                        const realData = { url: testVuln.url, payload: testVuln.payload, response: testVuln.response };
                        
                        const visualData = await impactVisualizer.createVulnerabilityVisualization(testVuln, realData);
                        testVuln.visual_impact_data = visualData;
                        log('✅ تم إنشاء التصورات البصرية بنجاح', 'success');
                        
                        const screenshots = await impactVisualizer.captureExploitationScreenshots(testVuln, realData);
                        testVuln.exploitation_screenshots = screenshots;
                        log('✅ تم التقاط صور الاستغلال بنجاح', 'success');
                        
                    } catch (error) {
                        log(`⚠️ خطأ في استخدام impact_visualizer.js: ${error.message}`, 'warning');
                    }
                } else {
                    log('❌ impact_visualizer.js غير متاح للاختبار', 'error');
                }
                
                // اختبار استخدام textual_impact_analyzer.js
                if (textualAnalyzerLoaded) {
                    log('📝 اختبار استخدام textual_impact_analyzer.js...', 'info');
                    try {
                        const textualAnalyzer = new window.TextualImpactAnalyzer(bugBountyCore);
                        const realData = { url: testVuln.url, payload: testVuln.payload, response: testVuln.response };
                        
                        const textualAnalysis = await textualAnalyzer.analyzeVulnerabilityImpact(testVuln, realData);
                        testVuln.textual_impact_analysis = textualAnalysis;
                        log('✅ تم إنشاء التحليل النصي بنجاح', 'success');
                        
                        const businessImpact = await textualAnalyzer.analyzeBusinessImpact(testVuln, realData);
                        testVuln.business_impact_analysis = businessImpact;
                        log('✅ تم تحليل التأثير على الأعمال بنجاح', 'success');
                        
                    } catch (error) {
                        log(`⚠️ خطأ في استخدام textual_impact_analyzer.js: ${error.message}`, 'warning');
                    }
                } else {
                    log('❌ textual_impact_analyzer.js غير متاح للاختبار', 'error');
                }
                
                // إنشاء تقرير تجريبي
                log('📄 إنشاء تقرير تجريبي مع الملفات المدمجة...', 'info');
                const testReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                
                // فحص محتوى التقرير
                const hasImpactVisualizerContent = testReport.includes('impact_visualizer.js') || testReport.includes('visual_impact_data');
                const hasTextualAnalyzerContent = testReport.includes('textual_impact_analyzer.js') || testReport.includes('textual_impact_analysis');
                const hasBusinessImpactContent = testReport.includes('business_impact_analysis');
                
                log(`📊 حجم التقرير: ${(testReport.length / 1024).toFixed(1)} KB`, 'info');
                log(`🎨 محتوى impact_visualizer.js: ${hasImpactVisualizerContent ? '✅ موجود' : '❌ مفقود'}`, hasImpactVisualizerContent ? 'success' : 'error');
                log(`📝 محتوى textual_impact_analyzer.js: ${hasTextualAnalyzerContent ? '✅ موجود' : '❌ مفقود'}`, hasTextualAnalyzerContent ? 'success' : 'error');
                log(`💼 تحليل التأثير على الأعمال: ${hasBusinessImpactContent ? '✅ موجود' : '❌ مفقود'}`, hasBusinessImpactContent ? 'success' : 'error');
                
                // حفظ التقرير
                const blob = new Blob([testReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `system_files_integration_test_${Date.now()}.html`;
                a.click();
                
                log('🎉 تم إنشاء وحفظ التقرير التجريبي بنجاح!', 'success');
                
                // النتيجة النهائية
                const allFilesWorking = impactVisualizerLoaded && textualAnalyzerLoaded && hasImpactVisualizerContent && hasTextualAnalyzerContent;
                log(`🏆 النتيجة النهائية: ${allFilesWorking ? '✅ جميع الملفات تعمل بنجاح' : '⚠️ بعض الملفات تحتاج مراجعة'}`, allFilesWorking ? 'success' : 'warning');
                
            } catch (error) {
                log(`❌ خطأ في اختبار تكامل الملفات: ${error.message}`, 'error');
            }
        }

        // تحميل تلقائي
        window.onload = function() {
            log('🔥 صفحة اختبار تكامل ملفات النظام v4.0 جاهزة', 'info');
            log('📊 اضغط على الزر لبدء الاختبار', 'info');
        };
    </script>
</body>
</html>
