<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار تشخيص مشكلة Stack Overflow</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        button { padding: 10px 20px; margin: 10px; font-size: 16px; }
        .log { background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🔧 اختبار تشخيص مشكلة Maximum call stack size exceeded</h1>
    
    <button onclick="testGeneratePageHTMLReport()">اختبار generatePageHTMLReport</button>
    <button onclick="testGenerateComprehensiveDetails()">اختبار generateComprehensiveDetailsFromRealData</button>
    <button onclick="clearLog()">مسح السجل</button>

    <div id="results"></div>
    <div id="log" class="log"></div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let logDiv = document.getElementById('log');
        let resultsDiv = document.getElementById('results');
        
        // تسجيل جميع الأخطاء
        window.addEventListener('error', function(e) {
            addLog(`❌ خطأ JavaScript: ${e.message} في ${e.filename}:${e.lineno}`);
            if (e.message.includes('Maximum call stack size exceeded')) {
                addResult('❌ تم اكتشاف Maximum call stack size exceeded!', 'error');
            }
        });

        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
        }

        function addLog(message) {
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            logDiv.innerHTML = '';
            resultsDiv.innerHTML = '';
        }

        async function testGeneratePageHTMLReport() {
            addResult('🔄 بدء اختبار generatePageHTMLReport...', 'info');
            addLog('بدء اختبار generatePageHTMLReport...');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    addLog('تم إنشاء مثيل BugBountyCore');
                }
                
                // إنشاء بيانات اختبار بسيطة
                const testPageResult = {
                    vulnerabilities: [{
                        name: 'Test XSS',
                        type: 'XSS',
                        severity: 'Medium',
                        description: 'Test vulnerability',
                        payload: '<script>alert(1)</script>',
                        url: 'https://example.com/test'
                    }]
                };
                
                addLog('تم إنشاء بيانات الاختبار');
                
                // محاولة استدعاء الدالة مع timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout after 10 seconds')), 10000);
                });
                
                const testPromise = bugBountyCore.generatePageHTMLReport(testPageResult, 'https://example.com', 1);
                
                addLog('بدء استدعاء generatePageHTMLReport...');
                
                const result = await Promise.race([testPromise, timeoutPromise]);
                
                if (result && result.length > 100) {
                    addResult(`✅ نجح الاختبار - تم إنشاء HTML بحجم ${result.length} حرف`, 'success');
                    addLog(`نجح الاختبار - HTML بحجم ${result.length} حرف`);
                } else {
                    addResult('⚠️ الاختبار أنتج نتيجة فارغة أو قصيرة', 'error');
                    addLog('الاختبار أنتج نتيجة فارغة أو قصيرة');
                }
                
            } catch (error) {
                addResult(`❌ فشل الاختبار: ${error.message}`, 'error');
                addLog(`فشل الاختبار: ${error.message}`);
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 تأكيد: المشكلة لا تزال موجودة!', 'error');
                    addLog('تأكيد: مشكلة Maximum call stack size exceeded لا تزال موجودة');
                }
            }
        }

        async function testGenerateComprehensiveDetails() {
            addResult('🔄 بدء اختبار generateComprehensiveDetailsFromRealData...', 'info');
            addLog('بدء اختبار generateComprehensiveDetailsFromRealData...');
            
            try {
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    addLog('تم إنشاء مثيل BugBountyCore');
                }
                
                // إنشاء بيانات اختبار بسيطة
                const testVuln = {
                    name: 'Test SQL Injection',
                    type: 'SQL Injection',
                    severity: 'High',
                    payload: "' OR 1=1 --",
                    url: 'https://example.com/test'
                };
                
                const testRealData = {
                    url: 'https://example.com/test',
                    parameter: 'id',
                    payload: "' OR 1=1 --"
                };
                
                addLog('تم إنشاء بيانات الاختبار');
                
                // محاولة استدعاء الدالة مع timeout
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout after 5 seconds')), 5000);
                });
                
                const testPromise = bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                
                addLog('بدء استدعاء generateComprehensiveDetailsFromRealData...');
                
                const result = await Promise.race([testPromise, timeoutPromise]);
                
                if (result && typeof result === 'object') {
                    addResult(`✅ نجح الاختبار - تم إنشاء تفاصيل شاملة`, 'success');
                    addLog(`نجح الاختبار - تم إنشاء تفاصيل شاملة`);
                } else {
                    addResult('⚠️ الاختبار أنتج نتيجة غير متوقعة', 'error');
                    addLog('الاختبار أنتج نتيجة غير متوقعة');
                }
                
            } catch (error) {
                addResult(`❌ فشل الاختبار: ${error.message}`, 'error');
                addLog(`فشل الاختبار: ${error.message}`);
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 تأكيد: المشكلة في هذه الدالة!', 'error');
                    addLog('تأكيد: مشكلة Maximum call stack size exceeded في generateComprehensiveDetailsFromRealData');
                }
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة التشخيص بنجاح', 'success');
            addLog('تم تحميل صفحة التشخيص');
        };
    </script>
</body>
</html>
