Write-Host "TESTING 36 COMPREHENSIVE FUNCTIONS" -ForegroundColor Red
Write-Host "====================================" -ForegroundColor Yellow

# Open HTML test file
Write-Host "Opening HTML test file..." -ForegroundColor Green
Start-Process "test_comprehensive_functions.html"
Write-Host "HTML test file opened" -ForegroundColor Green

# Check file
$filePath = "assets/modules/bugbounty/BugBountyCore.js"
if (Test-Path $filePath) {
    Write-Host "BugBountyCore.js exists" -ForegroundColor Green
    
    $content = Get-Content $filePath -Raw
    $fileSize = [math]::Round((Get-Item $filePath).Length / 1KB, 2)
    Write-Host "File size: $fileSize KB" -ForegroundColor Cyan
    
    # Check key functions
    $functions = @(
        "generateComprehensiveDetailsFromRealData",
        "generateVulnerabilitiesHTML",
        "generateDynamicImpactForAnyVulnerability",
        "generateAdvancedPayloadAnalysis",
        "generateComprehensiveDocumentation"
    )
    
    Write-Host ""
    Write-Host "Checking key functions:" -ForegroundColor Cyan
    $found = 0
    foreach ($func in $functions) {
        if ($content -match $func) {
            Write-Host "  Found: $func" -ForegroundColor Green
            $found++
        } else {
            Write-Host "  Missing: $func" -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Write-Host "RESULTS:" -ForegroundColor Yellow
    Write-Host "Found: $found/$($functions.Count) functions" -ForegroundColor Cyan
    
    if ($found -eq $functions.Count) {
        Write-Host "ALL KEY FUNCTIONS FOUND!" -ForegroundColor Green
    } elseif ($found -ge 3) {
        Write-Host "Most functions found" -ForegroundColor Yellow
    } else {
        Write-Host "Many functions missing" -ForegroundColor Red
    }
    
} else {
    Write-Host "BugBountyCore.js not found" -ForegroundColor Red
}

Write-Host ""
Write-Host "Test completed!" -ForegroundColor Green
Write-Host "Use the HTML interface for detailed testing" -ForegroundColor Cyan
