# Test Existing Reports Content - Check ACTUAL usage in generated reports
Write-Host "=== TESTING EXISTING REPORTS CONTENT ===" -ForegroundColor Red
Write-Host "Checking ACTUAL usage of 36 functions and files in existing reports" -ForegroundColor Yellow

$TestResults = @{
    ReportsFound = 0
    Functions36Used = 0
    FilesUsed = 0
    ComprehensiveContent = 0
    DynamicContent = 0
    TotalScore = 0
}

function Test-ReportContent {
    param($ReportPath, $SearchTexts)
    if (Test-Path $ReportPath) {
        $content = Get-Content $ReportPath -Raw -Encoding UTF8
        $matches = 0
        foreach ($text in $SearchTexts) {
            if ($content -like "*$text*") { $matches++ }
        }
        return $matches
    }
    return 0
}

Write-Host "`n1. Finding existing reports..." -ForegroundColor Cyan

# Find all HTML reports in current directory and subdirectories
$reportFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse | Where-Object { 
    $_.Name -like "*report*" -or 
    $_.Name -like "*bug*" -or 
    $_.Name -like "*vulnerability*" -or
    $_.Name -like "*comprehensive*" -or
    $_.Name -like "*main*" -or
    $_.Name -like "*separate*"
}

$TestResults.ReportsFound = $reportFiles.Count
Write-Host "  Found $($reportFiles.Count) report files" -ForegroundColor $(if ($reportFiles.Count -gt 0) { "Green" } else { "Red" })

if ($reportFiles.Count -eq 0) {
    Write-Host "  [ERROR] No report files found to test" -ForegroundColor Red
    Write-Host "  Please generate some reports first using the Bug Bounty system" -ForegroundColor Yellow
    exit
}

# Show found reports
foreach ($report in $reportFiles) {
    Write-Host "    - $($report.Name) ($('{0:N0}' -f ($report.Length / 1024)) KB)" -ForegroundColor Gray
}

Write-Host "`n2. Testing 36 Functions Usage in Reports..." -ForegroundColor Cyan

# Test for 36 comprehensive functions usage indicators
$functions36Indicators = @(
    "comprehensive_details",
    "dynamic_impact", 
    "exploitation_steps",
    "dynamic_recommendations",
    "visual_changes",
    "screenshot_data",
    "security_metrics",
    "remediation_plan",
    "comprehensive_documentation",
    "technical_report",
    "executive_summary",
    "compliance_report",
    "forensic_analysis",
    "comprehensive_analysis",
    "security_impact_analysis",
    "realtime_assessment",
    "risk_analysis",
    "threat_modeling",
    "testing_details",
    "response_analysis",
    "exploitation_chain",
    "expert_analysis",
    "real_visual_changes",
    "real_persistent_results",
    "real_payload",
    "detailed_vulnerability_analysis",
    "real_world_examples",
    "impact_visualizations",
    "before_after_screenshots",
    "advanced_payload_analysis",
    "interactive_dialogue",
    "real_impact_changes",
    "final_comprehensive_report",
    "business_impact",
    "affected_components",
    "confidence_level_analysis"
)

$totalFunctionsFound = 0
foreach ($report in $reportFiles) {
    $functionsInReport = Test-ReportContent $report.FullName $functions36Indicators
    $totalFunctionsFound += $functionsInReport
    Write-Host "    $($report.Name): $functionsInReport/36 function indicators found" -ForegroundColor $(if ($functionsInReport -ge 20) { "Green" } elseif ($functionsInReport -ge 10) { "Yellow" } else { "Red" })
}

$TestResults.Functions36Used = $totalFunctionsFound
Write-Host "  Total function indicators across all reports: $totalFunctionsFound" -ForegroundColor $(if ($totalFunctionsFound -ge 50) { "Green" } else { "Yellow" })

Write-Host "`n3. Testing System Files Usage in Reports..." -ForegroundColor Cyan

# Test for system files usage indicators
$filesUsageIndicators = @(
    "impact_visualizer.js",
    "textual_impact_analyzer.js",
    "visual_impact_data",
    "textual_impact_analysis",
    "business_impact_analysis",
    "exploitation_screenshots",
    "before_after_comparison",
    "real_time_visual_analysis",
    "comprehensive_textual_report",
    "detailed_textual_analysis"
)

$totalFilesUsed = 0
foreach ($report in $reportFiles) {
    $filesInReport = Test-ReportContent $report.FullName $filesUsageIndicators
    $totalFilesUsed += $filesInReport
    Write-Host "    $($report.Name): $filesInReport/10 file usage indicators found" -ForegroundColor $(if ($filesInReport -ge 6) { "Green" } elseif ($filesInReport -ge 3) { "Yellow" } else { "Red" })
}

$TestResults.FilesUsed = $totalFilesUsed
Write-Host "  Total file usage indicators across all reports: $totalFilesUsed" -ForegroundColor $(if ($totalFilesUsed -ge 20) { "Green" } else { "Yellow" })

Write-Host "`n4. Testing Comprehensive Content in Reports..." -ForegroundColor Cyan

# Test for comprehensive content sections
$comprehensiveContentIndicators = @(
    "comprehensive-section",
    "content-section", 
    "vulnerability-header",
    "التفاصيل الشاملة التفصيلية",
    "خطوات الاستغلال الشاملة التفصيلية",
    "تحليل التأثير الشامل التفصيلي",
    "الحوارات التفاعلية الشاملة",
    "تفاصيل الاختبار والـ Payloads",
    "التغيرات البصرية التفصيلية",
    "صور التأثير والاستغلال",
    "مقاييس الأمان في الوقت الفعلي",
    "خطة الإصلاح الشاملة",
    "التوثيق الشامل",
    "التقرير التقني المفصل",
    "الملخص التنفيذي",
    "تقرير الامتثال",
    "التحليل الجنائي",
    "التوصيات الشاملة التفصيلية"
)

$totalComprehensiveContent = 0
foreach ($report in $reportFiles) {
    $comprehensiveInReport = Test-ReportContent $report.FullName $comprehensiveContentIndicators
    $totalComprehensiveContent += $comprehensiveInReport
    Write-Host "    $($report.Name): $comprehensiveInReport/18 comprehensive sections found" -ForegroundColor $(if ($comprehensiveInReport -ge 12) { "Green" } elseif ($comprehensiveInReport -ge 6) { "Yellow" } else { "Red" })
}

$TestResults.ComprehensiveContent = $totalComprehensiveContent
Write-Host "  Total comprehensive content across all reports: $totalComprehensiveContent" -ForegroundColor $(if ($totalComprehensiveContent -ge 30) { "Green" } else { "Yellow" })

Write-Host "`n5. Testing Dynamic Content in Reports..." -ForegroundColor Cyan

# Test for dynamic/vulnerability-specific content
$dynamicContentIndicators = @(
    "SQL Injection",
    "XSS",
    "CSRF",
    "LFI",
    "RFI",
    "payload",
    "response",
    "evidence",
    "confidence_level",
    "parameter",
    "UNION SELECT",
    "<script>",
    "alert(",
    "../../../../",
    "include(",
    "http://",
    "testphp.vulnweb.com"
)

$totalDynamicContent = 0
foreach ($report in $reportFiles) {
    $dynamicInReport = Test-ReportContent $report.FullName $dynamicContentIndicators
    $totalDynamicContent += $dynamicInReport
    Write-Host "    $($report.Name): $dynamicInReport/17 dynamic content indicators found" -ForegroundColor $(if ($dynamicInReport -ge 10) { "Green" } elseif ($dynamicInReport -ge 5) { "Yellow" } else { "Red" })
}

$TestResults.DynamicContent = $totalDynamicContent
Write-Host "  Total dynamic content across all reports: $totalDynamicContent" -ForegroundColor $(if ($totalDynamicContent -ge 25) { "Green" } else { "Yellow" })

Write-Host "`n6. Testing v4.0 System Indicators..." -ForegroundColor Cyan

# Test for v4.0 system specific indicators
$v4SystemIndicators = @(
    "v4.0",
    "النظام v4.0",
    "Bug Bounty v4.0",
    "الـ 36 دالة",
    "36 دالة",
    "comprehensive functions",
    "الشاملة التفصيلية",
    "تلقائياً وديناميكياً",
    "حسب الثغرة المكتشفة",
    "جميع الملفات"
)

$totalV4Indicators = 0
foreach ($report in $reportFiles) {
    $v4InReport = Test-ReportContent $report.FullName $v4SystemIndicators
    $totalV4Indicators += $v4InReport
    Write-Host "    $($report.Name): $v4InReport/10 v4.0 system indicators found" -ForegroundColor $(if ($v4InReport -ge 6) { "Green" } elseif ($v4InReport -ge 3) { "Yellow" } else { "Red" })
}

Write-Host "  Total v4.0 system indicators across all reports: $totalV4Indicators" -ForegroundColor $(if ($totalV4Indicators -ge 15) { "Green" } else { "Yellow" })

Write-Host "`n7. Calculating ACTUAL Usage Score..." -ForegroundColor Cyan

# Calculate actual usage score based on found content
$maxScore = 100
$actualScore = 0

# Reports found (10 points)
$actualScore += [math]::Min($TestResults.ReportsFound * 2, 10)

# Functions usage (35 points)
$actualScore += [math]::Min($TestResults.Functions36Used * 0.5, 35)

# Files usage (25 points) 
$actualScore += [math]::Min($TestResults.FilesUsed * 1.25, 25)

# Comprehensive content (20 points)
$actualScore += [math]::Min($TestResults.ComprehensiveContent * 0.67, 20)

# Dynamic content (10 points)
$actualScore += [math]::Min($TestResults.DynamicContent * 0.4, 10)

$TestResults.TotalScore = [math]::Round($actualScore)

Write-Host "`n=== ACTUAL USAGE IN REPORTS RESULTS ===" -ForegroundColor Yellow
Write-Host "Reports Found: $($TestResults.ReportsFound)" -ForegroundColor White
Write-Host "36 Functions Usage Indicators: $($TestResults.Functions36Used)" -ForegroundColor White
Write-Host "System Files Usage Indicators: $($TestResults.FilesUsed)" -ForegroundColor White
Write-Host "Comprehensive Content Indicators: $($TestResults.ComprehensiveContent)" -ForegroundColor White
Write-Host "Dynamic Content Indicators: $($TestResults.DynamicContent)" -ForegroundColor White
Write-Host "v4.0 System Indicators: $totalV4Indicators" -ForegroundColor White

Write-Host "`nACTUAL USAGE SCORE: $($TestResults.TotalScore)/100" -ForegroundColor $(if ($TestResults.TotalScore -ge 80) { "Green" } elseif ($TestResults.TotalScore -ge 60) { "Yellow" } else { "Red" })

if ($TestResults.TotalScore -ge 80) {
    Write-Host "`n[EXCELLENT] The v4.0 system IS ACTUALLY USING all 36 functions and files in reports!" -ForegroundColor Green
    Write-Host "- Reports contain comprehensive detailed content" -ForegroundColor Green
    Write-Host "- Dynamic content based on discovered vulnerabilities" -ForegroundColor Green
    Write-Host "- All system files are integrated and used" -ForegroundColor Green
    Write-Host "- Reports are generated automatically and dynamically" -ForegroundColor Green
} elseif ($TestResults.TotalScore -ge 60) {
    Write-Host "`n[GOOD] The v4.0 system is using most functions and files in reports" -ForegroundColor Yellow
    Write-Host "- Some improvements needed for full utilization" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] The v4.0 system is not fully utilizing functions and files in reports" -ForegroundColor Red
    Write-Host "- Functions and files exist but are not being used effectively" -ForegroundColor Red
}

Write-Host "`n=== ACTUAL USAGE VERIFICATION COMPLETE ===" -ForegroundColor Green
