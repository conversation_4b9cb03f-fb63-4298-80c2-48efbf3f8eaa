# اختبار شامل للتحقق من إصلاح مشكلة Maximum call stack size exceeded
Write-Host "🔧 بدء اختبار شامل للتحقق من إصلاح مشكلة Maximum call stack size exceeded" -ForegroundColor Green
Write-Host ""

# متغيرات النتائج
$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-Fix {
    param(
        [string]$TestName,
        [string]$LineNumber,
        [string]$ExpectedContent,
        [string]$NotExpectedContent = ""
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    
    try {
        # قراءة الملف
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
        $lineIndex = [int]$LineNumber - 1
        $actualLine = $content[$lineIndex]
        
        Write-Host "   السطر $LineNumber`: $actualLine" -ForegroundColor Cyan
        
        # فحص المحتوى المتوقع
        $hasExpected = $actualLine -like "*$ExpectedContent*"
        $hasNotExpected = if ($NotExpectedContent) { $actualLine -like "*$NotExpectedContent*" } else { $false }
        
        if ($hasExpected -and -not $hasNotExpected) {
            Write-Host "   ✅ نجح الاختبار" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ فشل الاختبار" -ForegroundColor Red
            if (-not $hasExpected) {
                Write-Host "      - لم يتم العثور على: $ExpectedContent" -ForegroundColor Red
            }
            if ($hasNotExpected) {
                Write-Host "      - تم العثور على محتوى غير مرغوب: $NotExpectedContent" -ForegroundColor Red
            }
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

Write-Host "=== اختبار 1: التحقق من الإصلاحات في الكود ===" -ForegroundColor Yellow
Write-Host ""

# اختبار السطر 11724 - يجب أن يحتوي على vuln.comprehensive_details
Test-Fix -TestName "إصلاح السطر 11724" -LineNumber "11724" -ExpectedContent "vuln.comprehensive_details" -NotExpectedContent "generateComprehensiveDetailsFromRealData"

# اختبار السطر 11304 - يجب أن يحتوي على page_html_summary  
Test-Fix -TestName "إصلاح السطر 11304" -LineNumber "11304" -ExpectedContent "page_html_summary" -NotExpectedContent "generatePageHTMLReport"

# اختبار السطر 45730 - يجب أن يحتوي على final_comprehensive_summary
Test-Fix -TestName "إصلاح السطر 45730" -LineNumber "45730" -ExpectedContent "final_comprehensive_summary" -NotExpectedContent "generateFinalComprehensiveReport"

# اختبار السطر 11273 - يجب أن يحتوي على final_report_summary
Test-Fix -TestName "إصلاح السطر 11273" -LineNumber "11273" -ExpectedContent "final_report_summary" -NotExpectedContent "generateFinalComprehensiveReport"

# اختبار السطر 25074 - يجب أن يحتوي على await
Test-Fix -TestName "إصلاح السطر 25074" -LineNumber "25074" -ExpectedContent "await this.generatePageHTMLReport"

Write-Host ""
Write-Host "=== اختبار 2: فحص عدم وجود استدعاءات متكررة ===" -ForegroundColor Yellow
Write-Host ""

try {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # البحث عن استدعاءات خطيرة
    $dangerousPatterns = @(
        "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData",
        "generatePageHTMLReport.*generatePageHTMLReport", 
        "generateFinalComprehensiveReport.*generateFinalComprehensiveReport"
    )
    
    $foundDangerous = $false
    foreach ($pattern in $dangerousPatterns) {
        $matches = $content | Select-String -Pattern $pattern
        if ($matches) {
            Write-Host "❌ تم العثور على استدعاء متكرر خطير: $pattern" -ForegroundColor Red
            $foundDangerous = $true
            $failedTests++
        }
    }
    
    if (-not $foundDangerous) {
        Write-Host "✅ لم يتم العثور على استدعاءات متكررة خطيرة" -ForegroundColor Green
        $passedTests++
    }
    $totalTests++
    
} catch {
    Write-Host "❌ خطأ في فحص الاستدعاءات المتكررة: $($_.Exception.Message)" -ForegroundColor Red
    $failedTests++
    $totalTests++
}

Write-Host ""
Write-Host "=== اختبار 3: فحص بنية الملف ===" -ForegroundColor Yellow
Write-Host ""

try {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    $totalLines = $content.Count
    
    Write-Host "📊 إحصائيات الملف:" -ForegroundColor Cyan
    Write-Host "   - إجمالي الأسطر: $totalLines" -ForegroundColor White
    
    # فحص وجود الدوال المهمة
    $importantFunctions = @(
        "generateComprehensiveDetailsFromRealData",
        "generatePageHTMLReport", 
        "generateVulnerabilitiesHTML",
        "extractRealDataFromDiscoveredVulnerability"
    )
    
    foreach ($func in $importantFunctions) {
        $matches = $content | Select-String -Pattern "async\s+$func\s*\(" -AllMatches
        if ($matches) {
            Write-Host "   ✅ تم العثور على الدالة: $func" -ForegroundColor Green
            $passedTests++
        } else {
            Write-Host "   ❌ لم يتم العثور على الدالة: $func" -ForegroundColor Red
            $failedTests++
        }
        $totalTests++
    }
    
} catch {
    Write-Host "❌ خطأ في فحص بنية الملف: $($_.Exception.Message)" -ForegroundColor Red
    $failedTests++
    $totalTests++
}

Write-Host ""
Write-Host "=== النتائج النهائية ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "📊 إجمالي الاختبارات: $totalTests" -ForegroundColor White
Write-Host "✅ الاختبارات الناجحة: $passedTests" -ForegroundColor Green  
Write-Host "❌ الاختبارات الفاشلة: $failedTests" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "📈 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -ge 80) { "Green" } elseif ($successRate -ge 60) { "Yellow" } else { "Red" })

Write-Host ""
if ($failedTests -eq 0) {
    Write-Host "🎉 تم إصلاح جميع المشاكل بنجاح! النظام جاهز للعمل." -ForegroundColor Green
} elseif ($failedTests -le 2) {
    Write-Host "⚠️ هناك بعض المشاكل البسيطة التي تحتاج إصلاح." -ForegroundColor Yellow
} else {
    Write-Host "🚨 هناك مشاكل متعددة تحتاج إصلاح فوري." -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 انتهى الاختبار الشامل" -ForegroundColor Green
