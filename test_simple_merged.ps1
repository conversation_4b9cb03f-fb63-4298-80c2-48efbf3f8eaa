# اختبار بسيط للدالة المدمجة
Write-Host "🔧 اختبار بسيط للدالة المدمجة generatePageHTMLReport" -ForegroundColor Green
Write-Host ""

try {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # فحص عدد دوال generatePageHTMLReport
    $generatePageHTMLReportCount = ($content | Select-String "async generatePageHTMLReport").Count
    Write-Host "عدد دوال generatePageHTMLReport: $generatePageHTMLReportCount" -ForegroundColor Cyan
    
    if ($generatePageHTMLReportCount -eq 1) {
        Write-Host "✅ يوجد دالة واحدة فقط - تم الدمج بنجاح" -ForegroundColor Green
    } else {
        Write-Host "❌ يوجد $generatePageHTMLReportCount دوال - لم يتم الدمج بشكل صحيح" -ForegroundColor Red
    }
    
    # فحص وجود الـ36 دالة
    $functions36Count = ($content | Select-String "تطبيق جميع الـ 36 دالة الشاملة التفصيلية").Count
    Write-Host "عدد مراجع الـ36 دالة: $functions36Count" -ForegroundColor Cyan
    
    if ($functions36Count -gt 0) {
        Write-Host "✅ تم العثور على مراجع الـ36 دالة في الدالة المدمجة" -ForegroundColor Green
    } else {
        Write-Host "❌ لم يتم العثور على مراجع الـ36 دالة" -ForegroundColor Red
    }
    
    # فحص عدم وجود استدعاءات متكررة
    $recursivePattern1 = $content | Select-String "generatePageHTMLReport.*generatePageHTMLReport"
    $recursivePattern2 = $content | Select-String "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData"
    
    if (-not $recursivePattern1 -and -not $recursivePattern2) {
        Write-Host "✅ لا توجد استدعاءات متكررة خطيرة" -ForegroundColor Green
    } else {
        Write-Host "❌ تم العثور على استدعاءات متكررة خطيرة" -ForegroundColor Red
    }
    
    # فحص استخدام الملفات المطلوبة
    $impactVisualizerCount = ($content | Select-String "ImpactVisualizer").Count
    $textualAnalyzerCount = ($content | Select-String "TextualImpactAnalyzer").Count
    
    Write-Host "استخدام impact_visualizer.js: $impactVisualizerCount مرة" -ForegroundColor Cyan
    Write-Host "استخدام textual_impact_analyzer.js: $textualAnalyzerCount مرة" -ForegroundColor Cyan
    
    if ($impactVisualizerCount -gt 0 -and $textualAnalyzerCount -gt 0) {
        Write-Host "✅ تم العثور على استخدام الملفات المطلوبة" -ForegroundColor Green
    } else {
        Write-Host "❌ لم يتم العثور على استخدام الملفات المطلوبة" -ForegroundColor Red
    }
    
    Write-Host ""
    Write-Host "=== النتيجة النهائية ===" -ForegroundColor Yellow
    
    if ($generatePageHTMLReportCount -eq 1 -and $functions36Count -gt 0 -and -not $recursivePattern1 -and -not $recursivePattern2) {
        Write-Host "🎉 تم دمج الدوال بنجاح! النظام جاهز للعمل بدون مشاكل Maximum call stack size exceeded." -ForegroundColor Green
        Write-Host ""
        Write-Host "🔥 الدالة المدمجة تحتوي على:" -ForegroundColor Green
        Write-Host "   ✅ تحميل القالب من الملف" -ForegroundColor Green
        Write-Host "   ✅ جميع الـ36 دالة الشاملة التفصيلية" -ForegroundColor Green
        Write-Host "   ✅ استخدام impact_visualizer.js" -ForegroundColor Green
        Write-Host "   ✅ استخدام textual_impact_analyzer.js" -ForegroundColor Green
        Write-Host "   ✅ إنشاء HTML شامل تفصيلي" -ForegroundColor Green
        Write-Host "   ✅ لا توجد استدعاءات متكررة خطيرة" -ForegroundColor Green
    } else {
        Write-Host "🚨 هناك مشاكل تحتاج إصلاح." -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 انتهى الاختبار" -ForegroundColor Green
