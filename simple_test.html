<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار بسيط للدالة الشاملة</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 اختبار بسيط للدالة الشاملة التفصيلية</h1>
        
        <button class="btn" onclick="testFunction()">🚀 اختبار الدالة</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        
        function log(message) {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.textContent += `[${timestamp}] ${message}\n`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function clearResults() {
            document.getElementById('results').textContent = '';
        }
        
        async function testFunction() {
            try {
                log('🔄 بدء الاختبار...');
                
                // تهيئة النظام
                log('📝 تهيئة BugBountyCore...');
                bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة BugBountyCore بنجاح');
                
                // فحص وجود الدالة
                log('🔍 فحص وجود الدالة...');
                if (typeof bugBountyCore.generateComprehensiveDetailsFromRealData === 'function') {
                    log('✅ الدالة generateComprehensiveDetailsFromRealData موجودة');
                } else {
                    log('❌ الدالة generateComprehensiveDetailsFromRealData غير موجودة');
                    return;
                }
                
                // إنشاء بيانات اختبار
                const testVulnerability = {
                    name: 'XSS Test Vulnerability',
                    type: 'XSS',
                    url: 'http://example.com/test',
                    payload: '<script>alert("test")</script>',
                    severity: 'High'
                };

                const testRealData = {
                    payload: '<script>alert("test")</script>',
                    response: 'Script executed successfully',
                    evidence: 'Alert dialog appeared',
                    url: 'http://example.com/test'
                };
                
                log('📊 بيانات الاختبار:');
                log(`   الثغرة: ${JSON.stringify(testVulnerability, null, 2)}`);
                log(`   البيانات الحقيقية: ${JSON.stringify(testRealData, null, 2)}`);
                
                // تشغيل الدالة
                log('⚡ تشغيل الدالة...');
                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVulnerability, testRealData);
                
                // فحص النتائج
                log('📋 فحص النتائج...');
                log(`   نوع النتيجة: ${typeof result}`);
                
                if (result === null) {
                    log('❌ الدالة أرجعت null - حدث خطأ في التنفيذ');
                    return;
                }
                
                if (result && typeof result === 'object') {
                    const keys = Object.keys(result);
                    log(`✅ الدالة أرجعت كائناً يحتوي على ${keys.length} مفتاح`);
                    log(`   المفاتيح: ${keys.join(', ')}`);
                    
                    // فحص المحتوى
                    if (result.technical_details) {
                        log('✅ technical_details موجود');
                        if (result.technical_details.comprehensive_description) {
                            const desc = result.technical_details.comprehensive_description;
                            log(`   الوصف الشامل: ${desc.substring(0, 100)}...`);
                        }
                    }
                    
                    if (result.impact_analysis) {
                        log('✅ impact_analysis موجود');
                        if (result.impact_analysis.detailed_impact) {
                            const impact = result.impact_analysis.detailed_impact;
                            log(`   التأثير المفصل: ${typeof impact} (${impact.length} حرف)`);
                        }
                    }
                    
                    if (result.exploitation_results) {
                        log('✅ exploitation_results موجود');
                        if (result.exploitation_results.detailed_steps) {
                            const steps = result.exploitation_results.detailed_steps;
                            log(`   خطوات الاستغلال: ${Array.isArray(steps) ? steps.length + ' خطوة' : typeof steps}`);
                        }
                    }
                    
                    // فحص وجود محتوى placeholder
                    const jsonStr = JSON.stringify(result);
                    if (jsonStr.includes('[object Object]')) {
                        log('⚠️ تحذير: تم اكتشاف [object Object] في النتائج');
                    } else if (jsonStr.includes('payload_example')) {
                        log('⚠️ تحذير: تم اكتشاف payload_example في النتائج');
                    } else {
                        log('✅ لا يوجد محتوى placeholder - البيانات تبدو حقيقية');
                    }
                    
                    log('🎉 الاختبار مكتمل بنجاح!');
                    
                } else {
                    log('❌ الدالة لم ترجع كائناً صحيحاً');
                }
                
            } catch (error) {
                log(`❌ خطأ في الاختبار: ${error.message}`);
                log(`❌ تفاصيل الخطأ: ${error.stack}`);
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(testFunction, 1000);
        });
    </script>
</body>
</html>
