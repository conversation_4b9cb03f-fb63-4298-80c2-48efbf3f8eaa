﻿<!DOCTYPE html>
<html><head><meta charset="UTF-8"></head><body>
<script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
<script>
try {
    const core = new BugBountyCore();
    const testData = {vulnerabilities: [{name: "Test", type: "XSS", extracted_real_data: {}}]};
    core.generatePageHTMLReport(testData, "test", 1).then(result => {
        document.body.innerHTML = result ? "SUCCESS:" + result.length : "FAILED";
    }).catch(err => {
        document.body.innerHTML = "ERROR:" + err.message;
    });
} catch(e) {
    document.body.innerHTML = "CATCH:" + e.message;
}
</script>
</body></html>
