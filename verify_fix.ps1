# Verify the fix for comprehensive details display
Write-Host "VERIFYING FIX FOR COMPREHENSIVE DETAILS DISPLAY" -ForegroundColor Red
Write-Host "===============================================" -ForegroundColor Yellow

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$content = Get-Content $bugBountyFile -Raw

Write-Host ""
Write-Host "CHECKING FIXED OBJECT DISPLAY PATTERNS:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

# Check for the old problematic patterns
$oldPatterns = @(
    '\$\{vuln\.comprehensive_details\s*\|\|\s*[''"]',
    '\$\{vuln\.dynamic_impact\s*\|\|\s*[''"]',
    '\$\{vuln\.exploitation_steps\s*\|\|\s*[''"]'
)

$oldPatternsFound = 0
foreach ($pattern in $oldPatterns) {
    $matches = [regex]::Matches($content, $pattern)
    if ($matches.Count -gt 0) {
        Write-Host "OLD PATTERN STILL EXISTS: $pattern" -ForegroundColor Red
        $oldPatternsFound++
        foreach ($match in $matches) {
            Write-Host "   Found: $($match.Value)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "OLD PATTERN FIXED: $pattern" -ForegroundColor Green
    }
}

Write-Host ""
Write-Host "CHECKING NEW FIXED PATTERNS:" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Yellow

# Check for the new fixed patterns
$newPatterns = @(
    'vuln\.comprehensive_details\?\.technical_details\?\.comprehensive_description',
    'vuln\.dynamic_impact\?\.detailed_impact',
    'vuln\.exploitation_steps\?\.detailed_steps',
    'typeof vuln\.comprehensive_details === [''"]object[''"]'
)

$newPatternsFound = 0
foreach ($pattern in $newPatterns) {
    if ($content -match $pattern) {
        Write-Host "NEW PATTERN FOUND: $pattern" -ForegroundColor Green
        $newPatternsFound++
    } else {
        Write-Host "NEW PATTERN MISSING: $pattern" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "CHECKING IMPACT_VISUALIZER INTEGRATION:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

$impactVisualizerChecks = @(
    "this\.impactVisualizer",
    "initializeImpactVisualizer",
    "impact_visualizer\.js"
)

$visualizerIntegration = 0
foreach ($check in $impactVisualizerChecks) {
    if ($content -match [regex]::Escape($check)) {
        Write-Host "FOUND: $check" -ForegroundColor Green
        $visualizerIntegration++
    } else {
        Write-Host "MISSING: $check" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "CHECKING TEXTUAL_IMPACT_ANALYZER INTEGRATION:" -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Yellow

$textualAnalyzerChecks = @(
    "this\.textualImpactAnalyzer",
    "initializeTextualImpactAnalyzer", 
    "textual_impact_analyzer\.js"
)

$textualIntegration = 0
foreach ($check in $textualAnalyzerChecks) {
    if ($content -match [regex]::Escape($check)) {
        Write-Host "FOUND: $check" -ForegroundColor Green
        $textualIntegration++
    } else {
        Write-Host "MISSING: $check" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "CHECKING COMPREHENSIVE FUNCTIONS USAGE:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

$comprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateVisualChangesForVulnerability"
)

$functionsUsed = 0
foreach ($func in $comprehensiveFunctions) {
    if ($content -match $func) {
        Write-Host "FUNCTION EXISTS: $func" -ForegroundColor Green
        $functionsUsed++
    } else {
        Write-Host "FUNCTION MISSING: $func" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "VERIFICATION SUMMARY:" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

Write-Host "Old problematic patterns remaining: $oldPatternsFound" -ForegroundColor $(if ($oldPatternsFound -eq 0) { "Green" } else { "Red" })
Write-Host "New fixed patterns implemented: $newPatternsFound/4" -ForegroundColor $(if ($newPatternsFound -ge 3) { "Green" } else { "Red" })
Write-Host "Impact Visualizer integration: $visualizerIntegration/3" -ForegroundColor $(if ($visualizerIntegration -ge 2) { "Green" } else { "Red" })
Write-Host "Textual Analyzer integration: $textualIntegration/3" -ForegroundColor $(if ($textualIntegration -ge 2) { "Green" } else { "Red" })
Write-Host "Comprehensive functions: $functionsUsed/5" -ForegroundColor $(if ($functionsUsed -eq 5) { "Green" } else { "Red" })

Write-Host ""
if ($oldPatternsFound -eq 0 -and $newPatternsFound -ge 3 -and $functionsUsed -eq 5) {
    Write-Host "FIX VERIFICATION: SUCCESS!" -ForegroundColor Green
    Write-Host "The object display issue has been fixed!" -ForegroundColor Green
    Write-Host "Comprehensive details should now display properly!" -ForegroundColor Green
} else {
    Write-Host "FIX VERIFICATION: PARTIAL OR FAILED" -ForegroundColor Yellow
    Write-Host "Some issues may still remain" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "VERIFICATION COMPLETE!" -ForegroundColor Green
