<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار التقارير الشاملة التفصيلية v4.0 - صالح</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
            direction: rtl;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .report-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار التقارير الشاملة التفصيلية v4.0</h1>
            <p>التحقق من أن جميع التقارير تنتج التفاصيل الشاملة التفصيلية حسب الثغرة المكتشفة والمختبرة تلقائياً وديناميكياً</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📊 حالة الاختبار</h3>
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <p id="status">جاهز للبدء...</p>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div>إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div>الاختبارات الناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div>معدل النجاح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="reportsGenerated">0</div>
                    <div>التقارير المُنشأة</div>
                </div>
            </div>

            <div class="test-section">
                <h3>🎯 الاختبارات الشاملة</h3>
                <button class="btn" onclick="runComprehensiveTest()">🚀 تشغيل الاختبار الشامل</button>
                <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
            </div>

            <div class="test-section">
                <h3>📋 نتائج الاختبار</h3>
                <div id="results" class="result"></div>
            </div>

            <div class="test-section" id="reportPreviewSection" style="display: none;">
                <h3>📄 معاينة التقرير</h3>
                <div id="reportPreview" class="report-preview"></div>
            </div>
        </div>
    </div>

    <script>
        let totalTests = 0;
        let passedTests = 0;
        let reportsGenerated = 0;
        let bugBountyCore;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('successRate').textContent = totalTests > 0 ? Math.round(passedTests / totalTests * 100) + '%' : '0%';
            document.getElementById('reportsGenerated').textContent = reportsGenerated;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('reportPreviewSection').style.display = 'none';
            totalTests = 0;
            passedTests = 0;
            reportsGenerated = 0;
            updateStats();
        }

        function showReportPreview(reportContent, title) {
            const previewSection = document.getElementById('reportPreviewSection');
            const previewDiv = document.getElementById('reportPreview');
            
            previewDiv.innerHTML = `<h4>${title}</h4><pre>${reportContent.substring(0, 2000)}${reportContent.length > 2000 ? '\n\n... (تم اقتطاع المحتوى للعرض)' : ''}</pre>`;
            previewSection.style.display = 'block';
        }

        async function loadBugBountyCore() {
            return new Promise((resolve, reject) => {
                log('📂 تحميل ملف BugBountyCore.js...', 'info');
                
                const script = document.createElement('script');
                script.src = './assets/modules/bugbounty/BugBountyCore.js';
                
                script.onload = () => {
                    try {
                        if (typeof BugBountyCore !== 'undefined') {
                            bugBountyCore = new BugBountyCore();
                            log('✅ تم تحميل وتهيئة BugBountyCore بنجاح', 'success');
                            resolve(true);
                        } else {
                            log('❌ BugBountyCore غير معرف بعد التحميل', 'error');
                            reject(new Error('BugBountyCore not defined'));
                        }
                    } catch (error) {
                        log(`❌ خطأ في إنشاء BugBountyCore: ${error.message}`, 'error');
                        reject(error);
                    }
                };
                
                script.onerror = () => {
                    log('❌ فشل في تحميل ملف BugBountyCore.js', 'error');
                    reject(new Error('Failed to load script'));
                };
                
                document.head.appendChild(script);
            });
        }

        function createTestVulnerabilities() {
            return [
                {
                    name: 'SQL Injection in Login Form',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://example.com/login.php',
                    location: 'http://example.com/login.php?username=admin',
                    payload: "admin' OR '1'='1' --",
                    evidence: 'Database error revealed: MySQL syntax error',
                    description: 'Critical SQL injection vulnerability in login form',
                    parameter: 'username',
                    method: 'POST',
                    response: 'MySQL Error: You have an error in your SQL syntax'
                },
                {
                    name: 'Cross-Site Scripting (XSS)',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://example.com/search.php',
                    location: 'http://example.com/search.php?q=test',
                    payload: '<script>alert("XSS")</script>',
                    evidence: 'Script executed successfully in browser',
                    description: 'Reflected XSS vulnerability in search functionality',
                    parameter: 'q',
                    method: 'GET',
                    response: 'Script tag rendered without encoding'
                }
            ];
        }

        async function testComprehensiveFunctions() {
            log('🔧 اختبار الدوال الشاملة التفصيلية...', 'info');
            totalTests++;

            try {
                const testVuln = createTestVulnerabilities()[0];
                const testRealData = {
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence,
                    url: testVuln.url
                };

                log('📝 استدعاء generateComprehensiveDetailsFromRealData...', 'info');
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);

                if (comprehensiveDetails && typeof comprehensiveDetails === 'object' && Object.keys(comprehensiveDetails).length > 3) {
                    log('✅ الدالة الشاملة التفصيلية تعمل بشكل صحيح', 'success');
                    log(`📊 النتيجة تحتوي على ${Object.keys(comprehensiveDetails).length} قسم رئيسي`, 'info');
                    log(`🔑 الأقسام: ${Object.keys(comprehensiveDetails).join(', ')}`, 'info');
                    passedTests++;
                    return true;
                } else {
                    log('❌ الدالة الشاملة التفصيلية لا تعمل بشكل صحيح', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الدوال الشاملة: ${error.message}`, 'error');
                return false;
            }
        }

        async function testMainReport() {
            log('📊 اختبار التقرير الرئيسي...', 'info');
            totalTests++;

            try {
                const testVulns = createTestVulnerabilities();
                const testAnalysis = {
                    vulnerabilities: testVulns,
                    summary: {
                        total_vulnerabilities: testVulns.length,
                        critical_severity: 1,
                        high_severity: 1
                    }
                };

                log('📝 إنشاء التقرير الرئيسي...', 'info');
                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(testAnalysis, [], 'http://example.com');

                if (mainReport && typeof mainReport === 'string' && mainReport.length > 3000) {
                    log(`✅ التقرير الرئيسي تم إنشاؤه (${(mainReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        mainReport.includes('الوصف الشامل التفصيلي') ||
                        mainReport.includes('خطوات الاستغلال الشاملة') ||
                        mainReport.includes('تحليل التأثير الشامل') ||
                        mainReport.includes('النظام الشامل التفصيلي v4.0') ||
                        mainReport.includes('comprehensive-vulnerability') ||
                        mainReport.includes('vulnerability-comprehensive-v4');
                    
                    const hasGenericContent = 
                        mainReport.includes('تم اكتشاف ثغرة أمنية') ||
                        mainReport.includes('قد يؤثر على أمان الموقع') ||
                        mainReport.includes('يُنصح بإصلاح هذه الثغرة') ||
                        mainReport.includes('حدث خطأ في تحميل التفاصيل الشاملة');
                    
                    if (hasComprehensiveContent && !hasGenericContent) {
                        log('🎉 التقرير الرئيسي يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                        showReportPreview(mainReport, 'التقرير الرئيسي');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else if (hasGenericContent) {
                        log('❌ التقرير الرئيسي يحتوي على محتوى عام أو افتراضي', 'error');
                        showReportPreview(mainReport, 'التقرير الرئيسي (يحتوي على محتوى عام)');
                        return false;
                    } else {
                        log('⚠️ التقرير الرئيسي قد لا يحتوي على المحتوى الشامل التفصيلي', 'warning');
                        showReportPreview(mainReport, 'التقرير الرئيسي (محتوى غير واضح)');
                        return false;
                    }
                } else {
                    log('❌ التقرير الرئيسي غير صالح أو صغير جداً', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
                return false;
            }
        }

        async function testSeparateReports() {
            log('📋 اختبار التقارير المنفصلة...', 'info');
            totalTests++;

            try {
                const testVulns = createTestVulnerabilities();
                const testPageData = {
                    page_name: 'Test Security Page',
                    page_url: 'http://example.com/test',
                    vulnerabilities: testVulns
                };

                log('📝 إنشاء التقرير المنفصل...', 'info');
                const separateReport = await bugBountyCore.generatePageHTMLReport(testPageData, 'http://example.com/test', 1);

                if (separateReport && typeof separateReport === 'string' && separateReport.length > 2000) {
                    log(`✅ التقرير المنفصل تم إنشاؤه (${(separateReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const hasComprehensiveContent = 
                        separateReport.includes('الوصف الشامل التفصيلي') ||
                        separateReport.includes('خطوات الاستغلال الشاملة') ||
                        separateReport.includes('تحليل التأثير الشامل') ||
                        separateReport.includes('النظام الشامل التفصيلي v4.0') ||
                        separateReport.includes('comprehensive-vulnerability');
                    
                    const hasGenericContent = 
                        separateReport.includes('تم اكتشاف ثغرة أمنية') ||
                        separateReport.includes('قد يؤثر على أمان الموقع') ||
                        separateReport.includes('يُنصح بإصلاح هذه الثغرة') ||
                        separateReport.includes('حدث خطأ في تحميل التفاصيل الشاملة');
                    
                    if (hasComprehensiveContent && !hasGenericContent) {
                        log('🎉 التقرير المنفصل يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                        showReportPreview(separateReport, 'التقرير المنفصل');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else if (hasGenericContent) {
                        log('❌ التقرير المنفصل يحتوي على محتوى عام أو افتراضي', 'error');
                        showReportPreview(separateReport, 'التقرير المنفصل (يحتوي على محتوى عام)');
                        return false;
                    } else {
                        log('⚠️ التقرير المنفصل قد لا يحتوي على المحتوى الشامل التفصيلي', 'warning');
                        showReportPreview(separateReport, 'التقرير المنفصل (محتوى غير واضح)');
                        return false;
                    }
                } else {
                    log('❌ التقرير المنفصل غير صالح أو صغير جداً', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير المنفصل: ${error.message}`, 'error');
                return false;
            }
        }

        async function runComprehensiveTest() {
            clearResults();
            log('🚀 بدء الاختبار الشامل للتقارير v4.0', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...');
            updateProgress(0);

            try {
                // تحميل وتهيئة النظام
                updateProgress(10);
                await loadBugBountyCore();
                updateProgress(20);

                // اختبار الدوال الشاملة
                updateProgress(40);
                const functionsTest = await testComprehensiveFunctions();
                
                // اختبار التقرير الرئيسي
                updateProgress(70);
                const mainReportTest = await testMainReport();
                
                // اختبار التقارير المنفصلة
                updateProgress(90);
                const separateReportsTest = await testSeparateReports();

                updateProgress(100);

                // النتائج النهائية
                const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

                log('🏁 انتهى الاختبار الشامل', 'info');
                log(`📊 النتائج النهائية: ${passedTests}/${totalTests} (${successRate}%)`, 'info');

                if (passedTests === totalTests) {
                    updateStatus(`🎉 نجح في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                    log('🎉 النظام الشامل التفصيلي v4.0 يعمل بكفاءة 100%!', 'success');
                    log('✅ جميع التقارير تنتج التفاصيل الشاملة التفصيلية تلقائياً وديناميكياً', 'success');
                    log('🔥 لا يوجد محتوى عام أو افتراضي أو يدوي', 'success');
                    log('🚀 النظام جاهز للاستخدام الإنتاجي!', 'success');
                } else if (passedTests > 0) {
                    updateStatus(`⚠️ نجح في بعض الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                    log('⚠️ النظام الشامل التفصيلي v4.0 يحتاج مراجعة', 'warning');
                } else {
                    updateStatus(`❌ فشل في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                    log('❌ النظام الشامل التفصيلي v4.0 لا يعمل بشكل صحيح', 'error');
                }

                updateStats();

            } catch (error) {
                log(`❌ خطأ في الاختبار الشامل: ${error.message}`, 'error');
                updateStatus('❌ فشل في تشغيل الاختبار');
                updateStats();
            }
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runComprehensiveTest, 1000);
        });
    </script>
</body>
</html>
