# 🔥 اختبار النظام الفعلي v4.0 الشامل التفصيلي
# =====================================================

Write-Host "🔥 اختبار النظام الفعلي v4.0 الشامل التفصيلي" -ForegroundColor Red
Write-Host "=====================================================" -ForegroundColor Yellow

# التحقق من وجود الملفات
Write-Host "🔍 التحقق من وجود الملفات المطلوبة..." -ForegroundColor Cyan

$bugBountyFile = ".\assets\modules\bugbounty\BugBountyCore.js"
$templateFile = ".\assets\modules\bugbounty\report_template.html"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "❌ ملف BugBountyCore.js غير موجود" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $templateFile)) {
    Write-Host "❌ ملف report_template.html غير موجود" -ForegroundColor Red
    exit 1
}

Write-Host "✅ جميع الملفات المطلوبة موجودة" -ForegroundColor Green

# قراءة وفحص محتوى الملفات
Write-Host "📖 فحص محتوى الملفات..." -ForegroundColor Cyan

$bugBountyContent = Get-Content $bugBountyFile -Raw -Encoding UTF8
$templateContent = Get-Content $templateFile -Raw -Encoding UTF8

# فحص التحسينات في BugBountyCore.js
Write-Host "🔍 فحص التحسينات في BugBountyCore.js:" -ForegroundColor Yellow

# فحص دالة استخراج البيانات الحقيقية
if ($bugBountyContent -match "extractRealDataFromDiscoveredVulnerability.*discoveredVuln\.parameter.*discoveredVuln\.payload.*discoveredVuln\.url") {
    Write-Host "   ✅ دالة استخراج البيانات الحقيقية محسنة" -ForegroundColor Green
} else {
    Write-Host "   ❌ دالة استخراج البيانات الحقيقية تحتاج تحسين" -ForegroundColor Red
}

# فحص دالة التأثير الديناميكي
if ($bugBountyContent -match "generateDynamicImpactForAnyVulnerability.*extractRealDataFromDiscoveredVulnerability") {
    Write-Host "   ✅ دالة التأثير الديناميكي تستخدم البيانات المستخرجة" -ForegroundColor Green
} else {
    Write-Host "   ❌ دالة التأثير الديناميكي لا تستخدم البيانات المستخرجة" -ForegroundColor Red
}

# فحص دالة استخراج المعامل
if ($bugBountyContent -match "extractParameterFromDiscoveredVulnerability.*discoveredVuln\.parameter.*discoveredVuln\.affected_parameter") {
    Write-Host "   ✅ دالة استخراج المعامل الجديدة موجودة" -ForegroundColor Green
} else {
    Write-Host "   ❌ دالة استخراج المعامل الجديدة مفقودة" -ForegroundColor Red
}

# فحص عدم وجود النصوص العامة
$genericTexts = @("معامل مكتشف", "payload_discovered", "target_discovered", "موقع مستهدف")
$foundGeneric = 0

foreach ($text in $genericTexts) {
    if ($bugBountyContent -match [regex]::Escape($text)) {
        $foundGeneric++
        Write-Host "   ⚠️ تم العثور على نص عام: $text" -ForegroundColor Yellow
    }
}

if ($foundGeneric -eq 0) {
    Write-Host "   ✅ لا توجد نصوص عامة في الكود" -ForegroundColor Green
} else {
    Write-Host "   ⚠️ تم العثور على $foundGeneric نص عام" -ForegroundColor Yellow
}

# فحص القالب الشامل
Write-Host "🔍 فحص القالب الشامل:" -ForegroundColor Yellow

$templateVariables = @("{{VULNERABILITIES_CONTENT}}", "{{TESTING_DETAILS}}", "{{INTERACTIVE_DIALOGUES}}", "{{VISUAL_CHANGES}}", "{{PERSISTENT_RESULTS}}", "{{IMPACT_VISUALIZATIONS}}")
$foundVariables = 0

foreach ($variable in $templateVariables) {
    if ($templateContent -match [regex]::Escape($variable)) {
        $foundVariables++
    }
}

Write-Host "   📊 متغيرات القالب الموجودة: $foundVariables/$($templateVariables.Count)" -ForegroundColor White

if ($foundVariables -eq $templateVariables.Count) {
    Write-Host "   ✅ القالب الشامل مكتمل" -ForegroundColor Green
} else {
    Write-Host "   ❌ القالب الشامل غير مكتمل" -ForegroundColor Red
}

# فحص الدوال الشاملة التفصيلية
Write-Host "🔍 فحص الدوال الشاملة التفصيلية:" -ForegroundColor Yellow

$comprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability", 
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealDetailedDialogueFromDiscoveredVulnerability"
)

$foundFunctions = 0
foreach ($func in $comprehensiveFunctions) {
    if ($bugBountyContent -match [regex]::Escape($func)) {
        $foundFunctions++
    }
}

Write-Host "   📊 الدوال الشاملة الموجودة: $foundFunctions/$($comprehensiveFunctions.Count)" -ForegroundColor White

if ($foundFunctions -eq $comprehensiveFunctions.Count) {
    Write-Host "   ✅ جميع الدوال الشاملة التفصيلية موجودة" -ForegroundColor Green
} else {
    Write-Host "   ❌ بعض الدوال الشاملة التفصيلية مفقودة" -ForegroundColor Red
}

# إنشاء اختبار Node.js للنظام الفعلي
Write-Host "🔧 إنشاء اختبار Node.js للنظام الفعلي..." -ForegroundColor Cyan

$nodeTestContent = @"
// 🔥 اختبار النظام الفعلي v4.0
console.log('🔥 اختبار النظام الفعلي v4.0 الشامل التفصيلي');
console.log('=================================================');

const fs = require('fs');

try {
    // قراءة وتحميل النظام
    const code = fs.readFileSync('./assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    eval(code);
    
    // إنشاء النظام
    const system = new BugBountyCore();
    console.log('✅ تم تحميل النظام بنجاح');
    
    // بيانات ثغرة حقيقية للاختبار
    const realVuln = {
        name: 'SQL Injection في صفحة البحث',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'http://testphp.vulnweb.com/search.php',
        parameter: 'searchFor',
        payload: "test' UNION SELECT user(),database(),version() --",
        tested_payload: "test' UNION SELECT user(),database(),version() --",
        response: 'MySQL error: You have an error in your SQL syntax',
        evidence: 'تم كشف معلومات قاعدة البيانات',
        exploitation_response: 'تم الوصول لقاعدة البيانات بنجاح',
        exploitation_evidence: 'تم استخراج أسماء المستخدمين وكلمات المرور'
    };
    
    console.log('📊 اختبار الثغرة:', realVuln.name);
    console.log('   المعامل:', realVuln.parameter);
    console.log('   Payload:', realVuln.payload);
    console.log('   الموقع:', realVuln.url);
    
    // اختبار استخراج البيانات الحقيقية
    console.log('🔧 اختبار استخراج البيانات الحقيقية...');
    const extractedData = system.extractRealDataFromDiscoveredVulnerability(realVuln);
    
    console.log('✅ البيانات المستخرجة:');
    console.log('   اسم الثغرة:', extractedData.vulnName);
    console.log('   الموقع:', extractedData.location);
    console.log('   المعامل:', extractedData.parameter);
    console.log('   Payload:', extractedData.payload);
    console.log('   الاستجابة:', extractedData.response);
    
    // اختبار التأثير الديناميكي
    console.log('🔧 اختبار التأثير الديناميكي...');
    const dynamicImpact = system.generateDynamicImpactForAnyVulnerability(realVuln, extractedData);
    
    console.log('✅ تم إنشاء التأثير الديناميكي');
    console.log('📊 حجم المحتوى:', dynamicImpact.length, 'حرف');
    
    // فحص المحتوى
    const hasRealData = dynamicImpact.includes(realVuln.name) || 
                       dynamicImpact.includes(realVuln.parameter) ||
                       dynamicImpact.includes(realVuln.payload);
    
    const hasGenericData = dynamicImpact.includes('معامل مكتشف') ||
                          dynamicImpact.includes('payload_discovered') ||
                          dynamicImpact.includes('target_discovered');
    
    if (hasRealData && !hasGenericData) {
        console.log('🎉 ممتاز! التأثير الديناميكي يحتوي على البيانات الحقيقية فقط');
    } else if (hasRealData && hasGenericData) {
        console.log('⚠️ التأثير الديناميكي يحتوي على بيانات حقيقية وعامة');
    } else {
        console.log('❌ التأثير الديناميكي لا يحتوي على البيانات الحقيقية');
    }
    
    // اختبار التفاصيل الشاملة
    console.log('🔧 اختبار التفاصيل الشاملة...');
    system.generateComprehensiveDetailsFromRealData(realVuln, extractedData)
        .then(comprehensiveDetails => {
            console.log('✅ تم إنشاء التفاصيل الشاملة');
            console.log('📊 عدد الأقسام:', Object.keys(comprehensiveDetails).length);
            
            // فحص التفاصيل
            const detailsString = JSON.stringify(comprehensiveDetails);
            const hasRealInDetails = detailsString.includes(realVuln.name) ||
                                   detailsString.includes(realVuln.parameter) ||
                                   detailsString.includes(realVuln.payload);
            
            if (hasRealInDetails) {
                console.log('🎉 التفاصيل الشاملة تحتوي على البيانات الحقيقية');
            } else {
                console.log('❌ التفاصيل الشاملة لا تحتوي على البيانات الحقيقية');
            }
            
            console.log('');
            console.log('🏁 نتائج الاختبار النهائية:');
            console.log('🎉 النظام الشامل التفصيلي v4.0 يعمل بالبيانات الحقيقية!');
            console.log('✅ التقارير ستحتوي على التفاصيل الحقيقية للثغرات');
            console.log('🔥 لا يوجد محتوى عام أو افتراضي');
        })
        .catch(error => {
            console.log('❌ خطأ في التفاصيل الشاملة:', error.message);
        });
    
} catch (error) {
    console.log('❌ خطأ في الاختبار:', error.message);
}
"@

# حفظ اختبار Node.js
$nodeTestFile = ".\test_real_system_v4.js"
$nodeTestContent | Out-File -FilePath $nodeTestFile -Encoding UTF8

Write-Host "✅ تم إنشاء اختبار Node.js: $nodeTestFile" -ForegroundColor Green

# تشغيل اختبار Node.js
Write-Host "🚀 تشغيل اختبار Node.js..." -ForegroundColor Cyan
try {
    $nodeResult = node $nodeTestFile 2>&1
    Write-Host $nodeResult -ForegroundColor White
} catch {
    Write-Host "❌ خطأ في تشغيل Node.js: $_" -ForegroundColor Red
}

# النتائج النهائية
Write-Host ""
Write-Host "🏁 ملخص نتائج الاختبار:" -ForegroundColor Yellow
Write-Host "=========================" -ForegroundColor Yellow

$improvements = @(
    ($bugBountyContent -match "extractRealDataFromDiscoveredVulnerability.*discoveredVuln\.parameter"),
    ($bugBountyContent -match "generateDynamicImpactForAnyVulnerability.*extractRealDataFromDiscoveredVulnerability"),
    ($bugBountyContent -match "extractParameterFromDiscoveredVulnerability"),
    ($foundGeneric -eq 0),
    ($foundVariables -eq $templateVariables.Count),
    ($foundFunctions -eq $comprehensiveFunctions.Count)
)

$successfulImprovements = ($improvements | Where-Object { $_ }).Count
$totalImprovements = $improvements.Count

Write-Host "📊 التحسينات المطبقة: $successfulImprovements/$totalImprovements" -ForegroundColor White

if ($successfulImprovements -eq $totalImprovements) {
    Write-Host "🎉🎉🎉 جميع التحسينات مطبقة بنجاح!" -ForegroundColor Green
    Write-Host "✅ النظام الشامل التفصيلي v4.0 جاهز ويعمل بالبيانات الحقيقية" -ForegroundColor Green
    Write-Host "🔥 التقارير ستحتوي على التفاصيل الحقيقية للثغرات المكتشفة والمختبرة" -ForegroundColor Green
    Write-Host "🎯 لا يوجد محتوى عام أو افتراضي" -ForegroundColor Green
} elseif ($successfulImprovements -ge ($totalImprovements * 0.8)) {
    Write-Host "🎉 معظم التحسينات مطبقة بنجاح!" -ForegroundColor Yellow
    Write-Host "✅ النظام محسن بشكل كبير" -ForegroundColor Yellow
    Write-Host "⚠️ قد تحتاج بعض التحسينات الإضافية" -ForegroundColor Yellow
} else {
    Write-Host "⚠️ التحسينات جزئية" -ForegroundColor Red
    Write-Host "❌ يحتاج المزيد من العمل" -ForegroundColor Red
}

Write-Host ""
Write-Host "🔥 اختبار النظام الفعلي v4.0 مكتمل!" -ForegroundColor Red
