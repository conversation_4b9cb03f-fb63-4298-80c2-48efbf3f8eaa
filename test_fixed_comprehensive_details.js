// 🔥 اختبار الإصلاح للتفاصيل الشاملة التفصيلية
console.log('🔥 اختبار الإصلاح للتفاصيل الشاملة التفصيلية');
console.log('==============================================');

const fs = require('fs');

async function testFixedComprehensiveDetails() {
    try {
        console.log('📖 تحميل النظام v4.0 المُصلح...');
        
        // قراءة ملف BugBountyCore المُصلح
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        const code = fs.readFileSync(filePath, 'utf8');
        
        // تنفيذ الكود
        const modifiedCode = code + `
        if (typeof module !== 'undefined' && module.exports) {
            module.exports = { BugBountyCore };
        } else if (typeof global !== 'undefined') {
            global.BugBountyCore = BugBountyCore;
        }
        `;
        
        eval(modifiedCode);
        console.log('✅ تم تحميل النظام المُصلح بنجاح');
        
        // إنشاء مثيل من النظام
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء مثيل النظام');
        
        // ثغرات تجريبية شاملة للاختبار
        const testVulnerabilities = [
            {
                name: 'SQL Injection في نظام إدارة المستخدمين',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/admin/users.php',
                parameter: 'user_id',
                payload: "1' UNION SELECT user(),database(),version() --",
                response: 'MySQL error: Database version disclosed',
                evidence: 'تم كشف إصدار قاعدة البيانات',
                location: 'http://testphp.vulnweb.com/admin/users.php',
                business_impact: 'عالي - إمكانية الوصول لجميع بيانات النظام',
                affected_components: ['قاعدة البيانات', 'نظام المصادقة']
            },
            {
                name: 'XSS المخزن في نظام التعليقات',
                type: 'Cross-Site Scripting',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/comments.php',
                parameter: 'comment_text',
                payload: '<script>alert("XSS")</script>',
                response: 'Script executed successfully',
                evidence: 'تم تنفيذ الكود JavaScript',
                location: 'http://testphp.vulnweb.com/comments.php',
                business_impact: 'متوسط إلى عالي - إمكانية سرقة جلسات المستخدمين',
                affected_components: ['نظام التعليقات', 'جلسات المستخدمين']
            }
        ];
        
        console.log('🔥 اختبار إنتاج التقرير الرئيسي مع الإصلاح...');
        
        // تطبيق جميع الدوال الـ 36 على كل ثغرة
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            // استخراج البيانات الحقيقية
            const realData = {
                url: vuln.url,
                parameter: vuln.parameter,
                payload: vuln.payload,
                response: vuln.response,
                evidence: vuln.evidence
            };
            
            // تطبيق جميع الدوال الـ 36
            vuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(vuln, realData);
            vuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(vuln, realData);
            vuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(vuln, realData);
            vuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(vuln);
            
            console.log(`✅ تم تطبيق جميع الدوال على الثغرة ${i + 1}`);
            
            // فحص نوع البيانات المُنتجة
            console.log(`🔍 نوع comprehensive_details: ${typeof vuln.comprehensive_details}`);
            console.log(`🔍 نوع dynamic_impact: ${typeof vuln.dynamic_impact}`);
            console.log(`🔍 نوع exploitation_steps: ${typeof vuln.exploitation_steps}`);
            console.log(`🔍 نوع dynamic_recommendations: ${typeof vuln.dynamic_recommendations}`);
        }
        
        // إنتاج التقرير الرئيسي
        console.log('📄 إنتاج التقرير الرئيسي مع الإصلاح...');
        const mainReport = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
        
        // حفظ التقرير
        const mainReportFile = `fixed_main_report_${Date.now()}.html`;
        fs.writeFileSync(mainReportFile, mainReport, 'utf8');
        
        console.log(`✅ تم حفظ التقرير الرئيسي المُصلح: ${mainReportFile}`);
        console.log(`📊 حجم التقرير: ${Math.round(mainReport.length / 1024)} KB`);
        
        // فحص محتوى التقرير للتأكد من الإصلاح
        console.log('🔍 فحص محتوى التقرير المُصلح...');
        
        const hasObjectObject = mainReport.includes('[object Object]');
        const hasComprehensiveContent = mainReport.includes('تحليل شامل تفصيلي للثغرة');
        const hasRealData = mainReport.includes('تفاصيل الاكتشاف الحقيقية');
        const hasExploitationSteps = mainReport.includes('خطوات الاستغلال');
        const hasImpactAnalysis = mainReport.includes('تحليل التأثير');
        
        console.log(`🔍 يحتوي على [object Object]: ${hasObjectObject ? '❌ نعم' : '✅ لا'}`);
        console.log(`🔍 يحتوي على محتوى شامل: ${hasComprehensiveContent ? '✅ نعم' : '❌ لا'}`);
        console.log(`🔍 يحتوي على بيانات حقيقية: ${hasRealData ? '✅ نعم' : '❌ لا'}`);
        console.log(`🔍 يحتوي على خطوات الاستغلال: ${hasExploitationSteps ? '✅ نعم' : '❌ لا'}`);
        console.log(`🔍 يحتوي على تحليل التأثير: ${hasImpactAnalysis ? '✅ نعم' : '❌ لا'}`);
        
        // اختبار التقرير المنفصل
        console.log('📋 اختبار التقرير المنفصل مع الإصلاح...');
        
        const pageData = {
            page_name: 'صفحة اختبار الأمان',
            page_url: 'http://testphp.vulnweb.com',
            vulnerabilities: testVulnerabilities
        };
        
        const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, 'http://testphp.vulnweb.com', 1);
        
        // حفظ التقرير المنفصل
        const separateReportFile = `fixed_separate_report_${Date.now()}.html`;
        fs.writeFileSync(separateReportFile, separateReport, 'utf8');
        
        console.log(`✅ تم حفظ التقرير المنفصل المُصلح: ${separateReportFile}`);
        console.log(`📊 حجم التقرير المنفصل: ${Math.round(separateReport.length / 1024)} KB`);
        
        // فحص التقرير المنفصل
        const separateHasObjectObject = separateReport.includes('[object Object]');
        const separateHasComprehensive = separateReport.includes('تحليل شامل تفصيلي');
        
        console.log(`🔍 التقرير المنفصل - [object Object]: ${separateHasObjectObject ? '❌ نعم' : '✅ لا'}`);
        console.log(`🔍 التقرير المنفصل - محتوى شامل: ${separateHasComprehensive ? '✅ نعم' : '❌ لا'}`);
        
        return {
            success: true,
            mainReportFile: mainReportFile,
            separateReportFile: separateReportFile,
            mainReportSize: mainReport.length,
            separateReportSize: separateReport.length,
            fixedObjectIssue: !hasObjectObject && !separateHasObjectObject,
            hasComprehensiveContent: hasComprehensiveContent && separateHasComprehensive,
            hasRealData: hasRealData,
            vulnerabilitiesProcessed: testVulnerabilities.length
        };
        
    } catch (error) {
        console.log('❌ خطأ في اختبار الإصلاح:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testFixedComprehensiveDetails().then(result => {
    console.log('');
    console.log('🏁 نتائج اختبار الإصلاح:');
    console.log('========================');
    
    if (result.success) {
        console.log('🎉 الاختبار نجح بالكامل!');
        console.log(`📄 التقرير الرئيسي: ${result.mainReportFile} (${Math.round(result.mainReportSize / 1024)} KB)`);
        console.log(`📄 التقرير المنفصل: ${result.separateReportFile} (${Math.round(result.separateReportSize / 1024)} KB)`);
        console.log(`🚨 الثغرات المعالجة: ${result.vulnerabilitiesProcessed}`);
        
        console.log('');
        console.log('✅ تأكيدات الإصلاح:');
        console.log(`  🔧 تم إصلاح مشكلة [object Object]: ${result.fixedObjectIssue ? '✅ نعم' : '❌ لا'}`);
        console.log(`  📋 يحتوي على محتوى شامل تفصيلي: ${result.hasComprehensiveContent ? '✅ نعم' : '❌ لا'}`);
        console.log(`  🔍 يحتوي على بيانات حقيقية: ${result.hasRealData ? '✅ نعم' : '❌ لا'}`);
        
        if (result.fixedObjectIssue && result.hasComprehensiveContent && result.hasRealData) {
            console.log('');
            console.log('🎉🎉🎉 الإصلاح نجح بالكامل!');
            console.log('✅ التقارير تعرض الآن التفاصيل الشاملة التفصيلية بدلاً من [object Object]');
            console.log('✅ جميع الـ 36 دالة تعمل وتنتج محتوى شامل تفصيلي');
            console.log('✅ البيانات الحقيقية تظهر في التقارير');
        } else {
            console.log('');
            console.log('⚠️ الإصلاح جزئي - قد تحتاج تحسينات إضافية');
        }
        
    } else {
        console.log('❌ الاختبار فشل:', result.error);
    }
    
    console.log('🔥 اختبار الإصلاح مكتمل!');
});
