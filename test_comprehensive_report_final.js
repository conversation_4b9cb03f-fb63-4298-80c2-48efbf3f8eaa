// 🔥 اختبار شامل لإنتاج التقرير التفصيلي v4.0 مع الصور
console.log('🔥 اختبار شامل لإنتاج التقرير التفصيلي v4.0 مع الصور');
console.log('=======================================================');

const fs = require('fs');

async function testComprehensiveReportGeneration() {
    try {
        console.log('📖 تحميل النظام الشامل التفصيلي v4.0...');
        
        // قراءة ملف BugBountyCore
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        
        if (!fs.existsSync(filePath)) {
            console.log('❌ ملف BugBountyCore.js غير موجود');
            return;
        }
        
        const code = fs.readFileSync(filePath, 'utf8');
        eval(code);
        
        console.log('✅ تم تحميل النظام بنجاح');
        
        // إنشاء النظام
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء النظام بنجاح');
        
        // بيانات ثغرات حقيقية للاختبار
        const realVulnerabilities = [
            {
                name: 'SQL Injection في صفحة تسجيل الدخول',
                type: 'SQL Injection',
                severity: 'Critical',
                url: 'http://testphp.vulnweb.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --",
                tested_payload: "admin' OR '1'='1' --",
                response: 'تم تسجيل الدخول بنجاح - تم تجاوز المصادقة',
                evidence: 'تم تسجيل الدخول بدون بيانات اعتماد صحيحة',
                exploitation_response: 'تم الوصول لقاعدة البيانات',
                exploitation_evidence: 'تم استخراج بيانات المستخدمين'
            },
            {
                name: 'XSS المخزن في حقل التعليقات',
                type: 'Cross-Site Scripting (XSS)',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/comment.php',
                parameter: 'comment',
                payload: '<script>alert("XSS Vulnerability Found")</script>',
                tested_payload: '<script>alert("XSS Vulnerability Found")</script>',
                response: 'تم حفظ التعليق مع تنفيذ الكود',
                evidence: 'ظهور نافذة تنبيه JavaScript',
                exploitation_response: 'تم تنفيذ الكود JavaScript',
                exploitation_evidence: 'تم تأكيد تنفيذ الكود في المتصفح'
            },
            {
                name: 'تجاوز المصادقة في API المستخدمين',
                type: 'Authentication Bypass',
                severity: 'Medium',
                url: 'http://testphp.vulnweb.com/api/users',
                parameter: 'token',
                payload: 'bypass_token_12345',
                tested_payload: 'bypass_token_12345',
                response: 'تم الوصول للبيانات بدون مصادقة صحيحة',
                evidence: 'إرجاع بيانات المستخدمين بدون token صالح',
                exploitation_response: 'تم الوصول لجميع بيانات المستخدمين',
                exploitation_evidence: 'تم تحميل قائمة كاملة بالمستخدمين'
            }
        ];
        
        console.log('📊 تم إعداد بيانات الثغرات الحقيقية:');
        realVulnerabilities.forEach((vuln, index) => {
            console.log(`   ${index + 1}. ${vuln.name}`);
            console.log(`      المعامل: ${vuln.parameter}`);
            console.log(`      Payload: ${vuln.payload}`);
            console.log(`      الموقع: ${vuln.url}`);
        });
        
        console.log('');
        console.log('🔧 اختبار استخراج البيانات الحقيقية...');
        
        // اختبار استخراج البيانات لكل ثغرة
        const extractedDataResults = [];
        for (let i = 0; i < realVulnerabilities.length; i++) {
            const vuln = realVulnerabilities[i];
            console.log(`🔍 معالجة الثغرة ${i + 1}: ${vuln.name}`);
            
            const extractedData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(vuln);
            extractedDataResults.push(extractedData);
            
            console.log(`   ✅ البيانات المستخرجة:`);
            console.log(`      اسم الثغرة: ${extractedData.vulnName}`);
            console.log(`      الموقع: ${extractedData.location}`);
            console.log(`      المعامل: ${extractedData.parameter}`);
            console.log(`      Payload: ${extractedData.payload}`);
        }
        
        console.log('');
        console.log('🔧 اختبار إنتاج التفاصيل الشاملة التفصيلية...');
        
        // اختبار إنتاج التفاصيل الشاملة
        const comprehensiveResults = [];
        for (let i = 0; i < realVulnerabilities.length; i++) {
            const vuln = realVulnerabilities[i];
            const extractedData = extractedDataResults[i];
            
            console.log(`📋 إنتاج التفاصيل الشاملة للثغرة ${i + 1}...`);
            
            try {
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(vuln, extractedData);
                comprehensiveResults.push(comprehensiveDetails);
                
                console.log(`   ✅ تم إنتاج التفاصيل الشاملة بنجاح`);
                console.log(`   📊 عدد الأقسام: ${Object.keys(comprehensiveDetails).length}`);
                
                // فحص المحتوى
                const detailsString = JSON.stringify(comprehensiveDetails);
                const hasRealData = detailsString.includes(vuln.name) || 
                                   detailsString.includes(vuln.parameter) ||
                                   detailsString.includes(vuln.payload);
                
                if (hasRealData) {
                    console.log(`   🎉 التفاصيل تحتوي على البيانات الحقيقية للثغرة`);
                } else {
                    console.log(`   ⚠️ التفاصيل قد لا تحتوي على البيانات الحقيقية`);
                }
                
            } catch (error) {
                console.log(`   ❌ خطأ في إنتاج التفاصيل: ${error.message}`);
                comprehensiveResults.push(null);
            }
        }
        
        console.log('');
        console.log('🔧 اختبار إنتاج التأثير الديناميكي...');
        
        // اختبار التأثير الديناميكي
        const impactResults = [];
        for (let i = 0; i < realVulnerabilities.length; i++) {
            const vuln = realVulnerabilities[i];
            const extractedData = extractedDataResults[i];
            
            console.log(`💥 إنتاج التأثير الديناميكي للثغرة ${i + 1}...`);
            
            try {
                const dynamicImpact = bugBountyCore.generateDynamicImpactForAnyVulnerability(vuln, extractedData);
                impactResults.push(dynamicImpact);
                
                console.log(`   ✅ تم إنتاج التأثير الديناميكي بنجاح`);
                console.log(`   📊 حجم المحتوى: ${dynamicImpact.length} حرف`);
                
                // فحص المحتوى
                const hasRealData = dynamicImpact.includes(vuln.name) || 
                                   dynamicImpact.includes(vuln.parameter) ||
                                   dynamicImpact.includes(vuln.payload);
                
                const hasGenericData = dynamicImpact.includes('معامل مكتشف') ||
                                      dynamicImpact.includes('payload_discovered') ||
                                      dynamicImpact.includes('target_discovered');
                
                if (hasRealData && !hasGenericData) {
                    console.log(`   🎉 التأثير يحتوي على البيانات الحقيقية فقط`);
                } else if (hasRealData && hasGenericData) {
                    console.log(`   ⚠️ التأثير يحتوي على بيانات حقيقية وعامة`);
                } else {
                    console.log(`   ❌ التأثير لا يحتوي على البيانات الحقيقية`);
                }
                
            } catch (error) {
                console.log(`   ❌ خطأ في إنتاج التأثير: ${error.message}`);
                impactResults.push(null);
            }
        }
        
        console.log('');
        console.log('📄 اختبار إنتاج التقرير النهائي الشامل...');
        
        // إعداد بيانات التحليل الشامل
        const comprehensiveAnalysis = {
            vulnerabilities: realVulnerabilities,
            total_vulnerabilities: realVulnerabilities.length,
            summary: {
                total_vulnerabilities: realVulnerabilities.length,
                critical_severity: realVulnerabilities.filter(v => v.severity === 'Critical').length,
                high_severity: realVulnerabilities.filter(v => v.severity === 'High').length,
                medium_severity: realVulnerabilities.filter(v => v.severity === 'Medium').length
            }
        };
        
        try {
            console.log('🔄 إنتاج التقرير النهائي الشامل...');
            
            const finalReport = await bugBountyCore.generateFinalComprehensiveReport(
                comprehensiveAnalysis, 
                [], 
                'http://testphp.vulnweb.com'
            );
            
            console.log('✅ تم إنتاج التقرير النهائي الشامل بنجاح!');
            console.log(`📊 حجم التقرير: ${Math.round(finalReport.length / 1024)} KB`);
            
            // فحص محتوى التقرير
            const reportHasRealData = realVulnerabilities.some(vuln => 
                finalReport.includes(vuln.name) || 
                finalReport.includes(vuln.parameter) ||
                finalReport.includes(vuln.payload)
            );
            
            const reportHasGenericData = finalReport.includes('معامل مكتشف') ||
                                        finalReport.includes('payload_discovered') ||
                                        finalReport.includes('target_discovered') ||
                                        finalReport.includes('تم اكتشاف ثغرة أمنية');
            
            const reportHasTemplate = finalReport.includes('<!DOCTYPE html>') &&
                                     finalReport.includes('تقرير Bug Bounty') &&
                                     finalReport.includes('الثغرات المكتشفة والمحللة بالكامل');
            
            console.log('');
            console.log('🔍 فحص محتوى التقرير النهائي:');
            
            if (reportHasTemplate) {
                console.log('   ✅ التقرير يستخدم القالب الشامل الصحيح');
            } else {
                console.log('   ❌ التقرير لا يستخدم القالب الشامل');
            }
            
            if (reportHasRealData) {
                console.log('   ✅ التقرير يحتوي على البيانات الحقيقية للثغرات');
            } else {
                console.log('   ❌ التقرير لا يحتوي على البيانات الحقيقية');
            }
            
            if (!reportHasGenericData) {
                console.log('   ✅ التقرير لا يحتوي على محتوى عام أو افتراضي');
            } else {
                console.log('   ⚠️ التقرير يحتوي على بعض المحتوى العام');
            }
            
            // حفظ التقرير
            const reportFileName = `comprehensive_report_v4_${Date.now()}.html`;
            fs.writeFileSync(reportFileName, finalReport, 'utf8');
            console.log(`💾 تم حفظ التقرير: ${reportFileName}`);
            
            // عرض عينة من التقرير
            console.log('');
            console.log('📋 عينة من التقرير النهائي:');
            console.log('=====================================');
            const reportSample = finalReport.substring(0, 1000) + '...';
            console.log(reportSample);
            
            console.log('');
            console.log('🏁 نتائج الاختبار النهائية:');
            console.log('============================');
            
            if (reportHasTemplate && reportHasRealData && !reportHasGenericData) {
                console.log('🎉🎉🎉 نجح الاختبار بالكامل!');
                console.log('✅ النظام الشامل التفصيلي v4.0 يعمل بالبيانات الحقيقية');
                console.log('✅ التقرير يستخدم القالب الشامل الموجود في المشروع');
                console.log('✅ التقرير يحتوي على التفاصيل الحقيقية للثغرات المكتشفة والمختبرة');
                console.log('✅ لا يوجد محتوى عام أو افتراضي');
                console.log('🔥 التعديلات تعمل بشكل مثالي!');
            } else {
                console.log('⚠️ الاختبار جزئي النجاح');
                console.log(`   القالب الشامل: ${reportHasTemplate ? '✅' : '❌'}`);
                console.log(`   البيانات الحقيقية: ${reportHasRealData ? '✅' : '❌'}`);
                console.log(`   عدم وجود محتوى عام: ${!reportHasGenericData ? '✅' : '❌'}`);
            }
            
        } catch (error) {
            console.log('❌ خطأ في إنتاج التقرير النهائي:', error.message);
        }
        
    } catch (error) {
        console.log('❌ خطأ في الاختبار:', error.message);
    }
}

// تشغيل الاختبار
testComprehensiveReportGeneration();
