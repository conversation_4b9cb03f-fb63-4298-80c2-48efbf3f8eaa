// 🔥 اختبار إصلاح النظام الشامل التفصيلي v4.0

console.log('🔥 اختبار إصلاح النظام الشامل التفصيلي v4.0');
console.log('==============================================');

const fs = require('fs');

try {
    // قراءة ملف BugBountyCore
    const filePath = './assets/modules/bugbounty/BugBountyCore.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ ملف BugBountyCore.js غير موجود');
        process.exit(1);
    }
    
    console.log('✅ ملف BugBountyCore.js موجود');
    
    // قراءة المحتوى
    const code = fs.readFileSync(filePath, 'utf8');
    console.log('✅ تم قراءة الملف بنجاح');
    
    // تنفيذ الكود
    eval(code);
    console.log('✅ تم تحميل الكود بنجاح');
    
    // إنشاء النظام
    const bugBountyCore = new BugBountyCore();
    console.log('✅ تم إنشاء النظام بنجاح');
    
    // بيانات ثغرة اختبار حقيقية
    const testVuln = {
        name: 'API Authentication Bypass',
        type: 'Authentication Bypass',
        severity: 'Medium',
        url: 'http://testphp.vulnweb.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        tested_payload: "admin' OR '1'='1' --",
        response: 'Login successful - Authentication bypassed',
        evidence: 'Successfully logged in without valid credentials',
        description: 'Authentication bypass vulnerability in login form'
    };
    
    console.log('📊 بيانات الثغرة الحقيقية:');
    console.log('   الاسم:', testVuln.name);
    console.log('   النوع:', testVuln.type);
    console.log('   المعامل:', testVuln.parameter);
    console.log('   Payload:', testVuln.payload);
    console.log('   الموقع:', testVuln.url);
    
    // اختبار استخراج البيانات الحقيقية
    console.log('🔧 اختبار استخراج البيانات الحقيقية...');
    
    const extractedData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);
    
    console.log('✅ البيانات المستخرجة:');
    console.log('   اسم الثغرة:', extractedData.vulnName);
    console.log('   الموقع:', extractedData.location);
    console.log('   المعامل:', extractedData.parameter);
    console.log('   Payload:', extractedData.payload);
    console.log('   الاستجابة:', extractedData.response);
    console.log('   الأدلة:', extractedData.evidence);
    
    // اختبار الدالة الشاملة التفصيلية
    console.log('🔧 اختبار الدالة الشاملة التفصيلية...');
    
    const dynamicImpact = bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, extractedData);
    
    console.log('✅ التأثير الديناميكي تم إنشاؤه بنجاح');
    console.log('📊 حجم المحتوى:', dynamicImpact.length, 'حرف');
    
    // فحص المحتوى للتأكد من عدم وجود نصوص عامة
    const hasGenericContent = 
        dynamicImpact.includes('معامل مكتشف') ||
        dynamicImpact.includes('payload_discovered') ||
        dynamicImpact.includes('target_discovered') ||
        dynamicImpact.includes('موقع مستهدف');
    
    const hasRealContent = 
        dynamicImpact.includes(testVuln.name) ||
        dynamicImpact.includes(testVuln.parameter) ||
        dynamicImpact.includes(testVuln.payload) ||
        dynamicImpact.includes(testVuln.url);
    
    if (!hasGenericContent) {
        console.log('🎉 ممتاز! لا يوجد محتوى عام في التأثير الديناميكي');
    } else {
        console.log('❌ لا يزال يوجد محتوى عام في التأثير الديناميكي');
    }
    
    if (hasRealContent) {
        console.log('🎉 ممتاز! التأثير الديناميكي يحتوي على البيانات الحقيقية');
    } else {
        console.log('❌ التأثير الديناميكي لا يحتوي على البيانات الحقيقية');
    }
    
    // عرض جزء من المحتوى للفحص
    console.log('📋 عينة من التأثير الديناميكي:');
    console.log(dynamicImpact.substring(0, 500) + '...');
    
    // اختبار التقرير الشامل
    console.log('📊 اختبار إنشاء التقرير الشامل...');
    
    bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, extractedData)
        .then(comprehensiveDetails => {
            if (comprehensiveDetails && typeof comprehensiveDetails === 'object') {
                console.log('✅ التفاصيل الشاملة تم إنشاؤها بنجاح');
                console.log('📊 عدد الأقسام:', Object.keys(comprehensiveDetails).length);
                
                // فحص المحتوى الشامل
                const detailsString = JSON.stringify(comprehensiveDetails);
                const hasGenericInDetails = 
                    detailsString.includes('معامل مكتشف') ||
                    detailsString.includes('payload_discovered') ||
                    detailsString.includes('target_discovered');
                
                const hasRealInDetails = 
                    detailsString.includes(testVuln.name) ||
                    detailsString.includes(testVuln.parameter) ||
                    detailsString.includes(testVuln.payload);
                
                if (!hasGenericInDetails) {
                    console.log('🎉 ممتاز! التفاصيل الشاملة لا تحتوي على محتوى عام');
                } else {
                    console.log('❌ التفاصيل الشاملة تحتوي على محتوى عام');
                }
                
                if (hasRealInDetails) {
                    console.log('🎉 ممتاز! التفاصيل الشاملة تحتوي على البيانات الحقيقية');
                } else {
                    console.log('❌ التفاصيل الشاملة لا تحتوي على البيانات الحقيقية');
                }
                
                // النتائج النهائية
                console.log('');
                console.log('🏁 نتائج الاختبار النهائية:');
                
                if (!hasGenericContent && !hasGenericInDetails && hasRealContent && hasRealInDetails) {
                    console.log('🎉🎉🎉 نجح الإصلاح! النظام الشامل التفصيلي v4.0 يعمل بالبيانات الحقيقية!');
                    console.log('✅ التقارير ستحتوي على التفاصيل الحقيقية للثغرات المكتشفة والمختبرة');
                    console.log('🔥 لا يوجد محتوى عام أو افتراضي');
                } else {
                    console.log('⚠️ الإصلاح جزئي - يحتاج مراجعة إضافية');
                    console.log('❌ لا يزال يوجد محتوى عام أو البيانات الحقيقية مفقودة');
                }
                
            } else {
                console.log('❌ فشل في إنشاء التفاصيل الشاملة');
            }
        })
        .catch(error => {
            console.log('❌ خطأ في اختبار التفاصيل الشاملة:', error.message);
        });
        
} catch (error) {
    console.log('❌ خطأ في الاختبار:', error.message);
    process.exit(1);
}
