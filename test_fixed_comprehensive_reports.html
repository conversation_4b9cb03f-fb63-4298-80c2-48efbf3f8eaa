<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>اختبار التقارير الشاملة التفصيلية المُصلحة v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
        .header { background: #dc3545; color: white; padding: 20px; border-radius: 10px; text-align: center; margin-bottom: 20px; }
        .results { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; min-height: 300px; }
        .log { margin: 5px 0; padding: 8px; border-left: 3px solid #007bff; background: #f8f9fa; font-family: monospace; }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        button { background: #dc3545; color: white; padding: 15px 30px; border: none; border-radius: 8px; font-size: 16px; cursor: pointer; margin: 10px; }
        button:hover { background: #c82333; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار التقارير الشاملة التفصيلية المُصلحة v4.0</h1>
            <p>اختبار التقرير الرئيسي والتقارير المنفصلة مع جميع الـ 36 دالة</p>
        </div>
        
        <button onclick="testMainReport()">🚀 اختبار التقرير الرئيسي</button>
        <button onclick="testSeparateReport()">📋 اختبار التقرير المنفصل</button>
        <button onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div class="results" id="results">
            <h3>📊 نتائج الاختبار:</h3>
            <p>اضغط على الأزرار لبدء الاختبار...</p>
        </div>
    </div>

    <!-- تحميل النظام v4.0 -->
    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script src="./assets/modules/bugbounty/impact_visualizer.js"></script>
    <script src="./assets/modules/bugbounty/textual_impact_analyzer.js"></script>

    <script>
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            document.getElementById('results').appendChild(logEntry);
            console.log(`[${timestamp}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '<h3>📊 نتائج الاختبار:</h3>';
        }

        async function testMainReport() {
            clearResults();
            log('🔥 بدء اختبار التقرير الرئيسي الشامل التفصيلي...', 'info');
            
            try {
                // إنشاء النظام
                const bugBountyCore = new BugBountyCore();
                log('✅ تم تحميل النظام v4.0', 'success');
                
                // إنشاء ثغرة تجريبية
                const testVuln = {
                    name: 'SQL Injection في نظام إدارة المستخدمين',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://testphp.vulnweb.com/admin/users.php',
                    parameter: 'user_id',
                    payload: "1' UNION SELECT user(),database(),version() --",
                    response: 'MySQL error: You have an error in your SQL syntax; Database version: MySQL 5.7.31',
                    evidence: 'Database version disclosure detected',
                    confidence_level: 95
                };
                
                log('📊 إنشاء التقرير الرئيسي باستخدام جميع الـ 36 دالة...', 'info');
                const mainReport = await bugBountyCore.generateVulnerabilitiesHTML([testVuln]);
                
                // فحص المحتوى
                const hasComprehensive = mainReport.includes('التفاصيل الشاملة التفصيلية');
                const hasExploitation = mainReport.includes('خطوات الاستغلال الشاملة التفصيلية');
                const hasImpact = mainReport.includes('تحليل التأثير الشامل التفصيلي');
                const hasRecommendations = mainReport.includes('التوصيات الشاملة التفصيلية');
                const hasRealData = mainReport.includes(testVuln.payload);
                const hasTemplate = mainReport.includes('comprehensive-section');
                
                log(`📊 حجم التقرير: ${(mainReport.length / 1024).toFixed(1)} KB`, 'info');
                log(`📋 التفاصيل الشاملة: ${hasComprehensive ? '✅ موجودة' : '❌ مفقودة'}`, hasComprehensive ? 'success' : 'error');
                log(`🎯 خطوات الاستغلال: ${hasExploitation ? '✅ موجودة' : '❌ مفقودة'}`, hasExploitation ? 'success' : 'error');
                log(`💥 تحليل التأثير: ${hasImpact ? '✅ موجود' : '❌ مفقود'}`, hasImpact ? 'success' : 'error');
                log(`✅ التوصيات: ${hasRecommendations ? '✅ موجودة' : '❌ مفقودة'}`, hasRecommendations ? 'success' : 'error');
                log(`🔍 البيانات الحقيقية: ${hasRealData ? '✅ موجودة' : '❌ مفقودة'}`, hasRealData ? 'success' : 'error');
                log(`🎨 القالب الشامل: ${hasTemplate ? '✅ مستخدم' : '❌ غير مستخدم'}`, hasTemplate ? 'success' : 'error');
                
                // حفظ التقرير
                const blob = new Blob([mainReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `main_comprehensive_report_fixed_${Date.now()}.html`;
                a.click();
                
                log('🎉 تم إنشاء وحفظ التقرير الرئيسي بنجاح!', 'success');
                
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
            }
        }

        async function testSeparateReport() {
            clearResults();
            log('🔥 بدء اختبار التقرير المنفصل الشامل التفصيلي...', 'info');
            
            try {
                // إنشاء النظام
                const bugBountyCore = new BugBountyCore();
                log('✅ تم تحميل النظام v4.0', 'success');
                
                // إنشاء بيانات صفحة تجريبية
                const pageData = {
                    page_name: 'صفحة اختبار الأمان',
                    page_url: 'http://testphp.vulnweb.com/admin/users.php',
                    vulnerabilities: [{
                        name: 'XSS Reflected في نموذج البحث',
                        type: 'XSS',
                        severity: 'High',
                        url: 'http://testphp.vulnweb.com/search.php',
                        parameter: 'query',
                        payload: '<script>alert("XSS Confirmed")</script>',
                        response: 'تم عرض الـ script في الصفحة',
                        evidence: 'تم تنفيذ الكود JavaScript',
                        confidence_level: 90
                    }]
                };
                
                log('📋 إنشاء التقرير المنفصل باستخدام جميع الـ 36 دالة...', 'info');
                const separateReport = await bugBountyCore.generatePageHTMLReport(pageData, pageData.page_url, 1);
                
                // فحص المحتوى
                const hasComprehensive = separateReport.includes('التفاصيل الشاملة التفصيلية');
                const hasExploitation = separateReport.includes('خطوات الاستغلال الشاملة التفصيلية');
                const hasImpact = separateReport.includes('تحليل التأثير الشامل التفصيلي');
                const hasRecommendations = separateReport.includes('التوصيات الشاملة التفصيلية');
                const hasRealData = separateReport.includes(pageData.vulnerabilities[0].payload);
                const hasTemplate = separateReport.includes('comprehensive-section');
                const has36Functions = separateReport.includes('36 دالة الشاملة التفصيلية');
                
                log(`📊 حجم التقرير: ${(separateReport.length / 1024).toFixed(1)} KB`, 'info');
                log(`📋 التفاصيل الشاملة: ${hasComprehensive ? '✅ موجودة' : '❌ مفقودة'}`, hasComprehensive ? 'success' : 'error');
                log(`🎯 خطوات الاستغلال: ${hasExploitation ? '✅ موجودة' : '❌ مفقودة'}`, hasExploitation ? 'success' : 'error');
                log(`💥 تحليل التأثير: ${hasImpact ? '✅ موجود' : '❌ مفقود'}`, hasImpact ? 'success' : 'error');
                log(`✅ التوصيات: ${hasRecommendations ? '✅ موجودة' : '❌ مفقودة'}`, hasRecommendations ? 'success' : 'error');
                log(`🔍 البيانات الحقيقية: ${hasRealData ? '✅ موجودة' : '❌ مفقودة'}`, hasRealData ? 'success' : 'error');
                log(`🎨 القالب الشامل: ${hasTemplate ? '✅ مستخدم' : '❌ غير مستخدم'}`, hasTemplate ? 'success' : 'error');
                log(`🔥 الـ 36 دالة: ${has36Functions ? '✅ مذكورة' : '❌ غير مذكورة'}`, has36Functions ? 'success' : 'error');
                
                // حفظ التقرير
                const blob = new Blob([separateReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `separate_comprehensive_report_fixed_${Date.now()}.html`;
                a.click();
                
                log('🎉 تم إنشاء وحفظ التقرير المنفصل بنجاح!', 'success');
                
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير المنفصل: ${error.message}`, 'error');
            }
        }

        // تحميل تلقائي
        window.onload = function() {
            log('🔥 صفحة اختبار التقارير الشاملة التفصيلية المُصلحة جاهزة', 'info');
            log('📊 اضغط على الأزرار لاختبار التقارير', 'info');
        };
    </script>
</body>
</html>
