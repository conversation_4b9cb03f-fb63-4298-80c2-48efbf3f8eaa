<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>اختبار التصدير الفعلي - Bug Bounty v4.0</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        button { padding: 12px 24px; margin: 10px; font-size: 16px; border: none; border-radius: 5px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .log { background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; font-family: monospace; max-height: 400px; overflow-y: auto; border: 1px solid #dee2e6; }
        .progress { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-bar { height: 100%; background: #007bff; transition: width 0.3s ease; }
        .status { font-weight: bold; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 اختبار التصدير الفعلي - Bug Bounty v4.0</h1>
        <p>هذا الاختبار سيحاكي عملية التصدير الفعلية للتأكد من حل مشكلة Maximum call stack size exceeded</p>

        <div class="test-section info">
            <h3>🎯 خطوات الاختبار:</h3>
            <ol>
                <li>تحميل BugBountyCore.js</li>
                <li>إنشاء بيانات اختبار واقعية</li>
                <li>محاكاة المراحل الأربع للتصدير</li>
                <li>اختبار generatePageHTMLReport مباشرة</li>
                <li>قياس الأداء والذاكرة</li>
            </ol>
        </div>

        <div class="test-section">
            <button class="btn-primary" onclick="startFullExportTest()">🚀 بدء اختبار التصدير الكامل</button>
            <button class="btn-success" onclick="testGeneratePageHTMLOnly()">📄 اختبار generatePageHTMLReport فقط</button>
            <button class="btn-danger" onclick="clearResults()">🗑️ مسح النتائج</button>
        </div>

        <div id="progress-section" style="display: none;">
            <div class="status" id="current-status">جاري التحضير...</div>
            <div class="progress">
                <div class="progress-bar" id="progress-bar" style="width: 0%"></div>
            </div>
        </div>

        <div id="results"></div>
        <div id="log" class="log" style="display: none;"></div>
    </div>

    <script src="./assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testStartTime;
        let logDiv = document.getElementById('log');
        let resultsDiv = document.getElementById('results');
        let progressSection = document.getElementById('progress-section');
        let currentStatus = document.getElementById('current-status');
        let progressBar = document.getElementById('progress-bar');

        // تسجيل جميع الأخطاء
        window.addEventListener('error', function(e) {
            addLog(`❌ خطأ JavaScript: ${e.message}`);
            if (e.message.includes('Maximum call stack size exceeded')) {
                addResult('🚨 تم اكتشاف Maximum call stack size exceeded! الإصلاحات لم تعمل!', 'error');
                updateProgress(100, 'فشل - Maximum call stack size exceeded');
            }
        });

        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-section ${type}`;
            resultDiv.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function addLog(message) {
            logDiv.style.display = 'block';
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `${new Date().toLocaleTimeString()}: ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateProgress(percent, status) {
            progressSection.style.display = 'block';
            progressBar.style.width = percent + '%';
            currentStatus.textContent = status;
        }

        function clearResults() {
            resultsDiv.innerHTML = '';
            logDiv.innerHTML = '';
            logDiv.style.display = 'none';
            progressSection.style.display = 'none';
        }

        function createTestData() {
            return {
                vulnerabilities: [
                    {
                        name: 'SQL Injection في صفحة تسجيل الدخول',
                        type: 'SQL Injection',
                        severity: 'Critical',
                        description: 'ثغرة SQL Injection في معامل username',
                        payload: "admin' OR '1'='1' --",
                        url: 'https://example.com/login.php',
                        parameter: 'username',
                        method: 'POST',
                        response: 'تم تسجيل الدخول بنجاح',
                        evidence: 'تم الوصول للوحة الإدارة',
                        confidence_level: 95,
                        extracted_real_data: {
                            payload: "admin' OR '1'='1' --",
                            url: 'https://example.com/login.php',
                            parameter: 'username',
                            response: 'تم تسجيل الدخول بنجاح'
                        },
                        comprehensive_details: 'تفاصيل شاملة لثغرة SQL Injection',
                        dynamic_impact: 'تأثير خطير - إمكانية الوصول لقاعدة البيانات',
                        exploitation_steps: 'خطوات الاستغلال المفصلة',
                        dynamic_recommendations: 'توصيات الإصلاح العاجل'
                    },
                    {
                        name: 'XSS Reflected في البحث',
                        type: 'XSS',
                        severity: 'High',
                        description: 'ثغرة XSS في صفحة البحث',
                        payload: '<script>alert("XSS")</script>',
                        url: 'https://example.com/search.php',
                        parameter: 'q',
                        method: 'GET',
                        response: 'تم تنفيذ الكود',
                        evidence: 'ظهور نافذة alert',
                        confidence_level: 90,
                        extracted_real_data: {
                            payload: '<script>alert("XSS")</script>',
                            url: 'https://example.com/search.php',
                            parameter: 'q',
                            response: 'تم تنفيذ الكود'
                        },
                        comprehensive_details: 'تفاصيل شاملة لثغرة XSS',
                        dynamic_impact: 'تأثير عالي - إمكانية سرقة الجلسات',
                        exploitation_steps: 'خطوات استغلال XSS',
                        dynamic_recommendations: 'توصيات تنظيف المدخلات'
                    }
                ]
            };
        }

        async function testGeneratePageHTMLOnly() {
            addResult('🔄 بدء اختبار generatePageHTMLReport فقط...', 'info');
            testStartTime = Date.now();
            updateProgress(0, 'تحضير الاختبار...');

            try {
                // تحميل BugBountyCore
                updateProgress(10, 'تحميل BugBountyCore...');
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                    addLog('تم إنشاء مثيل BugBountyCore');
                }

                // إنشاء بيانات الاختبار
                updateProgress(20, 'إنشاء بيانات الاختبار...');
                const testData = createTestData();
                addLog(`تم إنشاء بيانات اختبار مع ${testData.vulnerabilities.length} ثغرة`);

                // اختبار generatePageHTMLReport مع timeout
                updateProgress(30, 'اختبار generatePageHTMLReport...');
                addLog('بدء استدعاء generatePageHTMLReport...');

                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Timeout after 30 seconds')), 30000);
                });

                const testPromise = bugBountyCore.generatePageHTMLReport(testData, 'https://example.com', 1);

                const result = await Promise.race([testPromise, timeoutPromise]);

                const duration = Date.now() - testStartTime;
                updateProgress(100, 'اكتمل بنجاح!');

                if (result && result.length > 100) {
                    addResult(`✅ نجح اختبار generatePageHTMLReport!`, 'success');
                    addResult(`📊 حجم HTML المُنتج: ${(result.length / 1024).toFixed(1)} KB`, 'success');
                    addResult(`⏱️ وقت التنفيذ: ${duration}ms`, 'success');
                    addResult(`🎉 تم حل مشكلة Maximum call stack size exceeded!`, 'success');
                    
                    // عرض جزء من HTML المُنتج
                    const preview = result.substring(0, 500) + '...';
                    addLog(`معاينة HTML المُنتج: ${preview}`);
                } else {
                    addResult('❌ فشل الاختبار - HTML فارغ أو قصير جداً', 'error');
                }

            } catch (error) {
                const duration = Date.now() - testStartTime;
                updateProgress(100, 'فشل الاختبار');
                
                addResult(`❌ فشل اختبار generatePageHTMLReport: ${error.message}`, 'error');
                addResult(`⏱️ وقت الفشل: ${duration}ms`, 'error');
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 المشكلة لا تزال موجودة! الإصلاحات لم تعمل!', 'error');
                } else if (error.message.includes('Timeout')) {
                    addResult('⏰ انتهت مهلة الاختبار - قد تكون هناك حلقة لا نهائية', 'warning');
                } else {
                    addResult('ℹ️ خطأ مختلف - قد تكون المشكلة في مكان آخر', 'warning');
                }
                
                addLog(`تفاصيل الخطأ: ${error.stack || error.message}`);
            }
        }

        async function startFullExportTest() {
            addResult('🚀 بدء اختبار التصدير الكامل...', 'info');
            testStartTime = Date.now();
            updateProgress(0, 'بدء الاختبار الكامل...');

            try {
                // المرحلة 1: تحضير البيانات
                updateProgress(10, 'المرحلة 1/4: تحضير البيانات...');
                addLog('💾 المرحلة 1/4: تحضير البيانات...');
                
                if (!bugBountyCore) {
                    bugBountyCore = new BugBountyCore();
                }
                
                const testData = createTestData();
                const vulnerabilitiesCount = testData.vulnerabilities.length;
                addLog(`📊 عدد الثغرات للتصدير: ${vulnerabilitiesCount}`);

                // المرحلة 2: إنشاء تقرير HTML
                updateProgress(25, 'المرحلة 2/4: إنشاء تقرير HTML...');
                addLog('📄 المرحلة 2/4: إنشاء تقرير HTML...');
                
                const htmlStartTime = Date.now();
                const pageReport = await bugBountyCore.generatePageHTMLReport(testData, 'https://example.com', 1);
                const htmlDuration = Date.now() - htmlStartTime;
                
                addLog(`✅ تم إنشاء تقرير HTML بحجم ${pageReport.length} حرف في ${htmlDuration}ms`);

                // المرحلة 3: إنشاء ملف التحميل
                updateProgress(60, 'المرحلة 3/4: إنشاء ملف التحميل...');
                addLog('💾 المرحلة 3/4: إنشاء ملف التحميل...');
                
                const domain = 'example_com';
                const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
                const filename = `Bug_Bounty_Test_${domain}_${timestamp}.html`;
                addLog(`📁 اسم الملف: ${filename}`);

                const blob = new Blob([pageReport], { type: 'text/html' });
                const url = URL.createObjectURL(blob);
                addLog(`🔗 تم إنشاء رابط التحميل`);

                // المرحلة 4: تحميل التقرير
                updateProgress(80, 'المرحلة 4/4: تحميل التقرير...');
                addLog('⬇️ المرحلة 4/4: تحميل التقرير...');
                
                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                a.click();
                addLog(`🖱️ تم تشغيل التحميل`);

                // تنظيف الذاكرة
                setTimeout(() => URL.revokeObjectURL(url), 1000);
                addLog(`🧹 تم تنظيف الذاكرة`);

                const totalDuration = Date.now() - testStartTime;
                updateProgress(100, 'اكتمل بنجاح!');

                addResult(`🎉 نجح اختبار التصدير الكامل!`, 'success');
                addResult(`📁 الملف: ${filename}`, 'success');
                addResult(`📊 الثغرات: ${vulnerabilitiesCount}`, 'success');
                addResult(`📄 الحجم: ${(pageReport.length / 1024).toFixed(1)} KB`, 'success');
                addResult(`⏱️ الوقت الإجمالي: ${totalDuration}ms`, 'success');
                addResult(`⏱️ وقت إنشاء HTML: ${htmlDuration}ms`, 'success');
                addResult(`✅ تم حل مشكلة Maximum call stack size exceeded بالكامل!`, 'success');

            } catch (error) {
                const totalDuration = Date.now() - testStartTime;
                updateProgress(100, 'فشل التصدير');
                
                addResult(`❌ فشل اختبار التصدير الكامل: ${error.message}`, 'error');
                addResult(`⏱️ وقت الفشل: ${totalDuration}ms`, 'error');
                
                if (error.message.includes('Maximum call stack size exceeded')) {
                    addResult('🚨 المشكلة لا تزال موجودة! الإصلاحات لم تعمل!', 'error');
                } else {
                    addResult('ℹ️ خطأ مختلف - قد تكون المشكلة في مكان آخر', 'warning');
                }
                
                addLog(`تفاصيل الخطأ: ${error.stack || error.message}`);
            }
        }

        // تشغيل اختبار أولي عند تحميل الصفحة
        window.onload = function() {
            addResult('🚀 تم تحميل صفحة اختبار التصدير الفعلي بنجاح', 'success');
            addLog('تم تحميل صفحة اختبار التصدير الفعلي');
            addResult('ℹ️ اضغط على أحد الأزرار لبدء الاختبار', 'info');
        };
    </script>
</body>
</html>
