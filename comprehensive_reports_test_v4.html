<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار التقارير الشاملة التفصيلية v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
            direction: rtl;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .info { border-left-color: #17a2b8; background: #d1ecf1; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .report-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار التقارير الشاملة التفصيلية v4.0</h1>
            <p>التحقق من أن جميع التقارير تنتج التفاصيل الشاملة التفصيلية حسب الثغرة المكتشفة والمختبرة تلقائياً وديناميكياً</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📊 حالة الاختبار</h3>
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <p id="status">جاهز للبدء...</p>
            </div>

            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number" id="totalTests">0</div>
                    <div>إجمالي الاختبارات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="passedTests">0</div>
                    <div>الاختبارات الناجحة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="successRate">0%</div>
                    <div>معدل النجاح</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="reportsGenerated">0</div>
                    <div>التقارير المُنشأة</div>
                </div>
            </div>

            <div class="test-section">
                <h3>🎯 الاختبارات الشاملة</h3>
                <button class="btn" onclick="runComprehensiveTest()">🚀 تشغيل الاختبار الشامل</button>
                <button class="btn" onclick="testMainReport()">📊 اختبار التقرير الرئيسي</button>
                <button class="btn" onclick="testSeparateReports()">📋 اختبار التقارير المنفصلة</button>
                <button class="btn" onclick="testComprehensiveFunctions()">🔧 اختبار الدوال الشاملة</button>
                <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
            </div>

            <div class="test-section">
                <h3>📋 نتائج الاختبار</h3>
                <div id="results" class="result"></div>
            </div>

            <div class="test-section" id="reportPreviewSection" style="display: none;">
                <h3>📄 معاينة التقرير</h3>
                <div id="reportPreview" class="report-preview"></div>
            </div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let totalTests = 0;
        let passedTests = 0;
        let reportsGenerated = 0;
        let bugBountyCore;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : type === 'success' ? 'success' : 'info';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('successRate').textContent = totalTests > 0 ? Math.round(passedTests / totalTests * 100) + '%' : '0%';
            document.getElementById('reportsGenerated').textContent = reportsGenerated;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('reportPreviewSection').style.display = 'none';
            totalTests = 0;
            passedTests = 0;
            reportsGenerated = 0;
            updateStats();
        }

        function showReportPreview(reportContent, title) {
            const previewSection = document.getElementById('reportPreviewSection');
            const previewDiv = document.getElementById('reportPreview');
            
            previewDiv.innerHTML = `<h4>${title}</h4><pre>${reportContent.substring(0, 3000)}${reportContent.length > 3000 ? '\n\n... (تم اقتطاع المحتوى)' : ''}</pre>`;
            previewSection.style.display = 'block';
        }

        async function initializeSystem() {
            try {
                log('🔄 تهيئة النظام...', 'info');
                bugBountyCore = new BugBountyCore();
                log('✅ تم تهيئة النظام بنجاح', 'success');
                return true;
            } catch (error) {
                log(`❌ فشل في تهيئة النظام: ${error.message}`, 'error');
                return false;
            }
        }

        function createTestVulnerabilities() {
            return [
                {
                    name: 'SQL Injection in Login Form',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'http://example.com/login.php',
                    location: 'http://example.com/login.php?username=admin',
                    payload: "admin' OR '1'='1' --",
                    evidence: 'Database error revealed: MySQL syntax error',
                    description: 'Critical SQL injection vulnerability in login form',
                    parameter: 'username',
                    method: 'POST',
                    response: 'MySQL Error: You have an error in your SQL syntax'
                },
                {
                    name: 'Cross-Site Scripting (XSS)',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://example.com/search.php',
                    location: 'http://example.com/search.php?q=test',
                    payload: '<script>alert("XSS")</script>',
                    evidence: 'Script executed successfully in browser',
                    description: 'Reflected XSS vulnerability in search functionality',
                    parameter: 'q',
                    method: 'GET',
                    response: 'Script tag rendered without encoding'
                },
                {
                    name: 'Local File Inclusion (LFI)',
                    type: 'LFI',
                    severity: 'High',
                    url: 'http://example.com/view.php',
                    location: 'http://example.com/view.php?file=../../../etc/passwd',
                    payload: '../../../etc/passwd',
                    evidence: 'System file contents exposed',
                    description: 'Local file inclusion vulnerability allows reading system files',
                    parameter: 'file',
                    method: 'GET',
                    response: 'root:x:0:0:root:/root:/bin/bash'
                }
            ];
        }

        async function testComprehensiveFunctions() {
            log('🔧 اختبار الدوال الشاملة التفصيلية...', 'info');
            totalTests++;

            try {
                const testVuln = createTestVulnerabilities()[0];
                const testRealData = {
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence,
                    url: testVuln.url
                };

                // اختبار الدالة الرئيسية
                const comprehensiveDetails = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);

                if (comprehensiveDetails && typeof comprehensiveDetails === 'object' && Object.keys(comprehensiveDetails).length > 5) {
                    log('✅ الدالة الشاملة التفصيلية تعمل بشكل صحيح', 'success');
                    log(`📊 النتيجة تحتوي على ${Object.keys(comprehensiveDetails).length} قسم رئيسي`, 'info');
                    
                    // فحص الأقسام المطلوبة
                    const requiredSections = ['technical_details', 'impact_analysis', 'exploitation_results', 'recommendations'];
                    let foundSections = 0;
                    
                    for (const section of requiredSections) {
                        if (comprehensiveDetails[section]) {
                            log(`✅ القسم ${section} موجود`, 'success');
                            foundSections++;
                        } else {
                            log(`❌ القسم ${section} مفقود`, 'error');
                        }
                    }
                    
                    if (foundSections === requiredSections.length) {
                        passedTests++;
                        return true;
                    } else {
                        log(`⚠️ ${foundSections}/${requiredSections.length} أقسام موجودة`, 'warning');
                        return false;
                    }
                } else {
                    log('❌ الدالة الشاملة التفصيلية لا تعمل بشكل صحيح', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الدوال الشاملة: ${error.message}`, 'error');
                return false;
            }
        }

        async function testMainReport() {
            log('📊 اختبار التقرير الرئيسي...', 'info');
            totalTests++;

            try {
                const testVulns = createTestVulnerabilities();
                const testAnalysis = {
                    vulnerabilities: testVulns,
                    summary: {
                        total_vulnerabilities: testVulns.length,
                        critical_severity: 1,
                        high_severity: 2
                    }
                };

                log('📝 إنشاء التقرير الرئيسي...', 'info');
                const mainReport = await bugBountyCore.generateFinalComprehensiveReport(testAnalysis, [], 'http://example.com');

                if (mainReport && typeof mainReport === 'string' && mainReport.length > 5000) {
                    log(`✅ التقرير الرئيسي تم إنشاؤه (${(mainReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const comprehensiveIndicators = [
                        'الوصف الشامل التفصيلي',
                        'خطوات الاستغلال الشاملة التفصيلية',
                        'تحليل التأثير الشامل التفصيلي',
                        'التوصيات الشاملة التفصيلية',
                        'النظام الشامل التفصيلي v4.0'
                    ];
                    
                    const genericContent = [
                        'تم اكتشاف ثغرة أمنية',
                        'قد يؤثر على أمان الموقع',
                        'يُنصح بإصلاح هذه الثغرة',
                        'ثغرة مكتشفة تحتاج مراجعة',
                        'حدث خطأ في تحميل التفاصيل الشاملة'
                    ];
                    
                    let comprehensiveCount = 0;
                    let genericCount = 0;
                    
                    for (const indicator of comprehensiveIndicators) {
                        if (mainReport.includes(indicator)) {
                            comprehensiveCount++;
                        }
                    }
                    
                    for (const generic of genericContent) {
                        if (mainReport.includes(generic)) {
                            genericCount++;
                        }
                    }
                    
                    log(`📊 مؤشرات المحتوى الشامل: ${comprehensiveCount}/${comprehensiveIndicators.length}`, 'info');
                    log(`📊 محتوى عام مكتشف: ${genericCount}/${genericContent.length}`, 'info');
                    
                    if (comprehensiveCount >= 3 && genericCount === 0) {
                        log('🎉 التقرير الرئيسي يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                        showReportPreview(mainReport, 'التقرير الرئيسي');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else {
                        log('❌ التقرير الرئيسي لا يحتوي على المحتوى الشامل التفصيلي المطلوب', 'error');
                        if (genericCount > 0) {
                            log('⚠️ تم اكتشاف محتوى عام أو افتراضي', 'warning');
                        }
                        return false;
                    }
                } else {
                    log('❌ التقرير الرئيسي غير صالح أو صغير جداً', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
                return false;
            }
        }

        async function testSeparateReports() {
            log('📋 اختبار التقارير المنفصلة...', 'info');
            totalTests++;

            try {
                const testVulns = createTestVulnerabilities();
                const testPageData = {
                    page_name: 'Test Security Page',
                    page_url: 'http://example.com/test',
                    vulnerabilities: testVulns
                };

                log('📝 إنشاء التقرير المنفصل...', 'info');
                const separateReport = await bugBountyCore.generatePageHTMLReport(testPageData, 'http://example.com/test', 1);

                if (separateReport && typeof separateReport === 'string' && separateReport.length > 3000) {
                    log(`✅ التقرير المنفصل تم إنشاؤه (${(separateReport.length / 1024).toFixed(1)} KB)`, 'success');
                    
                    // فحص المحتوى الشامل التفصيلي
                    const comprehensiveIndicators = [
                        'الوصف الشامل التفصيلي',
                        'خطوات الاستغلال الشاملة التفصيلية',
                        'تحليل التأثير الشامل التفصيلي',
                        'التوصيات الشاملة التفصيلية',
                        'النظام الشامل التفصيلي v4.0'
                    ];
                    
                    const genericContent = [
                        'تم اكتشاف ثغرة أمنية',
                        'قد يؤثر على أمان الموقع',
                        'يُنصح بإصلاح هذه الثغرة',
                        'حدث خطأ في تحميل التفاصيل الشاملة'
                    ];
                    
                    let comprehensiveCount = 0;
                    let genericCount = 0;
                    
                    for (const indicator of comprehensiveIndicators) {
                        if (separateReport.includes(indicator)) {
                            comprehensiveCount++;
                        }
                    }
                    
                    for (const generic of genericContent) {
                        if (separateReport.includes(generic)) {
                            genericCount++;
                        }
                    }
                    
                    log(`📊 مؤشرات المحتوى الشامل: ${comprehensiveCount}/${comprehensiveIndicators.length}`, 'info');
                    log(`📊 محتوى عام مكتشف: ${genericCount}/${genericContent.length}`, 'info');
                    
                    if (comprehensiveCount >= 3 && genericCount === 0) {
                        log('🎉 التقرير المنفصل يحتوي على المحتوى الشامل التفصيلي!', 'success');
                        log('✅ لا يوجد محتوى عام أو افتراضي', 'success');
                        showReportPreview(separateReport, 'التقرير المنفصل');
                        passedTests++;
                        reportsGenerated++;
                        return true;
                    } else {
                        log('❌ التقرير المنفصل لا يحتوي على المحتوى الشامل التفصيلي المطلوب', 'error');
                        if (genericCount > 0) {
                            log('⚠️ تم اكتشاف محتوى عام أو افتراضي', 'warning');
                        }
                        return false;
                    }
                } else {
                    log('❌ التقرير المنفصل غير صالح أو صغير جداً', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير المنفصل: ${error.message}`, 'error');
                return false;
            }
        }

        async function runComprehensiveTest() {
            clearResults();
            log('🚀 بدء الاختبار الشامل للتقارير v4.0', 'info');
            updateStatus('جاري تشغيل الاختبار الشامل...');
            updateProgress(0);

            // تهيئة النظام
            updateProgress(10);
            const initSuccess = await initializeSystem();
            if (!initSuccess) {
                updateStatus('❌ فشل في التهيئة');
                updateStats();
                return;
            }

            // اختبار الدوال الشاملة
            updateProgress(30);
            const functionsTest = await testComprehensiveFunctions();
            
            // اختبار التقرير الرئيسي
            updateProgress(60);
            const mainReportTest = await testMainReport();
            
            // اختبار التقارير المنفصلة
            updateProgress(90);
            const separateReportsTest = await testSeparateReports();

            updateProgress(100);

            // النتائج النهائية
            const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;

            log('🏁 انتهى الاختبار الشامل', 'info');
            log(`📊 النتائج النهائية: ${passedTests}/${totalTests} (${successRate}%)`, 'info');

            if (passedTests === totalTests) {
                updateStatus(`🎉 نجح في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                log('🎉 النظام الشامل التفصيلي v4.0 يعمل بكفاءة 100%!', 'success');
                log('✅ جميع التقارير تنتج التفاصيل الشاملة التفصيلية تلقائياً وديناميكياً', 'success');
                log('🔥 لا يوجد محتوى عام أو افتراضي أو يدوي', 'success');
                log('🚀 النظام جاهز للاستخدام الإنتاجي!', 'success');
            } else if (passedTests > totalTests * 0.8) {
                updateStatus(`✅ نجح في معظم الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                log('✅ النظام الشامل التفصيلي v4.0 يعمل بكفاءة عالية', 'success');
            } else {
                updateStatus(`⚠️ نجح في بعض الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                log('⚠️ النظام الشامل التفصيلي v4.0 يحتاج مراجعة', 'warning');
            }

            updateStats();
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runComprehensiveTest, 1000);
        });
    </script>
</body>
</html>
