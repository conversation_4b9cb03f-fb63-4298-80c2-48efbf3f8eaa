# تشغيل اختبار شامل لإنتاج التقارير والتحقق من جميع التعديلات
Write-Host "COMPREHENSIVE REPORT GENERATION TEST WITH ALL FIXES" -ForegroundColor Red
Write-Host "===================================================" -ForegroundColor Yellow

Write-Host ""
Write-Host "1. CHECKING SYSTEM FILES AND FIXES..." -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Yellow

# فحص الملفات المطلوبة
$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$impactVisualizerFile = "assets/modules/bugbounty/impact_visualizer.js"
$textualAnalyzerFile = "assets/modules/bugbounty/textual_impact_analyzer.js"
$testFile = "comprehensive_report_test.html"

$filesOk = $true
$requiredFiles = @($bugBountyFile, $impactVisualizerFile, $textualAnalyzerFile, $testFile)

foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        $size = [math]::Round((Get-Item $file).Length / 1KB, 2)
        Write-Host "FOUND: $file ($size KB)" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $file" -ForegroundColor Red
        $filesOk = $false
    }
}

if (-not $filesOk) {
    Write-Host "Some required files are missing! Cannot proceed." -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "2. VERIFYING TEXTUAL_IMPACT_ANALYZER PATH FIX..." -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Yellow

$bugBountyContent = Get-Content $bugBountyFile -Raw

# فحص إصلاح مسار textual_impact_analyzer
$correctPath = "assets/modules/bugbounty/textual_impact_analyzer.js"
if ($bugBountyContent -match [regex]::Escape($correctPath)) {
    Write-Host "PATH FIX APPLIED: $correctPath" -ForegroundColor Green
} else {
    Write-Host "PATH FIX MISSING: $correctPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "3. VERIFYING OBJECT DISPLAY FIXES..." -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Yellow

# فحص إصلاحات عرض الكائنات
$objectFixes = @(
    'vuln\.comprehensive_details\?\.technical_details\?\.comprehensive_description',
    'vuln\.dynamic_impact\?\.detailed_impact',
    'vuln\.exploitation_steps\?\.detailed_steps'
)

$fixesFound = 0
foreach ($fix in $objectFixes) {
    if ($bugBountyContent -match $fix) {
        Write-Host "OBJECT FIX FOUND: $fix" -ForegroundColor Green
        $fixesFound++
    } else {
        Write-Host "OBJECT FIX MISSING: $fix" -ForegroundColor Red
    }
}

Write-Host "Object Display Fixes: $fixesFound/$($objectFixes.Count)" -ForegroundColor $(if ($fixesFound -eq $objectFixes.Count) { "Green" } else { "Red" })

Write-Host ""
Write-Host "4. STARTING PYTHON SERVER..." -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Yellow

# بدء خادم Python
$pythonProcess = Start-Process -FilePath "python" -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
Write-Host "Python server started (PID: $($pythonProcess.Id))" -ForegroundColor Green
Start-Sleep -Seconds 3

# التحقق من الخادم
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "Server is running successfully!" -ForegroundColor Green
} catch {
    Write-Host "Server failed to start: $($_.Exception.Message)" -ForegroundColor Red
    Stop-Process -Id $pythonProcess.Id -Force
    exit 1
}

Write-Host ""
Write-Host "5. OPENING COMPREHENSIVE TEST PAGE..." -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Yellow

# فتح صفحة الاختبار
$testUrl = "http://localhost:3000/comprehensive_report_test.html"
Start-Process $testUrl

Write-Host "Test page opened: $testUrl" -ForegroundColor Green
Write-Host ""
Write-Host "MANUAL TESTING INSTRUCTIONS:" -ForegroundColor Yellow
Write-Host "============================" -ForegroundColor Yellow
Write-Host "1. Click 'اختبار شامل كامل' for full system test" -ForegroundColor White
Write-Host "2. Watch the progress and results in the browser" -ForegroundColor White
Write-Host "3. Reports will be downloaded automatically" -ForegroundColor White
Write-Host "4. Check browser console for detailed logs" -ForegroundColor White

Write-Host ""
Write-Host "6. WAITING FOR TEST COMPLETION..." -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Yellow

Write-Host "Waiting 30 seconds for manual testing..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

Write-Host ""
Write-Host "7. CHECKING FOR GENERATED REPORTS..." -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Yellow

# فحص التقارير المُنتجة في مجلد التحميلات
$downloadsPath = [Environment]::GetFolderPath("UserProfile") + "\Downloads"
$reportFiles = Get-ChildItem -Path $downloadsPath -Filter "*comprehensive*report*.html" -ErrorAction SilentlyContinue | Sort-Object LastWriteTime -Descending | Select-Object -First 10

if ($reportFiles.Count -gt 0) {
    Write-Host "GENERATED REPORTS FOUND IN DOWNLOADS:" -ForegroundColor Green
    Write-Host "=====================================" -ForegroundColor Yellow
    
    foreach ($file in $reportFiles) {
        $size = [math]::Round($file.Length / 1KB, 2)
        $age = [math]::Round((New-TimeSpan -Start $file.LastWriteTime -End (Get-Date)).TotalMinutes, 1)
        
        if ($age -le 5) {  # ملفات حديثة (آخر 5 دقائق)
            Write-Host "RECENT: $($file.Name) ($size KB) - $age min ago" -ForegroundColor Green
            
            # فحص محتوى التقرير
            $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
            if ($content) {
                $hasObjectObject = $content -match '\[object Object\]'
                $hasComprehensive = $content -match 'comprehensive|شامل'
                $hasRealData = $content -match 'SQL Injection|XSS|Authentication'
                $hasExploitation = $content -match 'exploitation|استغلال'
                $hasImpact = $content -match 'impact|تأثير'
                
                Write-Host "    [object Object] issue: $(if ($hasObjectObject) { 'FOUND' } else { 'FIXED' })" -ForegroundColor $(if ($hasObjectObject) { "Red" } else { "Green" })
                Write-Host "    Comprehensive content: $(if ($hasComprehensive) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasComprehensive) { "Green" } else { "Red" })
                Write-Host "    Real vulnerability data: $(if ($hasRealData) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasRealData) { "Green" } else { "Red" })
                Write-Host "    Exploitation steps: $(if ($hasExploitation) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasExploitation) { "Green" } else { "Red" })
                Write-Host "    Impact analysis: $(if ($hasImpact) { 'PRESENT' } else { 'MISSING' })" -ForegroundColor $(if ($hasImpact) { "Green" } else { "Red" })
                
                # تقييم جودة التقرير
                $qualityScore = 0
                if (-not $hasObjectObject) { $qualityScore++ }
                if ($hasComprehensive) { $qualityScore++ }
                if ($hasRealData) { $qualityScore++ }
                if ($hasExploitation) { $qualityScore++ }
                if ($hasImpact) { $qualityScore++ }
                
                $qualityPercentage = [math]::Round(($qualityScore / 5) * 100)
                Write-Host "    Quality Score: $qualityScore/5 ($qualityPercentage%)" -ForegroundColor $(if ($qualityPercentage -ge 80) { "Green" } elseif ($qualityPercentage -ge 60) { "Yellow" } else { "Red" })
                Write-Host ""
            }
        } else {
            Write-Host "OLD: $($file.Name) ($size KB) - $age min ago" -ForegroundColor Gray
        }
    }
} else {
    Write-Host "No comprehensive reports found in Downloads folder" -ForegroundColor Yellow
    Write-Host "Reports may have been saved elsewhere or test may have failed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "8. CHECKING CURRENT DIRECTORY FOR REPORTS..." -ForegroundColor Cyan
Write-Host "=============================================" -ForegroundColor Yellow

# فحص المجلد الحالي أيضاً
$localReports = Get-ChildItem -Path "." -Filter "*comprehensive*report*.html" -ErrorAction SilentlyContinue

if ($localReports.Count -gt 0) {
    Write-Host "REPORTS FOUND IN CURRENT DIRECTORY:" -ForegroundColor Green
    foreach ($file in $localReports) {
        $size = [math]::Round($file.Length / 1KB, 2)
        Write-Host "LOCAL: $($file.Name) ($size KB)" -ForegroundColor Green
    }
} else {
    Write-Host "No reports found in current directory" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "9. FINAL ASSESSMENT..." -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Yellow

$totalScore = 0
$maxScore = 4

# تقييم النتائج
if ($filesOk) { $totalScore++ }
if ($fixesFound -eq $objectFixes.Count) { $totalScore++ }
if ($reportFiles.Count -gt 0) { $totalScore++ }

# فحص جودة التقارير المُنتجة
$highQualityReports = 0
foreach ($file in $reportFiles) {
    if ((New-TimeSpan -Start $file.LastWriteTime -End (Get-Date)).TotalMinutes -le 5) {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            $hasObjectObject = $content -match '\[object Object\]'
            $hasComprehensive = $content -match 'comprehensive|شامل'
            $hasRealData = $content -match 'SQL Injection|XSS|Authentication'
            
            if (-not $hasObjectObject -and $hasComprehensive -and $hasRealData) {
                $highQualityReports++
            }
        }
    }
}

if ($highQualityReports -gt 0) { $totalScore++ }

$percentage = [math]::Round(($totalScore / $maxScore) * 100)

Write-Host "COMPREHENSIVE TEST RESULTS:" -ForegroundColor Yellow
Write-Host "===========================" -ForegroundColor Yellow
Write-Host "System Files: $(if ($filesOk) { 'PASS' } else { 'FAIL' })" -ForegroundColor $(if ($filesOk) { "Green" } else { "Red" })
Write-Host "Object Display Fixes: $fixesFound/$($objectFixes.Count)" -ForegroundColor $(if ($fixesFound -eq $objectFixes.Count) { "Green" } else { "Red" })
Write-Host "Reports Generated: $($reportFiles.Count)" -ForegroundColor $(if ($reportFiles.Count -gt 0) { "Green" } else { "Red" })
Write-Host "High Quality Reports: $highQualityReports" -ForegroundColor $(if ($highQualityReports -gt 0) { "Green" } else { "Red" })

Write-Host ""
Write-Host "OVERALL SCORE: $totalScore/$maxScore ($percentage%)" -ForegroundColor $(if ($percentage -eq 100) { "Green" } elseif ($percentage -ge 75) { "Yellow" } else { "Red" })

if ($percentage -eq 100) {
    Write-Host ""
    Write-Host "EXCELLENT! ALL FIXES ARE WORKING PERFECTLY!" -ForegroundColor Green
    Write-Host "Comprehensive reports generated successfully!" -ForegroundColor Green
    Write-Host "Object display issues completely resolved!" -ForegroundColor Green
    Write-Host "All 36 comprehensive functions working!" -ForegroundColor Green
    Write-Host "Integration files loaded and working!" -ForegroundColor Green
} elseif ($percentage -ge 75) {
    Write-Host ""
    Write-Host "GOOD! Most fixes are working correctly!" -ForegroundColor Yellow
    Write-Host "Minor improvements may be needed!" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "ISSUES DETECTED! Some critical problems remain!" -ForegroundColor Red
}

Write-Host ""
Write-Host "10. CLEANUP..." -ForegroundColor Cyan
Write-Host "=============" -ForegroundColor Yellow

# إيقاف خادم Python
Write-Host "Stopping Python server..." -ForegroundColor Yellow
Stop-Process -Id $pythonProcess.Id -Force
Write-Host "Python server stopped." -ForegroundColor Green

Write-Host ""
Write-Host "COMPREHENSIVE TEST COMPLETE!" -ForegroundColor Green
Write-Host "Check the generated reports to verify all fixes are working!" -ForegroundColor Green
