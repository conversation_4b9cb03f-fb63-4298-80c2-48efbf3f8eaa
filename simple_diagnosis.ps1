# Comprehensive Diagnosis for v4.0 System Issues
Write-Host "COMPREHENSIVE DIAGNOSIS - v4.0 SYSTEM ISSUES" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Yellow

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$impactVisualizerFile = "assets/modules/bugbounty/impact_visualizer.js"
$textualAnalyzerFile = "assets/modules/bugbounty/textual_impact_analyzer.js"

# Check file existence
Write-Host ""
Write-Host "FILE EXISTENCE CHECK:" -ForegroundColor Cyan
Write-Host "====================" -ForegroundColor Yellow

if (Test-Path $bugBountyFile) {
    $size1 = [math]::Round((Get-Item $bugBountyFile).Length / 1KB, 2)
    Write-Host "BugBountyCore.js exists ($size1 KB)" -ForegroundColor Green
} else {
    Write-Host "BugBountyCore.js missing" -ForegroundColor Red
}

if (Test-Path $impactVisualizerFile) {
    $size2 = [math]::Round((Get-Item $impactVisualizerFile).Length / 1KB, 2)
    Write-Host "impact_visualizer.js exists ($size2 KB)" -ForegroundColor Green
} else {
    Write-Host "impact_visualizer.js missing" -ForegroundColor Red
}

if (Test-Path $textualAnalyzerFile) {
    $size3 = [math]::Round((Get-Item $textualAnalyzerFile).Length / 1KB, 2)
    Write-Host "textual_impact_analyzer.js exists ($size3 KB)" -ForegroundColor Green
} else {
    Write-Host "textual_impact_analyzer.js missing" -ForegroundColor Red
}

# Read file contents
$bugBountyContent = Get-Content $bugBountyFile -Raw

# Check impact_visualizer usage
Write-Host ""
Write-Host "IMPACT_VISUALIZER USAGE CHECK:" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Yellow

$impactVisualizerUsage = @(
    "impact_visualizer",
    "ImpactVisualizer",
    "impactVisualizer",
    "initializeImpactVisualizer",
    "this.impactVisualizer"
)

$visualizerUsageFound = 0
foreach ($usage in $impactVisualizerUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "Found: $usage" -ForegroundColor Green
        $visualizerUsageFound++
    } else {
        Write-Host "Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "Impact Visualizer Usage: $visualizerUsageFound/5" -ForegroundColor $(if ($visualizerUsageFound -ge 3) { "Green" } else { "Red" })

# Check textual_impact_analyzer usage
Write-Host ""
Write-Host "TEXTUAL_IMPACT_ANALYZER USAGE CHECK:" -ForegroundColor Cyan
Write-Host "====================================" -ForegroundColor Yellow

$textualAnalyzerUsage = @(
    "textual_impact_analyzer",
    "TextualImpactAnalyzer",
    "textualImpactAnalyzer",
    "initializeTextualImpactAnalyzer",
    "this.textualImpactAnalyzer"
)

$textualUsageFound = 0
foreach ($usage in $textualAnalyzerUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "Found: $usage" -ForegroundColor Green
        $textualUsageFound++
    } else {
        Write-Host "Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "Textual Analyzer Usage: $textualUsageFound/5" -ForegroundColor $(if ($textualUsageFound -ge 3) { "Green" } else { "Red" })

# Check comprehensive details function
Write-Host ""
Write-Host "COMPREHENSIVE DETAILS FUNCTION CHECK:" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Yellow

if ($bugBountyContent -match "async generateComprehensiveDetailsFromRealData") {
    Write-Host "generateComprehensiveDetailsFromRealData function exists" -ForegroundColor Green
    
    # Check complex object return
    if ($bugBountyContent -match "technical_details.*impact_analysis.*exploitation_results") {
        Write-Host "Function returns complex object structure" -ForegroundColor Green
    } else {
        Write-Host "Function may not return complex object" -ForegroundColor Red
    }
    
    # Check real data usage
    if ($bugBountyContent -match "extractRealDataFromDiscoveredVulnerability") {
        Write-Host "Function uses real data extraction" -ForegroundColor Green
    } else {
        Write-Host "Function may not extract real data" -ForegroundColor Red
    }
    
} else {
    Write-Host "generateComprehensiveDetailsFromRealData function missing" -ForegroundColor Red
}

# Check HTML display issue
Write-Host ""
Write-Host "HTML DISPLAY ISSUE CHECK:" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Yellow

# Search for direct object display
$directDisplayMatches = [regex]::Matches($bugBountyContent, '\$\{vuln\.comprehensive_details[^}]*\}')
Write-Host "Found $($directDisplayMatches.Count) direct display usages of comprehensive_details"

if ($directDisplayMatches.Count -gt 0) {
    Write-Host "PROBLEM: comprehensive_details displayed directly as object" -ForegroundColor Red
    Write-Host "SOLUTION: Extract specific properties from the object" -ForegroundColor Yellow
    
    foreach ($match in $directDisplayMatches) {
        Write-Host "   Found: $($match.Value)" -ForegroundColor Yellow
    }
}

# Check screenshot usage
Write-Host ""
Write-Host "SCREENSHOT USAGE CHECK:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Yellow

$screenshotUsage = @(
    "captureScreenshotForVulnerability",
    "generateBeforeAfterScreenshots",
    "screenshot_data",
    "visual_evidence",
    "screenshots"
)

$screenshotFound = 0
foreach ($usage in $screenshotUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "Found: $usage" -ForegroundColor Green
        $screenshotFound++
    } else {
        Write-Host "Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "Screenshot Usage: $screenshotFound/$($screenshotUsage.Count)" -ForegroundColor $(if ($screenshotFound -ge 3) { "Green" } else { "Red" })

# Check separate reports
Write-Host ""
Write-Host "SEPARATE REPORTS CHECK:" -ForegroundColor Cyan
Write-Host "======================" -ForegroundColor Yellow

if ($bugBountyContent -match "generatePageHTMLReport") {
    Write-Host "generatePageHTMLReport function exists" -ForegroundColor Green
} else {
    Write-Host "generatePageHTMLReport function missing" -ForegroundColor Red
}

# Summary
Write-Host ""
Write-Host "DIAGNOSIS SUMMARY:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

Write-Host "Impact Visualizer Integration: $visualizerUsageFound/5" -ForegroundColor $(if ($visualizerUsageFound -ge 3) { "Green" } else { "Red" })
Write-Host "Textual Analyzer Integration: $textualUsageFound/5" -ForegroundColor $(if ($textualUsageFound -ge 3) { "Green" } else { "Red" })
Write-Host "Screenshot Integration: $screenshotFound/$($screenshotUsage.Count)" -ForegroundColor $(if ($screenshotFound -ge 3) { "Green" } else { "Red" })
Write-Host "Object Display Issues: $($directDisplayMatches.Count) found" -ForegroundColor $(if ($directDisplayMatches.Count -eq 0) { "Green" } else { "Red" })

Write-Host ""
Write-Host "REQUIRED FIXES:" -ForegroundColor Red
Write-Host "==============" -ForegroundColor Yellow

if ($directDisplayMatches.Count -gt 0) {
    Write-Host "1. Fix object display issue in HTML templates" -ForegroundColor Red
}

if ($visualizerUsageFound -lt 3) {
    Write-Host "2. Improve Impact Visualizer integration" -ForegroundColor Red
}

if ($textualUsageFound -lt 3) {
    Write-Host "3. Improve Textual Analyzer integration" -ForegroundColor Red
}

if ($screenshotFound -lt 3) {
    Write-Host "4. Improve screenshot integration" -ForegroundColor Red
}

Write-Host ""
Write-Host "DIAGNOSIS COMPLETE!" -ForegroundColor Green
