# اختبار مباشر لإنتاج التقارير مع جميع التعديلات الجديدة
Write-Host "DIRECT REPORT GENERATION TEST WITH ALL NEW FIXES" -ForegroundColor Red
Write-Host "=================================================" -ForegroundColor Yellow

# بدء خادم Python للاختبار
Write-Host ""
Write-Host "STARTING PYTHON SERVER FOR TESTING..." -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Yellow

$pythonProcess = Start-Process -FilePath "python" -ArgumentList "-m", "http.server", "3000" -PassThru -WindowStyle Hidden
Write-Host "Python server started on port 3000 (PID: $($pythonProcess.Id))" -ForegroundColor Green

# انتظار قليل لبدء الخادم
Start-Sleep -Seconds 3

# اختبار الوصول للخادم
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5
    Write-Host "Server is running successfully!" -ForegroundColor Green
} catch {
    Write-Host "Failed to connect to server: $($_.Exception.Message)" -ForegroundColor Red
    Stop-Process -Id $pythonProcess.Id -Force
    exit 1
}

Write-Host ""
Write-Host "TESTING BUGBOUNTY SYSTEM INITIALIZATION..." -ForegroundColor Cyan
Write-Host "==========================================" -ForegroundColor Yellow

# اختبار تحميل النظام
try {
    $testUrl = "http://localhost:3000/test_actual_report_generation.html"
    Write-Host "Opening test page: $testUrl" -ForegroundColor Green
    
    # فتح صفحة الاختبار
    Start-Process $testUrl
    
    Write-Host "Test page opened successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "INSTRUCTIONS FOR MANUAL TESTING:" -ForegroundColor Yellow
    Write-Host "================================" -ForegroundColor Yellow
    Write-Host "1. Click 'اختبار التقرير الرئيسي' to test main report generation" -ForegroundColor White
    Write-Host "2. Click 'اختبار التقارير المنفصلة' to test separate reports" -ForegroundColor White
    Write-Host "3. Click 'اختبار جميع الـ 36 دالة' to test all comprehensive functions" -ForegroundColor White
    Write-Host "4. Click 'اختبار الصور والتأثيرات' to test visual features" -ForegroundColor White
    Write-Host "5. Click 'اختبار شامل كامل' to run all tests" -ForegroundColor White
    
} catch {
    Write-Host "Failed to open test page: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "AUTOMATED FILE VERIFICATION..." -ForegroundColor Cyan
Write-Host "=============================" -ForegroundColor Yellow

# فحص الملفات المطلوبة
$requiredFiles = @(
    "assets/modules/bugbounty/BugBountyCore.js",
    "assets/modules/bugbounty/impact_visualizer.js", 
    "assets/modules/bugbounty/textual_impact_analyzer.js"
)

$filesOk = $true
foreach ($file in $requiredFiles) {
    if (Test-Path $file) {
        $size = [math]::Round((Get-Item $file).Length / 1KB, 2)
        Write-Host "FOUND: $file ($size KB)" -ForegroundColor Green
    } else {
        Write-Host "MISSING: $file" -ForegroundColor Red
        $filesOk = $false
    }
}

if ($filesOk) {
    Write-Host "All required files are present!" -ForegroundColor Green
} else {
    Write-Host "Some required files are missing!" -ForegroundColor Red
}

Write-Host ""
Write-Host "CHECKING FIXES IN BUGBOUNTYCORE..." -ForegroundColor Cyan
Write-Host "==================================" -ForegroundColor Yellow

$bugBountyContent = Get-Content "assets/modules/bugbounty/BugBountyCore.js" -Raw

# فحص الإصلاحات الجديدة
$fixes = @(
    'vuln\.comprehensive_details\?\.technical_details\?\.comprehensive_description',
    'vuln\.dynamic_impact\?\.detailed_impact',
    'vuln\.exploitation_steps\?\.detailed_steps',
    'this\.impactVisualizer\s*=',
    'this\.textualImpactAnalyzer\s*='
)

$fixesFound = 0
foreach ($fix in $fixes) {
    if ($bugBountyContent -match $fix) {
        Write-Host "FIX IMPLEMENTED: $fix" -ForegroundColor Green
        $fixesFound++
    } else {
        Write-Host "FIX MISSING: $fix" -ForegroundColor Red
    }
}

Write-Host "Fixes implemented: $fixesFound/$($fixes.Count)" -ForegroundColor $(if ($fixesFound -eq $fixes.Count) { "Green" } else { "Red" })

Write-Host ""
Write-Host "TESTING REPORT GENERATION FUNCTIONS..." -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Yellow

# فحص دوال إنتاج التقارير
$reportFunctions = @(
    'generateVulnerabilitiesHTML',
    'generatePageHTMLReport',
    'generateComprehensiveDetailsFromRealData',
    'generateDynamicImpactForAnyVulnerability',
    'generateRealExploitationStepsForVulnerabilityComprehensive'
)

$functionsFound = 0
foreach ($func in $reportFunctions) {
    if ($bugBountyContent -match $func) {
        Write-Host "FUNCTION FOUND: $func" -ForegroundColor Green
        $functionsFound++
    } else {
        Write-Host "FUNCTION MISSING: $func" -ForegroundColor Red
    }
}

Write-Host "Report functions found: $functionsFound/$($reportFunctions.Count)" -ForegroundColor $(if ($functionsFound -eq $reportFunctions.Count) { "Green" } else { "Red" })

Write-Host ""
Write-Host "CREATING SAMPLE VULNERABILITY FOR TESTING..." -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Yellow

# إنشاء ملف اختبار JSON للثغرات
$sampleVulnerabilities = @{
    vulnerabilities = @(
        @{
            name = "SQL Injection في نظام إدارة المستخدمين"
            type = "SQL Injection"
            severity = "Critical"
            url = "http://testphp.vulnweb.com/admin/users.php"
            parameter = "user_id"
            payload = "1' UNION SELECT user(),database(),version() --"
            response = "MySQL error: Database version disclosed"
            evidence = "تم كشف إصدار قاعدة البيانات"
            business_impact = "عالي جداً - إمكانية الوصول لجميع بيانات النظام"
        },
        @{
            name = "XSS المخزن في نظام التعليقات"
            type = "Cross-Site Scripting"
            severity = "High"
            url = "http://testphp.vulnweb.com/comments.php"
            parameter = "comment_text"
            payload = "<script>alert('XSS')</script>"
            response = "تم حفظ التعليق مع تنفيذ الكود"
            evidence = "ظهور نافذة تنبيه JavaScript"
            business_impact = "عالي - إمكانية سرقة جلسات المستخدمين"
        }
    )
}

$sampleJson = $sampleVulnerabilities | ConvertTo-Json -Depth 3
$sampleJson | Out-File -FilePath "sample_vulnerabilities.json" -Encoding UTF8

Write-Host "Sample vulnerabilities created: sample_vulnerabilities.json" -ForegroundColor Green

Write-Host ""
Write-Host "FINAL TEST SUMMARY:" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

Write-Host "Required files: $(if ($filesOk) { 'PASS' } else { 'FAIL' })" -ForegroundColor $(if ($filesOk) { "Green" } else { "Red" })
Write-Host "Fixes implemented: $fixesFound/$($fixes.Count)" -ForegroundColor $(if ($fixesFound -eq $fixes.Count) { "Green" } else { "Red" })
Write-Host "Report functions: $functionsFound/$($reportFunctions.Count)" -ForegroundColor $(if ($functionsFound -eq $reportFunctions.Count) { "Green" } else { "Red" })
Write-Host "Python server: RUNNING (PID: $($pythonProcess.Id))" -ForegroundColor Green
Write-Host "Test page: OPENED" -ForegroundColor Green

$totalScore = 0
$maxScore = 3

if ($filesOk) { $totalScore++ }
if ($fixesFound -eq $fixes.Count) { $totalScore++ }
if ($functionsFound -eq $reportFunctions.Count) { $totalScore++ }

$percentage = [math]::Round(($totalScore / $maxScore) * 100)

Write-Host ""
Write-Host "OVERALL READINESS: $totalScore/$maxScore ($percentage%)" -ForegroundColor $(if ($percentage -eq 100) { "Green" } elseif ($percentage -ge 80) { "Yellow" } else { "Red" })

if ($percentage -eq 100) {
    Write-Host ""
    Write-Host "EXCELLENT! SYSTEM IS READY FOR TESTING!" -ForegroundColor Green
    Write-Host "All components are in place and fixes are implemented!" -ForegroundColor Green
    Write-Host "Use the opened test page to verify report generation!" -ForegroundColor Green
} elseif ($percentage -ge 80) {
    Write-Host ""
    Write-Host "GOOD! System is mostly ready!" -ForegroundColor Yellow
    Write-Host "Minor issues may need attention!" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "ISSUES DETECTED! System needs fixes!" -ForegroundColor Red
}

Write-Host ""
Write-Host "NEXT STEPS:" -ForegroundColor Yellow
Write-Host "==========" -ForegroundColor Yellow
Write-Host "1. Use the test page to generate actual reports" -ForegroundColor White
Write-Host "2. Check downloaded reports for comprehensive details" -ForegroundColor White
Write-Host "3. Verify that [object Object] issues are fixed" -ForegroundColor White
Write-Host "4. Confirm all 36 functions are working" -ForegroundColor White
Write-Host "5. Test visual features and screenshots" -ForegroundColor White

Write-Host ""
Write-Host "Press any key to stop the Python server and exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# إيقاف خادم Python
Write-Host ""
Write-Host "Stopping Python server..." -ForegroundColor Yellow
Stop-Process -Id $pythonProcess.Id -Force
Write-Host "Python server stopped." -ForegroundColor Green

Write-Host ""
Write-Host "DIRECT REPORT GENERATION TEST COMPLETE!" -ForegroundColor Green
