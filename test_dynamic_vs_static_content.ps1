# Test DYNAMIC vs STATIC content - Verify content is based on discovered vulnerabilities
Write-Host "=== TESTING DYNAMIC VS STATIC CONTENT ===" -ForegroundColor Red
Write-Host "Verifying content is automatically generated based on discovered vulnerabilities" -ForegroundColor Yellow
Write-Host "NOT manual, default, or generic content" -ForegroundColor Yellow

$TestResults = @{
    ReportsAnalyzed = 0
    DynamicContentFound = 0
    StaticContentFound = 0
    VulnerabilitySpecificContent = 0
    GenericContent = 0
    AutomaticGeneration = 0
    ManualContent = 0
    DynamicScore = 0
}

function Test-DynamicContent {
    param($ReportPath, $VulnSpecificIndicators, $GenericIndicators)
    if (Test-Path $ReportPath) {
        $content = Get-Content $ReportPath -Raw -Encoding UTF8
        
        $vulnSpecific = 0
        $generic = 0
        
        foreach ($indicator in $VulnSpecificIndicators) {
            if ($content -like "*$indicator*") { $vulnSpecific++ }
        }
        
        foreach ($indicator in $GenericIndicators) {
            if ($content -like "*$indicator*") { $generic++ }
        }
        
        return @{
            VulnSpecific = $vulnSpecific
            Generic = $generic
        }
    }
    return @{ VulnSpecific = 0; Generic = 0 }
}

Write-Host "`n1. Finding reports with actual vulnerability data..." -ForegroundColor Cyan

# Find reports that should contain real vulnerability data
$reportFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse | Where-Object { 
    $_.Name -like "*comprehensive*" -or 
    $_.Name -like "*main*" -or 
    $_.Name -like "*separate*" -or
    $_.Name -like "*v4*" -or
    $_.Name -like "*real*"
} | Where-Object {
    $_.Length -gt 10KB  # Only analyze substantial reports
}

$TestResults.ReportsAnalyzed = $reportFiles.Count
Write-Host "  Found $($reportFiles.Count) substantial reports to analyze" -ForegroundColor Green

Write-Host "`n2. Testing for VULNERABILITY-SPECIFIC dynamic content..." -ForegroundColor Cyan

# Vulnerability-specific indicators (should be dynamic based on discovered vulnerabilities)
$vulnSpecificIndicators = @(
    # SQL Injection specific
    "UNION SELECT",
    "user(),database(),version()",
    "MySQL error",
    "syntax error",
    "Database version",
    "user_id",
    "admin/users.php",
    
    # XSS specific
    "<script>alert",
    "XSS Confirmed",
    "JavaScript code execution",
    "search.php",
    "query parameter",
    
    # LFI specific
    "../../../../etc/passwd",
    "Local File Inclusion",
    "view.php",
    "file parameter",
    
    # Specific payloads and responses
    "testphp.vulnweb.com",
    "confidence_level: 95",
    "confidence_level: 90",
    "confidence_level: 85"
)

# Generic/static indicators (should be avoided - indicates manual/default content)
$genericIndicators = @(
    "وصف شامل للثغرة",
    "payload متخصص",
    "[object Object]",
    "payload_example",
    "تم اكتشاف ثغرة أمنية",
    "generic vulnerability",
    "example payload",
    "sample response",
    "default description",
    "placeholder content",
    "template text",
    "generic content"
)

$totalVulnSpecific = 0
$totalGeneric = 0

foreach ($report in $reportFiles) {
    $result = Test-DynamicContent $report.FullName $vulnSpecificIndicators $genericIndicators
    $totalVulnSpecific += $result.VulnSpecific
    $totalGeneric += $result.Generic
    
    $isDynamic = $result.VulnSpecific -gt $result.Generic
    $status = if ($isDynamic) { "DYNAMIC" } else { "STATIC/GENERIC" }
    $color = if ($isDynamic) { "Green" } else { "Red" }
    
    Write-Host "    $($report.Name): $($result.VulnSpecific) vuln-specific, $($result.Generic) generic [$status]" -ForegroundColor $color
}

$TestResults.VulnerabilitySpecificContent = $totalVulnSpecific
$TestResults.GenericContent = $totalGeneric

Write-Host "  Total vulnerability-specific content: $totalVulnSpecific" -ForegroundColor Green
Write-Host "  Total generic/static content: $totalGeneric" -ForegroundColor $(if ($totalGeneric -lt 5) { "Green" } else { "Red" })

Write-Host "`n3. Testing for AUTOMATIC generation indicators..." -ForegroundColor Cyan

# Automatic generation indicators
$automaticIndicators = @(
    "تم إنشاء تلقائياً",
    "automatically generated",
    "dynamically created",
    "based on discovered",
    "حسب الثغرة المكتشفة",
    "تلقائياً وديناميكياً",
    "extracted from vulnerability",
    "real data from testing",
    "discovered and tested",
    "automatic analysis"
)

# Manual content indicators (should be avoided)
$manualIndicators = @(
    "manually created",
    "hand-written",
    "static template",
    "pre-defined content",
    "default template",
    "manual analysis",
    "copy-paste",
    "hardcoded content"
)

$totalAutomatic = 0
$totalManual = 0

foreach ($report in $reportFiles) {
    $automaticResult = Test-DynamicContent $report.FullName $automaticIndicators $manualIndicators
    $totalAutomatic += $automaticResult.VulnSpecific
    $totalManual += $automaticResult.Generic
    
    Write-Host "    $($report.Name): $($automaticResult.VulnSpecific) automatic, $($automaticResult.Generic) manual indicators" -ForegroundColor $(if ($automaticResult.VulnSpecific -gt $automaticResult.Generic) { "Green" } else { "Yellow" })
}

$TestResults.AutomaticGeneration = $totalAutomatic
$TestResults.ManualContent = $totalManual

Write-Host "  Total automatic generation indicators: $totalAutomatic" -ForegroundColor Green
Write-Host "  Total manual content indicators: $totalManual" -ForegroundColor $(if ($totalManual -lt 3) { "Green" } else { "Red" })

Write-Host "`n4. Testing for REAL vulnerability data extraction..." -ForegroundColor Cyan

# Real data extraction indicators
$realDataIndicators = @(
    "extractRealDataFromDiscoveredVulnerability",
    "realData",
    "vuln.payload",
    "vuln.response", 
    "vuln.evidence",
    "vuln.url",
    "vuln.parameter",
    "vuln.confidence_level",
    "discovered vulnerability",
    "tested vulnerability",
    "actual payload",
    "actual response"
)

$totalRealData = 0

foreach ($report in $reportFiles) {
    $realDataCount = 0
    $content = Get-Content $report.FullName -Raw -Encoding UTF8
    
    foreach ($indicator in $realDataIndicators) {
        if ($content -like "*$indicator*") { $realDataCount++ }
    }
    
    $totalRealData += $realDataCount
    Write-Host "    $($report.Name): $realDataCount/12 real data indicators" -ForegroundColor $(if ($realDataCount -ge 6) { "Green" } elseif ($realDataCount -ge 3) { "Yellow" } else { "Red" })
}

Write-Host "  Total real data extraction indicators: $totalRealData" -ForegroundColor Green

Write-Host "`n5. Testing for SPECIFIC vulnerability types and payloads..." -ForegroundColor Cyan

# Test for specific vulnerability types with their specific payloads
$specificVulnTests = @{
    "SQL Injection" = @("UNION SELECT", "user(),database(),version()", "MySQL error", "syntax error")
    "XSS" = @("<script>alert", "JavaScript", "XSS", "script execution")
    "LFI" = @("../../../../", "etc/passwd", "Local File", "file inclusion")
    "CSRF" = @("Cross-Site Request", "CSRF token", "request forgery")
    "RFI" = @("Remote File", "include(", "http://", "remote inclusion")
}

$vulnTypeMatches = 0

foreach ($vulnType in $specificVulnTests.Keys) {
    $typeFound = 0
    $payloadFound = 0
    
    foreach ($report in $reportFiles) {
        $content = Get-Content $report.FullName -Raw -Encoding UTF8
        
        if ($content -like "*$vulnType*") {
            $typeFound++
            
            # Check for specific payloads for this vulnerability type
            foreach ($payload in $specificVulnTests[$vulnType]) {
                if ($content -like "*$payload*") {
                    $payloadFound++
                    break
                }
            }
        }
    }
    
    if ($typeFound -gt 0 -and $payloadFound -gt 0) {
        $vulnTypeMatches++
        Write-Host "    $vulnType: Found in $typeFound reports with specific payloads in $payloadFound reports" -ForegroundColor Green
    } else {
        Write-Host "    $vulnType: Found in $typeFound reports with specific payloads in $payloadFound reports" -ForegroundColor Yellow
    }
}

Write-Host "  Vulnerability types with specific payloads: $vulnTypeMatches/5" -ForegroundColor $(if ($vulnTypeMatches -ge 3) { "Green" } else { "Yellow" })

Write-Host "`n6. Calculating DYNAMIC vs STATIC score..." -ForegroundColor Cyan

# Calculate dynamic score
$maxScore = 100
$dynamicScore = 0

# Vulnerability-specific content (40 points)
$dynamicScore += [math]::Min(($totalVulnSpecific * 2), 40)

# Automatic generation (25 points)
$dynamicScore += [math]::Min(($totalAutomatic * 5), 25)

# Real data extraction (20 points)
$dynamicScore += [math]::Min(($totalRealData * 1.67), 20)

# Specific vulnerability types (15 points)
$dynamicScore += [math]::Min(($vulnTypeMatches * 3), 15)

# Penalty for generic content
$dynamicScore -= [math]::Min(($totalGeneric * 2), 20)
$dynamicScore -= [math]::Min(($totalManual * 3), 15)

# Ensure score is not negative
$dynamicScore = [math]::Max($dynamicScore, 0)

$TestResults.DynamicScore = [math]::Round($dynamicScore)

Write-Host "`n=== DYNAMIC vs STATIC CONTENT RESULTS ===" -ForegroundColor Yellow
Write-Host "Reports Analyzed: $($TestResults.ReportsAnalyzed)" -ForegroundColor White
Write-Host "Vulnerability-Specific Content: $totalVulnSpecific indicators" -ForegroundColor Green
Write-Host "Generic/Static Content: $totalGeneric indicators" -ForegroundColor $(if ($totalGeneric -lt 5) { "Green" } else { "Red" })
Write-Host "Automatic Generation: $totalAutomatic indicators" -ForegroundColor Green
Write-Host "Manual Content: $totalManual indicators" -ForegroundColor $(if ($totalManual -lt 3) { "Green" } else { "Red" })
Write-Host "Real Data Extraction: $totalRealData indicators" -ForegroundColor Green
Write-Host "Specific Vuln Types: $vulnTypeMatches/5 with specific payloads" -ForegroundColor Green

Write-Host "`nDYNAMIC CONTENT SCORE: $($TestResults.DynamicScore)/100" -ForegroundColor $(if ($TestResults.DynamicScore -ge 80) { "Green" } elseif ($TestResults.DynamicScore -ge 60) { "Yellow" } else { "Red" })

if ($TestResults.DynamicScore -ge 80) {
    Write-Host "`n[EXCELLENT] Content IS TRULY DYNAMIC based on discovered vulnerabilities!" -ForegroundColor Green
    Write-Host "- Reports contain vulnerability-specific content" -ForegroundColor Green
    Write-Host "- Content is automatically generated, not manual" -ForegroundColor Green
    Write-Host "- Real data is extracted from discovered vulnerabilities" -ForegroundColor Green
    Write-Host "- Specific payloads match vulnerability types" -ForegroundColor Green
    Write-Host "- Minimal generic or static content" -ForegroundColor Green
} elseif ($TestResults.DynamicScore -ge 60) {
    Write-Host "`n[GOOD] Content is mostly dynamic with some static elements" -ForegroundColor Yellow
    Write-Host "- Most content is based on discovered vulnerabilities" -ForegroundColor Yellow
    Write-Host "- Some improvements needed to reduce static content" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] Content appears to be mostly static/generic" -ForegroundColor Red
    Write-Host "- Too much generic or manual content detected" -ForegroundColor Red
    Write-Host "- Content may not be truly dynamic based on vulnerabilities" -ForegroundColor Red
}

Write-Host "`n=== DYNAMIC CONTENT VERIFICATION COMPLETE ===" -ForegroundColor Green
