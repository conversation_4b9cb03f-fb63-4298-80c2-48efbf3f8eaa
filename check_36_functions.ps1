$filePath = "assets/modules/bugbounty/BugBountyCore.js"
$content = Get-Content $filePath -Raw

$allFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "extractRealDataFromDiscoveredVulnerability",
    "generateDynamicImpactForAnyVulnerability",
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealDetailedDialogueFromDiscoveredVulnerability",
    "generateComprehensiveVulnerabilityAnalysis",
    "generateDynamicSecurityImpactAnalysis",
    "generateRealTimeVulnerabilityAssessment",
    "generateComprehensiveRiskAnalysis",
    "generateDynamicThreatModelingForVulnerability",
    "generateComprehensiveTestingDetails",
    "generateVisualChangesForVulnerability",
    "generatePersistentResultsForVulnerability",
    "generateImpactVisualizationsForVulnerability",
    "captureScreenshotForVulnerability",
    "generateBeforeAfterScreenshots",
    "generateAdvancedPayloadAnalysis",
    "generateComprehensiveResponseAnalysis",
    "generateDynamicExploitationChain",
    "generateRealTimeSecurityMetrics",
    "generateComprehensiveRemediationPlan",
    "generateComprehensiveDocumentation",
    "generateDetailedTechnicalReport",
    "generateExecutiveSummaryReport",
    "generateComplianceReport",
    "generateForensicAnalysisReport",
    "extractParameterFromDiscoveredVulnerability",
    "extractParametersFromUrl",
    "extractParameterFromPayload",
    "generateRealPayloadFromVulnerability",
    "analyzeVulnerabilityContext",
    "generateInteractiveDialogue",
    "generatePageHTMLReport",
    "generateFinalComprehensiveReport",
    "generateComprehensiveVulnerabilitiesContentUsingExistingFunctions"
)

Write-Host "CHECKING ALL 36 COMPREHENSIVE FUNCTIONS IN v4.0 SYSTEM" -ForegroundColor Red
Write-Host "=======================================================" -ForegroundColor Yellow

$foundFunctions = 0
foreach ($func in $allFunctions) {
    if ($content -match $func) {
        Write-Host "✅ $func" -ForegroundColor Green
        $foundFunctions++
    } else {
        Write-Host "❌ $func" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "RESULT: Found $foundFunctions out of $($allFunctions.Count) functions" -ForegroundColor $(if ($foundFunctions -eq $allFunctions.Count) { "Green" } else { "Yellow" })

if ($foundFunctions -eq $allFunctions.Count) {
    Write-Host "ALL 36 COMPREHENSIVE FUNCTIONS ARE PRESENT IN v4.0!" -ForegroundColor Green
} else {
    Write-Host "SOME FUNCTIONS ARE MISSING" -ForegroundColor Yellow
}

# Check if comprehensive details are being used in reports
Write-Host ""
Write-Host "CHECKING COMPREHENSIVE USAGE IN REPORTS:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Yellow

$comprehensiveUsage = @(
    "comprehensive_details",
    "التفاصيل الشاملة التفصيلية",
    "خطوات الاستغلال الشاملة التفصيلية",
    "تحليل التأثير الشامل التفصيلي",
    "التوصيات الشاملة التفصيلية",
    "النظام الشامل التفصيلي v4.0"
)

$usageFound = 0
foreach ($usage in $comprehensiveUsage) {
    if ($content -match [regex]::Escape($usage)) {
        Write-Host "✅ Found: $usage" -ForegroundColor Green
        $usageFound++
    } else {
        Write-Host "❌ Missing: $usage" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "COMPREHENSIVE USAGE: Found $usageFound out of $($comprehensiveUsage.Count) indicators" -ForegroundColor $(if ($usageFound -ge 4) { "Green" } else { "Yellow" })

Write-Host ""
Write-Host "FINAL VERIFICATION COMPLETE!" -ForegroundColor Green
