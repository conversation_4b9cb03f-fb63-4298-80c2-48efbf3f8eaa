# اختبار شامل نهائي لحل مشكلة Maximum call stack size exceeded
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host "🔧 اختبار شامل نهائي لحل مشكلة Maximum call stack size exceeded" -ForegroundColor Green
Write-Host "================================================================" -ForegroundColor Yellow
Write-Host ""

$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-Function {
    param(
        [string]$TestName,
        [string]$Description,
        [scriptblock]$TestScript
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $result = & $TestScript
        if ($result) {
            Write-Host "   ✅ نجح الاختبار" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ فشل الاختبار" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

# اختبار 1: فحص عدد الاستدعاءات المتكررة
Test-Function -TestName "فحص الاستدعاءات المتكررة" -Description "التأكد من عدم وجود استدعاءات متكررة خطيرة" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # فحص الأنماط الخطيرة
    $pattern1 = $content | Select-String "generatePageHTMLReport.*generatePageHTMLReport"
    $pattern2 = $content | Select-String "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData"
    $pattern3 = $content | Select-String "generateDynamicImpactForAnyVulnerability.*generateDynamicImpactForAnyVulnerability"
    
    Write-Host "      generatePageHTMLReport متكرر: $($pattern1.Count)" -ForegroundColor White
    Write-Host "      generateComprehensiveDetailsFromRealData متكرر: $($pattern2.Count)" -ForegroundColor White
    Write-Host "      generateDynamicImpactForAnyVulnerability متكرر: $($pattern3.Count)" -ForegroundColor White
    
    return ($pattern1.Count -eq 0 -and $pattern2.Count -eq 0 -and $pattern3.Count -eq 0)
}

# اختبار 2: فحص عدد دوال generatePageHTMLReport
Test-Function -TestName "فحص دالة generatePageHTMLReport" -Description "التأكد من وجود دالة واحدة فقط" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    $generatePageHTMLReportCount = ($content | Select-String "async generatePageHTMLReport").Count
    
    Write-Host "      عدد دوال generatePageHTMLReport: $generatePageHTMLReportCount" -ForegroundColor White
    
    return ($generatePageHTMLReportCount -eq 1)
}

# اختبار 3: فحص الاستدعاءات المتكررة لـ extractRealDataFromDiscoveredVulnerability
Test-Function -TestName "فحص extractRealDataFromDiscoveredVulnerability" -Description "التأكد من تقليل الاستدعاءات المتكررة" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    $extractCalls = ($content | Select-String "this\.extractRealDataFromDiscoveredVulnerability").Count
    
    Write-Host "      عدد استدعاءات extractRealDataFromDiscoveredVulnerability: $extractCalls" -ForegroundColor White
    
    # يجب أن يكون العدد أقل من 40 (كان 43 قبل الإصلاح)
    return ($extractCalls -lt 40)
}

# اختبار 4: فحص الاستدعاءات في generateDynamicImpactForAnyVulnerability
Test-Function -TestName "فحص generateDynamicImpactForAnyVulnerability" -Description "التأكد من عدم وجود استدعاءات متكررة داخلية" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # العثور على بداية الدالة
    $startLine = ($content | Select-String "generateDynamicImpactForAnyVulnerability\(vulnerability, realData\)" | Select-Object -First 1).LineNumber
    if ($startLine) {
        # فحص 50 سطر من بداية الدالة
        $functionContent = $content[($startLine-1)..($startLine+49)]
        $recursiveCalls = $functionContent | Select-String "this\.extractRealDataFromDiscoveredVulnerability"
        
        Write-Host "      استدعاءات متكررة في الدالة: $($recursiveCalls.Count)" -ForegroundColor White
        
        # يجب أن يكون 0 أو 1 فقط
        return ($recursiveCalls.Count -le 1)
    }
    return $false
}

# اختبار 5: فحص الاستدعاءات في generatePageHTMLReport
Test-Function -TestName "فحص استدعاءات generatePageHTMLReport" -Description "التأكد من عدم وجود استدعاءات متكررة خطيرة" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    # العثور على بداية الدالة
    $startLine = ($content | Select-String "async generatePageHTMLReport" | Select-Object -First 1).LineNumber
    if ($startLine) {
        # فحص 200 سطر من بداية الدالة
        $functionContent = $content[($startLine-1)..($startLine+199)]
        
        $extractCalls = $functionContent | Select-String "this\.extractRealDataFromDiscoveredVulnerability"
        $impactCalls = $functionContent | Select-String "this\.generateDynamicImpactForAnyVulnerability"
        
        Write-Host "      استدعاءات extractRealDataFromDiscoveredVulnerability: $($extractCalls.Count)" -ForegroundColor White
        Write-Host "      استدعاءات generateDynamicImpactForAnyVulnerability: $($impactCalls.Count)" -ForegroundColor White
        
        # يجب أن تكون قليلة جداً أو معدومة
        return ($extractCalls.Count -le 2 -and $impactCalls.Count -le 5)
    }
    return $false
}

# اختبار 6: فحص استخدام البيانات المحفوظة
Test-Function -TestName "فحص استخدام البيانات المحفوظة" -Description "التأكد من استخدام البيانات المحفوظة بدلاً من الاستدعاءات" -TestScript {
    $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js"
    
    $savedDataUsage = ($content | Select-String "vuln\.extracted_real_data.*vuln\.realData").Count
    $directUsage = ($content | Select-String "realData \|\| \{\}").Count
    
    Write-Host "      استخدام البيانات المحفوظة: $savedDataUsage" -ForegroundColor White
    Write-Host "      استخدام البيانات المباشرة: $directUsage" -ForegroundColor White
    
    return ($savedDataUsage -gt 0 -or $directUsage -gt 0)
}

Write-Host ""
Write-Host "=== تشغيل جميع الاختبارات ===" -ForegroundColor Yellow
Write-Host ""

# تشغيل جميع الاختبارات
$tests = @(
    @{Name="فحص الاستدعاءات المتكررة"; Description="التأكد من عدم وجود استدعاءات متكررة خطيرة"},
    @{Name="فحص دالة generatePageHTMLReport"; Description="التأكد من وجود دالة واحدة فقط"},
    @{Name="فحص extractRealDataFromDiscoveredVulnerability"; Description="التأكد من تقليل الاستدعاءات المتكررة"},
    @{Name="فحص generateDynamicImpactForAnyVulnerability"; Description="التأكد من عدم وجود استدعاءات متكررة داخلية"},
    @{Name="فحص استدعاءات generatePageHTMLReport"; Description="التأكد من عدم وجود استدعاءات متكررة خطيرة"},
    @{Name="فحص استخدام البيانات المحفوظة"; Description="التأكد من استخدام البيانات المحفوظة بدلاً من الاستدعاءات"}
)

Write-Host ""
Write-Host "=== النتائج النهائية ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "📊 إجمالي الاختبارات: $totalTests" -ForegroundColor White
Write-Host "✅ الاختبارات الناجحة: $passedTests" -ForegroundColor Green  
Write-Host "❌ الاختبارات الفاشلة: $failedTests" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "📈 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 80) { "Yellow" } else { "Red" })

Write-Host ""
if ($failedTests -eq 0) {
    Write-Host "🎉 تم حل مشكلة Maximum call stack size exceeded بالكامل!" -ForegroundColor Green
    Write-Host "✅ النظام جاهز للعمل بدون مشاكل في التصدير" -ForegroundColor Green
    Write-Host "✅ المرحلة 2/4: إنشاء تقرير HTML ستعمل بنجاح" -ForegroundColor Green
} else {
    Write-Host "🚨 لا تزال هناك مشاكل تحتاج إصلاح" -ForegroundColor Red
}

Write-Host ""
Write-Host "================================================================" -ForegroundColor Yellow
