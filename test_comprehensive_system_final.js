// 🔥 اختبار النظام المُحدث مع جميع الـ 36 دالة والقالب الشامل
console.log('🔥 اختبار النظام المُحدث مع جميع الـ 36 دالة والقالب الشامل');
console.log('=======================================================');

const fs = require('fs');

async function testUpdatedComprehensiveSystem() {
    try {
        console.log('📖 تحميل النظام المُحدث...');
        
        // قراءة ملف BugBountyCore المُحدث
        const filePath = './assets/modules/bugbounty/BugBountyCore.js';
        
        if (!fs.existsSync(filePath)) {
            console.log('❌ ملف BugBountyCore.js غير موجود');
            return;
        }
        
        const code = fs.readFileSync(filePath, 'utf8');
        
        // تنفيذ الكود مع معالجة مشاكل التصدير
        console.log('🔧 تحميل النظام المُحدث...');
        
        // إضافة تصدير مؤقت
        const modifiedCode = code + `
        
// تصدير مؤقت للاختبار
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { BugBountyCore };
} else if (typeof global !== 'undefined') {
    global.BugBountyCore = BugBountyCore;
}
        `;
        
        eval(modifiedCode);
        
        console.log('✅ تم تحميل النظام المُحدث بنجاح');
        
        // إنشاء مثيل من النظام
        let bugBountyCore;
        try {
            bugBountyCore = new BugBountyCore();
            console.log('✅ تم إنشاء مثيل النظام بنجاح');
        } catch (error) {
            console.log('⚠️ خطأ في إنشاء المثيل، سيتم المتابعة بدون مثيل');
        }
        
        // بيانات ثغرة حقيقية للاختبار
        const testVulnerability = {
            name: 'SQL Injection في نظام إدارة المستخدمين',
            type: 'SQL Injection',
            category: 'Database Security',
            severity: 'Critical',
            url: 'http://testphp.vulnweb.com/admin/users.php',
            parameter: 'user_id',
            payload: "1' UNION SELECT user(),database(),version(),@@version,@@datadir --",
            response: 'MySQL error: You have an error in your SQL syntax; Database version: MySQL 5.7.31',
            evidence: 'تم كشف إصدار قاعدة البيانات ومعلومات الخادم الحساسة',
            method: 'POST',
            confidence_level: 95,
            exploitation_complexity: 'منخفضة',
            business_impact: 'عالي - إمكانية الوصول لجميع بيانات النظام',
            affected_components: ['قاعدة البيانات', 'نظام المصادقة', 'بيانات المستخدمين'],
            security_implications: 'انتهاك الخصوصية وسرقة البيانات الحساسة',
            screenshots: ['before_attack.png', 'during_attack.png', 'after_attack.png'],
            real_data_extracted: true,
            tested_successfully: true,
            exploitation_successful: true
        };
        
        console.log('🔥 اختبار دالة generateVulnerabilitiesHTML المُحدثة...');

        // محاولة إنشاء مثيل جديد مع معالجة الأخطاء
        if (!bugBountyCore) {
            try {
                console.log('🔧 محاولة إنشاء مثيل جديد...');
                bugBountyCore = new BugBountyCore();
                console.log('✅ تم إنشاء مثيل جديد بنجاح');
            } catch (error) {
                console.log('⚠️ فشل في إنشاء مثيل جديد:', error.message);
            }
        }

        // فحص وجود الدالة
        console.log('🔍 فحص وجود دالة generateVulnerabilitiesHTML...');
        console.log(`   في المثيل: ${bugBountyCore && typeof bugBountyCore.generateVulnerabilitiesHTML}`);
        console.log(`   في الكلاس: ${typeof BugBountyCore.prototype.generateVulnerabilitiesHTML}`);

        if (bugBountyCore && bugBountyCore.generateVulnerabilitiesHTML) {
            console.log('✅ دالة generateVulnerabilitiesHTML موجودة في المثيل');

            // اختبار الدالة مع الثغرة
            console.log('🔧 تشغيل generateVulnerabilitiesHTML مع الثغرة الاختبارية...');

            const htmlResult = await bugBountyCore.generateVulnerabilitiesHTML([testVulnerability]);
        } else if (BugBountyCore.prototype.generateVulnerabilitiesHTML) {
            console.log('✅ دالة generateVulnerabilitiesHTML موجودة في الكلاس');

            // إنشاء مثيل مؤقت للاختبار
            const tempInstance = Object.create(BugBountyCore.prototype);
            tempInstance.extractRealDataFromDiscoveredVulnerability = function(vuln) {
                return {
                    url: vuln.url,
                    parameter: vuln.parameter,
                    payload: vuln.payload,
                    response: vuln.response,
                    evidence: vuln.evidence
                };
            };
            tempInstance.reportTemplate = null;
            tempInstance.loadReportTemplate = async function() { return null; };
            tempInstance.cleanVulnerabilityName = function(name) { return name; };

            // إضافة جميع الدوال المطلوبة
            tempInstance.generateComprehensiveDetailsFromRealData = async function(vuln, data) {
                return `تفاصيل شاملة للثغرة ${vuln.name}`;
            };
            tempInstance.generateDynamicImpactForAnyVulnerability = async function(vuln, data) {
                return `تحليل التأثير الديناميكي للثغرة ${vuln.name}`;
            };
            tempInstance.generateRealExploitationStepsForVulnerabilityComprehensive = async function(vuln, data) {
                return `خطوات الاستغلال الشاملة للثغرة ${vuln.name}`;
            };
            tempInstance.generateDynamicRecommendationsForVulnerability = async function(vuln, data) {
                return `التوصيات الديناميكية للثغرة ${vuln.name}`;
            };

            // إضافة باقي الدوال الـ 36
            const allFunctions = [
                'generateComprehensiveVulnerabilityAnalysis',
                'generateDynamicSecurityImpactAnalysis',
                'generateRealTimeVulnerabilityAssessment',
                'generateComprehensiveRiskAnalysis',
                'generateDynamicThreatModelingForVulnerability',
                'generateComprehensiveTestingDetails',
                'generateVisualChangesForVulnerability',
                'generatePersistentResultsForVulnerability',
                'generateImpactVisualizationsForVulnerability',
                'captureScreenshotForVulnerability',
                'generateBeforeAfterScreenshots',
                'generateAdvancedPayloadAnalysis',
                'generateComprehensiveResponseAnalysis',
                'generateDynamicExploitationChain',
                'generateRealTimeSecurityMetrics',
                'generateComprehensiveRemediationPlan',
                'generateComprehensiveDocumentation',
                'generateDetailedTechnicalReport',
                'generateExecutiveSummaryReport',
                'generateComplianceReport',
                'generateForensicAnalysisReport',
                'extractParameterFromDiscoveredVulnerability',
                'extractParametersFromUrl',
                'extractParameterFromPayload',
                'generateRealPayloadFromVulnerability',
                'analyzeVulnerabilityContext',
                'generateInteractiveDialogue',
                'generatePageHTMLReport',
                'generateFinalComprehensiveReport',
                'generateComprehensiveVulnerabilitiesContentUsingExistingFunctions',
                'generateRealDetailedDialogueFromDiscoveredVulnerability'
            ];

            allFunctions.forEach(funcName => {
                tempInstance[funcName] = async function(vuln, data) {
                    return `نتيجة ${funcName} للثغرة ${vuln.name}`;
                };
            });

            console.log('🔧 تشغيل generateVulnerabilitiesHTML مع المثيل المؤقت...');

            const htmlResult = await BugBountyCore.prototype.generateVulnerabilitiesHTML.call(tempInstance, [testVulnerability]);
            
            console.log('✅ تم تشغيل generateVulnerabilitiesHTML بنجاح');
            console.log(`📊 حجم النتيجة: ${htmlResult.length} حرف`);
            
            // فحص المحتوى للتأكد من استخدام جميع الـ 36 دالة
            console.log('🔍 فحص المحتوى للتأكد من استخدام جميع الـ 36 دالة...');
            
            const functionsToCheck = [
                'comprehensive_details',
                'exploitation_steps', 
                'dynamic_impact',
                'dynamic_recommendations',
                'comprehensive_analysis',
                'security_impact_analysis',
                'realtime_assessment',
                'risk_analysis',
                'threat_modeling',
                'testing_details',
                'visual_changes',
                'impact_visualizations',
                'screenshot_data',
                'before_after_screenshots',
                'payload_analysis',
                'response_analysis',
                'exploitation_chain',
                'security_metrics',
                'remediation_plan',
                'comprehensive_documentation',
                'technical_report',
                'executive_summary',
                'detailed_dialogue'
            ];
            
            let functionsFound = 0;
            functionsToCheck.forEach(func => {
                if (htmlResult.includes(func) || htmlResult.includes(func.replace('_', ' '))) {
                    functionsFound++;
                    console.log(`✅ تم العثور على: ${func}`);
                } else {
                    console.log(`⚠️ لم يتم العثور على: ${func}`);
                }
            });
            
            console.log(`📊 تم العثور على ${functionsFound}/${functionsToCheck.length} دالة في المحتوى`);
            
            // فحص استخدام القالب الشامل
            console.log('🔍 فحص استخدام القالب الشامل...');
            
            if (htmlResult.includes('comprehensive-section') || 
                htmlResult.includes('vulnerability-section') ||
                htmlResult.includes('تم استخدام جميع الـ 36 دالة')) {
                console.log('✅ تم استخدام القالب الشامل أو تنسيق شامل');
            } else {
                console.log('⚠️ لم يتم استخدام القالب الشامل');
            }
            
            // حفظ النتيجة للفحص
            const outputFile = `test_result_${Date.now()}.html`;
            fs.writeFileSync(outputFile, htmlResult, 'utf8');
            console.log(`📄 تم حفظ النتيجة: ${outputFile}`);
            
            // إحصائيات النتيجة
            console.log('');
            console.log('📊 إحصائيات النتيجة:');
            console.log(`   حجم HTML: ${htmlResult.length} حرف`);
            console.log(`   عدد الأسطر: ${htmlResult.split('\n').length}`);
            console.log(`   يحتوي على "شامل": ${htmlResult.includes('شامل') ? 'نعم' : 'لا'}`);
            console.log(`   يحتوي على "تفصيلي": ${htmlResult.includes('تفصيلي') ? 'نعم' : 'لا'}`);
            console.log(`   يحتوي على "36 دالة": ${htmlResult.includes('36') ? 'نعم' : 'لا'}`);
            
            return {
                success: true,
                htmlSize: htmlResult.length,
                functionsFound: functionsFound,
                totalFunctions: functionsToCheck.length,
                usesTemplate: htmlResult.includes('comprehensive-section'),
                outputFile: outputFile
            };
            
        } else {
            console.log('❌ دالة generateVulnerabilitiesHTML غير موجودة');
            return { success: false, error: 'Function not found' };
        }
        
    } catch (error) {
        console.log('❌ خطأ في اختبار النظام:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار
testUpdatedComprehensiveSystem().then(result => {
    console.log('');
    console.log('🏁 نتائج اختبار النظام المُحدث:');
    console.log('==============================');
    
    if (result.success) {
        console.log('🎉 الاختبار نجح!');
        console.log(`📊 حجم HTML: ${result.htmlSize} حرف`);
        console.log(`🔧 الدوال المستخدمة: ${result.functionsFound}/${result.totalFunctions}`);
        console.log(`📄 استخدام القالب: ${result.usesTemplate ? 'نعم' : 'لا'}`);
        console.log(`📁 ملف النتيجة: ${result.outputFile}`);
        
        if (result.functionsFound >= 15) {
            console.log('✅ النظام يستخدم معظم الدوال الشاملة التفصيلية');
        } else {
            console.log('⚠️ النظام لا يستخدم جميع الدوال الشاملة التفصيلية');
        }
        
        if (result.usesTemplate) {
            console.log('✅ النظام يستخدم القالب الشامل');
        } else {
            console.log('⚠️ النظام لا يستخدم القالب الشامل');
        }
        
    } else {
        console.log('❌ الاختبار فشل:', result.error);
    }
    
    console.log('🔥 اختبار النظام المُحدث مكتمل!');
});
