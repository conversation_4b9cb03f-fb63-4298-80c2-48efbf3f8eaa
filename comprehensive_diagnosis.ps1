# 🔥 تشخيص شامل لمشكلة عدم ظهور التفاصيل الشاملة التفصيلية
Write-Host "🔥 COMPREHENSIVE DIAGNOSIS - v4.0 SYSTEM ISSUES" -ForegroundColor Red
Write-Host "===============================================" -ForegroundColor Yellow

$bugBountyFile = "assets/modules/bugbounty/BugBountyCore.js"
$impactVisualizerFile = "assets/modules/bugbounty/impact_visualizer.js"
$textualAnalyzerFile = "assets/modules/bugbounty/textual_impact_analyzer.js"

# فحص وجود الملفات
Write-Host ""
Write-Host "📁 FILE EXISTENCE CHECK:" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Yellow

if (Test-Path $bugBountyFile) {
    $size1 = [math]::Round((Get-Item $bugBountyFile).Length / 1KB, 2)
    Write-Host "✅ BugBountyCore.js exists ($size1 KB)" -ForegroundColor Green
} else {
    Write-Host "❌ BugBountyCore.js missing" -ForegroundColor Red
}

if (Test-Path $impactVisualizerFile) {
    $size2 = [math]::Round((Get-Item $impactVisualizerFile).Length / 1KB, 2)
    Write-Host "✅ impact_visualizer.js exists ($size2 KB)" -ForegroundColor Green
} else {
    Write-Host "❌ impact_visualizer.js missing" -ForegroundColor Red
}

if (Test-Path $textualAnalyzerFile) {
    $size3 = [math]::Round((Get-Item $textualAnalyzerFile).Length / 1KB, 2)
    Write-Host "✅ textual_impact_analyzer.js exists ($size3 KB)" -ForegroundColor Green
} else {
    Write-Host "❌ textual_impact_analyzer.js missing" -ForegroundColor Red
}

# قراءة محتوى الملفات
$bugBountyContent = Get-Content $bugBountyFile -Raw
$impactVisualizerContent = Get-Content $impactVisualizerFile -Raw
$textualAnalyzerContent = Get-Content $textualAnalyzerFile -Raw

# فحص استخدام impact_visualizer
Write-Host ""
Write-Host "🔍 IMPACT_VISUALIZER USAGE CHECK:" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Yellow

$impactVisualizerUsage = @(
    "impact_visualizer",
    "ImpactVisualizer",
    "impactVisualizer",
    "initializeImpactVisualizer",
    "this.impactVisualizer"
)

$visualizerUsageFound = 0
foreach ($usage in $impactVisualizerUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "✅ Found: $usage" -ForegroundColor Green
        $visualizerUsageFound++
    } else {
        Write-Host "❌ Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "📊 Impact Visualizer Usage: $visualizerUsageFound/5" -ForegroundColor $(if ($visualizerUsageFound -ge 3) { "Green" } else { "Red" })

# فحص استخدام textual_impact_analyzer
Write-Host ""
Write-Host "🔍 TEXTUAL_IMPACT_ANALYZER USAGE CHECK:" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Yellow

$textualAnalyzerUsage = @(
    "textual_impact_analyzer",
    "TextualImpactAnalyzer",
    "textualImpactAnalyzer",
    "initializeTextualImpactAnalyzer",
    "this.textualImpactAnalyzer"
)

$textualUsageFound = 0
foreach ($usage in $textualAnalyzerUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "✅ Found: $usage" -ForegroundColor Green
        $textualUsageFound++
    } else {
        Write-Host "❌ Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "📊 Textual Analyzer Usage: $textualUsageFound/5" -ForegroundColor $(if ($textualUsageFound -ge 3) { "Green" } else { "Red" })

# فحص دالة generateComprehensiveDetailsFromRealData
Write-Host ""
Write-Host "🔍 COMPREHENSIVE DETAILS FUNCTION CHECK:" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Yellow

if ($bugBountyContent -match "async generateComprehensiveDetailsFromRealData") {
    Write-Host "✅ generateComprehensiveDetailsFromRealData function exists" -ForegroundColor Green
    
    # فحص إرجاع الكائن المعقد
    if ($bugBountyContent -match "technical_details.*impact_analysis.*exploitation_results") {
        Write-Host "✅ Function returns complex object structure" -ForegroundColor Green
    } else {
        Write-Host "❌ Function may not return complex object" -ForegroundColor Red
    }
    
    # فحص استخدام البيانات الحقيقية
    if ($bugBountyContent -match "extractRealDataFromDiscoveredVulnerability") {
        Write-Host "✅ Function uses real data extraction" -ForegroundColor Green
    } else {
        Write-Host "❌ Function may not extract real data" -ForegroundColor Red
    }
    
} else {
    Write-Host "❌ generateComprehensiveDetailsFromRealData function missing" -ForegroundColor Red
}

# فحص كيفية عرض البيانات في HTML
Write-Host ""
Write-Host "🔍 HTML DISPLAY ISSUE CHECK:" -ForegroundColor Cyan
Write-Host "============================" -ForegroundColor Yellow

# البحث عن استخدام comprehensive_details في HTML
$htmlUsagePatterns = @(
    'vuln\.comprehensive_details\s*\|\|',
    'comprehensive_details.*content',
    'technical_details\.comprehensive_description',
    'impact_analysis\.detailed_impact',
    'exploitation_results\.detailed_steps'
)

$htmlIssuesFound = 0
foreach ($pattern in $htmlUsagePatterns) {
    if ($bugBountyContent -match $pattern) {
        Write-Host "✅ Found HTML usage pattern: $pattern" -ForegroundColor Green
        $htmlIssuesFound++
    } else {
        Write-Host "❌ Missing HTML usage pattern: $pattern" -ForegroundColor Red
    }
}

Write-Host "📊 HTML Usage Patterns: $htmlIssuesFound/$($htmlUsagePatterns.Count)" -ForegroundColor $(if ($htmlIssuesFound -ge 2) { "Green" } else { "Red" })

# فحص مشكلة عرض الكائن كـ [object Object]
Write-Host ""
Write-Host "🚨 OBJECT DISPLAY ISSUE ANALYSIS:" -ForegroundColor Red
Write-Host "=================================" -ForegroundColor Yellow

# البحث عن الأماكن التي تعرض comprehensive_details مباشرة
$directDisplayMatches = [regex]::Matches($bugBountyContent, '\$\{vuln\.comprehensive_details[^}]*\}')
Write-Host "Found $($directDisplayMatches.Count) direct display usages of comprehensive_details"

if ($directDisplayMatches.Count -gt 0) {
    Write-Host "🚨 PROBLEM IDENTIFIED: comprehensive_details is displayed directly as object" -ForegroundColor Red
    Write-Host "🔧 SOLUTION NEEDED: Extract specific properties from the object" -ForegroundColor Yellow
    
    foreach ($match in $directDisplayMatches) {
        Write-Host "   Found: $($match.Value)" -ForegroundColor Yellow
    }
}

# فحص استخدام الصور
Write-Host ""
Write-Host "🔍 SCREENSHOT USAGE CHECK:" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Yellow

$screenshotUsage = @(
    "captureScreenshotForVulnerability",
    "generateBeforeAfterScreenshots",
    "screenshot_data",
    "visual_evidence",
    "screenshots"
)

$screenshotFound = 0
foreach ($usage in $screenshotUsage) {
    if ($bugBountyContent -match [regex]::Escape($usage)) {
        Write-Host "✅ Found: $usage" -ForegroundColor Green
        $screenshotFound++
    } else {
        Write-Host "❌ Missing: $usage" -ForegroundColor Red
    }
}

Write-Host "📊 Screenshot Usage: $screenshotFound/$($screenshotUsage.Count)" -ForegroundColor $(if ($screenshotFound -ge 3) { "Green" } else { "Red" })

# فحص التقارير المنفصلة
Write-Host ""
Write-Host "🔍 SEPARATE REPORTS CHECK:" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Yellow

if ($bugBountyContent -match "generatePageHTMLReport") {
    Write-Host "✅ generatePageHTMLReport function exists" -ForegroundColor Green
    
    # فحص استخدام التفاصيل الشاملة في التقارير المنفصلة
    if ($bugBountyContent -match "generatePageHTMLReport.*comprehensive.*details") {
        Write-Host "✅ Separate reports use comprehensive details" -ForegroundColor Green
    } else {
        Write-Host "❌ Separate reports may not use comprehensive details properly" -ForegroundColor Red
    }
} else {
    Write-Host "❌ generatePageHTMLReport function missing" -ForegroundColor Red
}

# الخلاصة والحلول
Write-Host ""
Write-Host "🎯 DIAGNOSIS SUMMARY:" -ForegroundColor Yellow
Write-Host "====================" -ForegroundColor Yellow

Write-Host "📊 Impact Visualizer Integration: $visualizerUsageFound/5" -ForegroundColor $(if ($visualizerUsageFound -ge 3) { "Green" } else { "Red" })
Write-Host "📊 Textual Analyzer Integration: $textualUsageFound/5" -ForegroundColor $(if ($textualUsageFound -ge 3) { "Green" } else { "Red" })
Write-Host "📊 HTML Display Issues: $htmlIssuesFound/$($htmlUsagePatterns.Count)" -ForegroundColor $(if ($htmlIssuesFound -ge 2) { "Green" } else { "Red" })
Write-Host "📊 Screenshot Integration: $screenshotFound/$($screenshotUsage.Count)" -ForegroundColor $(if ($screenshotFound -ge 3) { "Green" } else { "Red" })

Write-Host ""
Write-Host "🔧 REQUIRED FIXES:" -ForegroundColor Red
Write-Host "==================" -ForegroundColor Yellow

if ($directDisplayMatches.Count -gt 0) {
    Write-Host "1. ❌ Fix object display issue in HTML templates" -ForegroundColor Red
    Write-Host "   - Replace vuln.comprehensive_details with vuln.comprehensive_details.technical_details.comprehensive_description" -ForegroundColor Yellow
}

if ($visualizerUsageFound -lt 3) {
    Write-Host "2. ❌ Improve Impact Visualizer integration" -ForegroundColor Red
    Write-Host "   - Add proper initialization and usage of impact_visualizer.js" -ForegroundColor Yellow
}

if ($textualUsageFound -lt 3) {
    Write-Host "3. ❌ Improve Textual Analyzer integration" -ForegroundColor Red
    Write-Host "   - Add proper initialization and usage of textual_impact_analyzer.js" -ForegroundColor Yellow
}

if ($screenshotFound -lt 3) {
    Write-Host "4. ❌ Improve screenshot integration" -ForegroundColor Red
    Write-Host "   - Ensure screenshots are properly captured and displayed" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎉 DIAGNOSIS COMPLETE!" -ForegroundColor Green
