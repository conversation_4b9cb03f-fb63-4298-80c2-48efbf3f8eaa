# اختبار شامل للدالة المدمجة generatePageHTMLReport
Write-Host "🔧 اختبار شامل للدالة المدمجة generatePageHTMLReport" -ForegroundColor Green
Write-Host ""

# متغيرات النتائج
$totalTests = 0
$passedTests = 0
$failedTests = 0

function Test-MergedFunction {
    param(
        [string]$TestName,
        [string]$Description
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
        
        # فحص عدد دوال generatePageHTMLReport
        $generatePageHTMLReportCount = ($content | Select-String "async generatePageHTMLReport").Count
        
        if ($generatePageHTMLReportCount -eq 1) {
            Write-Host "   ✅ يوجد دالة واحدة فقط - تم الدمج بنجاح" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ يوجد $generatePageHTMLReportCount دوال - لم يتم الدمج بشكل صحيح" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-36Functions {
    param(
        [string]$TestName,
        [string]$Description
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
        
        # فحص وجود الـ36 دالة في الدالة المدمجة
        $functions36Count = ($content | Select-String "تطبيق جميع الـ 36 دالة الشاملة التفصيلية").Count
        
        if ($functions36Count -gt 0) {
            Write-Host "   ✅ تم العثور على $functions36Count مرجع للـ36 دالة" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ لم يتم العثور على مراجع الـ36 دالة" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-NoInfiniteRecursion {
    param(
        [string]$TestName,
        [string]$Description
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
        
        # البحث عن استدعاءات متكررة خطيرة
        $dangerousPatterns = @(
            "generatePageHTMLReport.*generatePageHTMLReport",
            "generateComprehensiveDetailsFromRealData.*generateComprehensiveDetailsFromRealData",
            "generateFinalComprehensiveReport.*generateFinalComprehensiveReport"
        )
        
        $foundDangerous = $false
        foreach ($pattern in $dangerousPatterns) {
            $matches = $content | Select-String -Pattern $pattern
            if ($matches) {
                Write-Host "   ❌ تم العثور على استدعاء متكرر خطير: $pattern" -ForegroundColor Red
                $foundDangerous = $true
            }
        }
        
        if (-not $foundDangerous) {
            Write-Host "   ✅ لم يتم العثور على استدعاءات متكررة خطيرة" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

function Test-ImpactVisualizerIntegration {
    param(
        [string]$TestName,
        [string]$Description
    )
    
    $global:totalTests++
    Write-Host "🔍 اختبار: $TestName" -ForegroundColor Yellow
    Write-Host "   الوصف: $Description" -ForegroundColor Cyan
    
    try {
        $content = Get-Content "assets\modules\bugbounty\BugBountyCore.js" -ErrorAction Stop
        
        # فحص وجود استخدام impact_visualizer.js
        $impactVisualizerCount = ($content | Select-String "ImpactVisualizer").Count
        $textualAnalyzerCount = ($content | Select-String "TextualImpactAnalyzer").Count
        
        if ($impactVisualizerCount -gt 0 -and $textualAnalyzerCount -gt 0) {
            Write-Host "   ✅ تم العثور على استخدام impact_visualizer.js ($impactVisualizerCount) و textual_impact_analyzer.js ($textualAnalyzerCount)" -ForegroundColor Green
            $global:passedTests++
            return $true
        } else {
            Write-Host "   ❌ لم يتم العثور على استخدام الملفات المطلوبة" -ForegroundColor Red
            Write-Host "      - impact_visualizer.js: $impactVisualizerCount" -ForegroundColor Red
            Write-Host "      - textual_impact_analyzer.js: $textualAnalyzerCount" -ForegroundColor Red
            $global:failedTests++
            return $false
        }
    } catch {
        Write-Host "   ❌ خطأ في الاختبار: $($_.Exception.Message)" -ForegroundColor Red
        $global:failedTests++
        return $false
    }
}

Write-Host "=== بدء الاختبارات الشاملة للدالة المدمجة ===" -ForegroundColor Yellow
Write-Host ""

# تشغيل الاختبارات
Test-MergedFunction -TestName "فحص دمج الدوال" -Description "التأكد من وجود دالة واحدة فقط generatePageHTMLReport"
Test-36Functions -TestName "فحص الـ36 دالة" -Description "التأكد من وجود جميع الـ36 دالة الشاملة التفصيلية"
Test-NoInfiniteRecursion -TestName "فحص عدم التكرار اللا نهائي" -Description "التأكد من عدم وجود استدعاءات متكررة خطيرة"
Test-ImpactVisualizerIntegration -TestName "فحص تكامل الملفات" -Description "التأكد من استخدام impact_visualizer.js و textual_impact_analyzer.js"

Write-Host ""
Write-Host "=== النتائج النهائية ===" -ForegroundColor Yellow
Write-Host ""
Write-Host "📊 إجمالي الاختبارات: $totalTests" -ForegroundColor White
Write-Host "✅ الاختبارات الناجحة: $passedTests" -ForegroundColor Green  
Write-Host "❌ الاختبارات الفاشلة: $failedTests" -ForegroundColor Red

$successRate = if ($totalTests -gt 0) { [math]::Round(($passedTests / $totalTests) * 100, 2) } else { 0 }
Write-Host "📈 معدل النجاح: $successRate%" -ForegroundColor $(if ($successRate -eq 100) { "Green" } elseif ($successRate -ge 75) { "Yellow" } else { "Red" })

Write-Host ""
if ($failedTests -eq 0) {
    Write-Host "🎉 تم دمج الدوال بنجاح! النظام جاهز للعمل بدون مشاكل Maximum call stack size exceeded." -ForegroundColor Green
    Write-Host "🔥 الدالة المدمجة تحتوي على:" -ForegroundColor Green
    Write-Host "   ✅ تحميل القالب من الملف" -ForegroundColor Green
    Write-Host "   ✅ جميع الـ36 دالة الشاملة التفصيلية" -ForegroundColor Green
    Write-Host "   ✅ استخدام impact_visualizer.js" -ForegroundColor Green
    Write-Host "   ✅ استخدام textual_impact_analyzer.js" -ForegroundColor Green
    Write-Host "   ✅ إنشاء HTML شامل تفصيلي" -ForegroundColor Green
} else {
    Write-Host "🚨 هناك مشاكل تحتاج إصلاح فوري." -ForegroundColor Red
}

Write-Host ""
Write-Host "🔧 انتهى اختبار الدالة المدمجة" -ForegroundColor Green
