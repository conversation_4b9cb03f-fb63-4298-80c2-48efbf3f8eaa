<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎯 التحقق من النظام الشامل التفصيلي v4.0</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
            direction: rtl;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        .stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .stat {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 التحقق من النظام الشامل التفصيلي v4.0</h1>
        
        <div class="stats">
            <div class="stat">
                <div class="stat-number" id="totalTests">0</div>
                <div>إجمالي الاختبارات</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="passedTests">0</div>
                <div>الاختبارات الناجحة</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="successRate">0%</div>
                <div>معدل النجاح</div>
            </div>
            <div class="stat">
                <div class="stat-number" id="functionsFound">0</div>
                <div>الدوال الموجودة</div>
            </div>
        </div>
        
        <button class="btn" onclick="runSystemVerification()">🚀 تشغيل التحقق الشامل</button>
        <button class="btn" onclick="clearResults()">🗑️ مسح النتائج</button>
        
        <div id="results" class="result"></div>
    </div>

    <script>
        let totalTests = 0;
        let passedTests = 0;
        let functionsFound = 0;
        
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'warning' ? 'warning' : 'success';
            resultsDiv.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
            console.log(message);
        }
        
        function updateStats() {
            document.getElementById('totalTests').textContent = totalTests;
            document.getElementById('passedTests').textContent = passedTests;
            document.getElementById('successRate').textContent = totalTests > 0 ? Math.round(passedTests / totalTests * 100) + '%' : '0%';
            document.getElementById('functionsFound').textContent = functionsFound;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            totalTests = 0;
            passedTests = 0;
            functionsFound = 0;
            updateStats();
        }
        
        async function loadBugBountyCore() {
            try {
                log('🔄 محاولة تحميل BugBountyCore...');
                
                // محاولة تحميل الملف
                const script = document.createElement('script');
                script.src = './assets/modules/bugbounty/BugBountyCore.js';
                
                return new Promise((resolve, reject) => {
                    script.onload = () => {
                        try {
                            if (typeof BugBountyCore !== 'undefined') {
                                const core = new BugBountyCore();
                                log('✅ تم تحميل BugBountyCore بنجاح');
                                resolve(core);
                            } else {
                                log('❌ BugBountyCore غير معرف بعد التحميل', 'error');
                                reject(new Error('BugBountyCore not defined'));
                            }
                        } catch (error) {
                            log(`❌ خطأ في إنشاء BugBountyCore: ${error.message}`, 'error');
                            reject(error);
                        }
                    };
                    
                    script.onerror = () => {
                        log('❌ فشل في تحميل ملف BugBountyCore.js', 'error');
                        reject(new Error('Failed to load script'));
                    };
                    
                    document.head.appendChild(script);
                });
            } catch (error) {
                log(`❌ خطأ في تحميل BugBountyCore: ${error.message}`, 'error');
                throw error;
            }
        }
        
        async function testComprehensiveFunctions(bugBountyCore) {
            log('🔧 اختبار الدوال الشاملة التفصيلية...');
            
            const comprehensiveFunctions = [
                'generateComprehensiveDetailsFromRealData',
                'extractRealDataFromDiscoveredVulnerability',
                'formatComprehensiveVulnerabilitySection',
                'generateDynamicImpactForAnyVulnerability',
                'generateRealExploitationStepsForVulnerabilityComprehensive',
                'generateDynamicRecommendationsForVulnerability',
                'generateRealVisualChangesForVulnerability',
                'generateRealPersistentResultsForVulnerability',
                'generateFinalComprehensiveReport',
                'formatSinglePageReport'
            ];
            
            let localFunctionsFound = 0;
            
            for (const funcName of comprehensiveFunctions) {
                totalTests++;
                if (typeof bugBountyCore[funcName] === 'function') {
                    log(`✅ ${funcName} - موجودة وجاهزة`);
                    localFunctionsFound++;
                    passedTests++;
                } else {
                    log(`❌ ${funcName} - غير موجودة`, 'error');
                }
            }
            
            functionsFound = localFunctionsFound;
            
            const functionSuccessRate = (localFunctionsFound / comprehensiveFunctions.length * 100).toFixed(1);
            log(`📊 نتائج اختبار الدوال: ${localFunctionsFound}/${comprehensiveFunctions.length} (${functionSuccessRate}%)`);
            
            return localFunctionsFound === comprehensiveFunctions.length;
        }
        
        async function testMainReportFunction(bugBountyCore) {
            log('📊 اختبار دالة التقرير الرئيسي...');
            totalTests++;
            
            try {
                if (typeof bugBountyCore.generateFinalComprehensiveReport === 'function') {
                    log('✅ دالة generateFinalComprehensiveReport موجودة');
                    passedTests++;
                    return true;
                } else {
                    log('❌ دالة generateFinalComprehensiveReport غير موجودة', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقرير الرئيسي: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testSeparateReportFunction(bugBountyCore) {
            log('📋 اختبار دالة التقارير المنفصلة...');
            totalTests++;
            
            try {
                if (typeof bugBountyCore.formatSinglePageReport === 'function') {
                    log('✅ دالة formatSinglePageReport موجودة');
                    passedTests++;
                    return true;
                } else {
                    log('❌ دالة formatSinglePageReport غير موجودة', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار التقارير المنفصلة: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function testComprehensiveDetailsFunction(bugBountyCore) {
            log('🔥 اختبار الدالة الشاملة التفصيلية الرئيسية...');
            totalTests++;
            
            try {
                const testVuln = {
                    name: 'Test XSS Vulnerability',
                    type: 'XSS',
                    severity: 'High',
                    url: 'http://example.com/test',
                    payload: '<script>alert("test")</script>',
                    evidence: 'Test evidence'
                };
                
                const testRealData = {
                    payload: '<script>alert("test")</script>',
                    response: 'Script executed',
                    evidence: 'Alert appeared',
                    url: 'http://example.com/test'
                };
                
                log('📝 اختبار generateComprehensiveDetailsFromRealData...');
                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, testRealData);
                
                if (result && typeof result === 'object' && Object.keys(result).length > 0) {
                    log('✅ generateComprehensiveDetailsFromRealData تعمل وترجع بيانات صحيحة');
                    log(`📊 الكائن المُرجع يحتوي على ${Object.keys(result).length} مفتاح`);
                    passedTests++;
                    return true;
                } else {
                    log('❌ generateComprehensiveDetailsFromRealData لا ترجع بيانات صحيحة', 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الدالة الشاملة: ${error.message}`, 'error');
                return false;
            }
        }
        
        async function runSystemVerification() {
            clearResults();
            log('🚀 بدء التحقق الشامل من النظام v4.0');
            
            try {
                // تحميل النظام
                const bugBountyCore = await loadBugBountyCore();
                
                // اختبار الدوال
                const functionsTest = await testComprehensiveFunctions(bugBountyCore);
                const mainReportTest = await testMainReportFunction(bugBountyCore);
                const separateReportTest = await testSeparateReportFunction(bugBountyCore);
                const comprehensiveTest = await testComprehensiveDetailsFunction(bugBountyCore);
                
                // النتائج النهائية
                const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
                
                log('🏁 انتهى التحقق من النظام');
                log(`📊 النتائج النهائية: ${passedTests}/${totalTests} (${successRate}%)`);
                
                if (passedTests === totalTests) {
                    log('🎉 النظام الشامل التفصيلي v4.0 يعمل بكفاءة 100%');
                    log('✅ جميع التقارير تستخدم الدوال الشاملة التفصيلية تلقائياً وديناميكياً');
                    log('🔥 لا يوجد محتوى عام أو افتراضي أو يدوي');
                } else if (passedTests > totalTests * 0.8) {
                    log('✅ النظام الشامل التفصيلي v4.0 يعمل بكفاءة عالية');
                } else {
                    log('⚠️ النظام الشامل التفصيلي v4.0 يحتاج مراجعة', 'warning');
                }
                
                updateStats();
                
            } catch (error) {
                log(`❌ فشل في التحقق من النظام: ${error.message}`, 'error');
                updateStats();
            }
        }
        
        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runSystemVerification, 1000);
        });
    </script>
</body>
</html>
