# Test v4.0 Comprehensive System Improvements
Write-Host "Test v4.0 Comprehensive System Improvements" -ForegroundColor Red
Write-Host "=============================================" -ForegroundColor Yellow

# Check if files exist
$bugBountyFile = ".\assets\modules\bugbounty\BugBountyCore.js"
$templateFile = ".\assets\modules\bugbounty\report_template.html"

if (-not (Test-Path $bugBountyFile)) {
    Write-Host "ERROR: BugBountyCore.js not found" -ForegroundColor Red
    exit 1
}

if (-not (Test-Path $templateFile)) {
    Write-Host "ERROR: report_template.html not found" -ForegroundColor Red
    exit 1
}

Write-Host "SUCCESS: All required files found" -ForegroundColor Green

# Read file contents
$bugBountyContent = Get-Content $bugBountyFile -Raw -Encoding UTF8
$templateContent = Get-Content $templateFile -Raw -Encoding UTF8

Write-Host "SUCCESS: Files read successfully" -ForegroundColor Green

# Test 1: Check extractRealDataFromDiscoveredVulnerability improvements
Write-Host "Test 1: Check extractRealDataFromDiscoveredVulnerability improvements" -ForegroundColor Cyan

$hasRealDataExtraction = $bugBountyContent -match "discoveredVuln\.parameter.*discoveredVuln\.payload.*discoveredVuln\.url"
$hasResponseExtraction = $bugBountyContent -match "discoveredVuln\.response.*discoveredVuln\.evidence"
$hasImprovedLogging = $bugBountyContent -match "استخراج البيانات الحقيقية من الثغرة المكتشفة والمختبرة"

if ($hasRealDataExtraction -and $hasResponseExtraction -and $hasImprovedLogging) {
    Write-Host "   SUCCESS: extractRealDataFromDiscoveredVulnerability improved" -ForegroundColor Green
} else {
    Write-Host "   ERROR: extractRealDataFromDiscoveredVulnerability needs improvement" -ForegroundColor Red
}

# Test 2: Check generateDynamicImpactForAnyVulnerability improvements
Write-Host "Test 2: Check generateDynamicImpactForAnyVulnerability improvements" -ForegroundColor Cyan

$usesExtractedData = $bugBountyContent -match "extractRealDataFromDiscoveredVulnerability.*extractedRealData"
$hasImprovedDefaults = $bugBountyContent -match "payload حقيقي للثغرة.*الموقع المستهدف للثغرة"

if ($usesExtractedData -and $hasImprovedDefaults) {
    Write-Host "   SUCCESS: generateDynamicImpactForAnyVulnerability improved" -ForegroundColor Green
} else {
    Write-Host "   ERROR: generateDynamicImpactForAnyVulnerability needs improvement" -ForegroundColor Red
}

# Test 3: Check new extractParameterFromDiscoveredVulnerability function
Write-Host "Test 3: Check new extractParameterFromDiscoveredVulnerability function" -ForegroundColor Cyan

$hasNewFunction = $bugBountyContent -match "extractParameterFromDiscoveredVulnerability\(discoveredVuln\)"
$hasParameterSearch = $bugBountyContent -match "discoveredVuln\.parameter.*discoveredVuln\.affected_parameter.*discoveredVuln\.vulnerable_param"
$hasUrlExtraction = $bugBountyContent -match "extractParametersFromUrl.*extractParameterFromPayload"

if ($hasNewFunction -and $hasParameterSearch -and $hasUrlExtraction) {
    Write-Host "   SUCCESS: extractParameterFromDiscoveredVulnerability function added" -ForegroundColor Green
} else {
    Write-Host "   ERROR: extractParameterFromDiscoveredVulnerability function missing" -ForegroundColor Red
}

# Test 4: Check template usage
Write-Host "Test 4: Check comprehensive template usage" -ForegroundColor Cyan

$templateVariables = @("VULNERABILITIES_CONTENT", "TESTING_DETAILS", "INTERACTIVE_DIALOGUES", "VISUAL_CHANGES", "PERSISTENT_RESULTS", "IMPACT_VISUALIZATIONS")
$foundVariables = 0

foreach ($variable in $templateVariables) {
    if ($templateContent -match $variable) {
        $foundVariables++
    }
}

$usesTemplate = $bugBountyContent -match "report_template\.html.*VULNERABILITIES_CONTENT"

if ($foundVariables -eq $templateVariables.Count -and $usesTemplate) {
    Write-Host "   SUCCESS: Comprehensive template is used correctly ($foundVariables/$($templateVariables.Count) variables)" -ForegroundColor Green
} else {
    Write-Host "   ERROR: Template usage incomplete ($foundVariables/$($templateVariables.Count) variables)" -ForegroundColor Red
}

# Test 5: Check comprehensive functions
Write-Host "Test 5: Check comprehensive detailed functions" -ForegroundColor Cyan

$comprehensiveFunctions = @(
    "generateComprehensiveDetailsFromRealData",
    "generateDynamicImpactForAnyVulnerability", 
    "generateRealExploitationStepsForVulnerabilityComprehensive",
    "generateDynamicRecommendationsForVulnerability",
    "generateRealDetailedDialogueFromDiscoveredVulnerability"
)

$foundFunctions = 0
foreach ($func in $comprehensiveFunctions) {
    if ($bugBountyContent -match $func) {
        $foundFunctions++
    }
}

if ($foundFunctions -eq $comprehensiveFunctions.Count) {
    Write-Host "   SUCCESS: All comprehensive functions found ($foundFunctions/$($comprehensiveFunctions.Count))" -ForegroundColor Green
} else {
    Write-Host "   ERROR: Some comprehensive functions missing ($foundFunctions/$($comprehensiveFunctions.Count))" -ForegroundColor Red
}

# Test 6: Check for generic content removal
Write-Host "Test 6: Check for generic content removal" -ForegroundColor Cyan

$genericCount = 0
$genericTexts = @("معامل مكتشف", "payload_discovered", "target_discovered", "موقع مستهدف")

foreach ($text in $genericTexts) {
    if ($bugBountyContent -match [regex]::Escape($text)) {
        $genericCount++
        Write-Host "   WARNING: Found generic text: $text" -ForegroundColor Yellow
    }
}

if ($genericCount -eq 0) {
    Write-Host "   SUCCESS: No generic content found" -ForegroundColor Green
} else {
    Write-Host "   WARNING: Found $genericCount generic texts" -ForegroundColor Yellow
}

# Test 7: Create sample vulnerability and test data extraction
Write-Host "Test 7: Create sample vulnerability for testing" -ForegroundColor Cyan

$sampleVuln = @"
{
    "name": "SQL Injection Test",
    "type": "SQL Injection",
    "severity": "Critical",
    "url": "http://testphp.vulnweb.com/login.php",
    "parameter": "username",
    "payload": "admin' OR '1'='1' --",
    "tested_payload": "admin' OR '1'='1' --",
    "response": "Login successful - Authentication bypassed",
    "evidence": "Successfully logged in without valid credentials",
    "exploitation_response": "Database access gained",
    "exploitation_evidence": "User data extracted"
}
"@

Write-Host "   Sample vulnerability created:" -ForegroundColor White
Write-Host "   Name: SQL Injection Test" -ForegroundColor White
Write-Host "   Parameter: username" -ForegroundColor White
Write-Host "   Payload: admin' OR '1'='1' --" -ForegroundColor White
Write-Host "   URL: http://testphp.vulnweb.com/login.php" -ForegroundColor White

# Final Results
Write-Host ""
Write-Host "FINAL TEST RESULTS:" -ForegroundColor Yellow
Write-Host "==================" -ForegroundColor Yellow

$tests = @(
    ($hasRealDataExtraction -and $hasResponseExtraction -and $hasImprovedLogging),
    ($usesExtractedData -and $hasImprovedDefaults),
    ($hasNewFunction -and $hasParameterSearch -and $hasUrlExtraction),
    ($foundVariables -eq $templateVariables.Count -and $usesTemplate),
    ($foundFunctions -eq $comprehensiveFunctions.Count),
    ($genericCount -eq 0)
)

$passedTests = ($tests | Where-Object { $_ }).Count
$totalTests = $tests.Count

Write-Host "Tests Passed: $passedTests/$totalTests" -ForegroundColor White

if ($passedTests -eq $totalTests) {
    Write-Host "SUCCESS: All improvements implemented successfully!" -ForegroundColor Green
    Write-Host "SUCCESS: v4.0 Comprehensive System works with real data" -ForegroundColor Green
    Write-Host "SUCCESS: Reports will contain real vulnerability details" -ForegroundColor Green
    Write-Host "SUCCESS: No generic or default content" -ForegroundColor Green
} elseif ($passedTests -ge ($totalTests * 0.8)) {
    Write-Host "PARTIAL: Most improvements implemented successfully!" -ForegroundColor Yellow
    Write-Host "WARNING: Some minor issues may need attention" -ForegroundColor Yellow
} else {
    Write-Host "ERROR: Improvements need more work" -ForegroundColor Red
    Write-Host "ERROR: System may not work with real data" -ForegroundColor Red
}

Write-Host ""
Write-Host "v4.0 Comprehensive System Test Complete!" -ForegroundColor Red
