// 🔥 اختبار استخراج البيانات الحقيقية من الثغرات

console.log('🔥 اختبار استخراج البيانات الحقيقية من الثغرات');
console.log('===============================================');

const fs = require('fs');

try {
    // قراءة ملف BugBountyCore
    const filePath = './assets/modules/bugbounty/BugBountyCore.js';
    
    if (!fs.existsSync(filePath)) {
        console.log('❌ ملف BugBountyCore.js غير موجود');
        process.exit(1);
    }
    
    console.log('✅ ملف BugBountyCore.js موجود');
    
    // قراءة المحتوى
    const code = fs.readFileSync(filePath, 'utf8');
    console.log('✅ تم قراءة الملف بنجاح');
    
    // فحص التحسينات في الكود
    console.log('🔍 فحص التحسينات في استخراج البيانات الحقيقية:');
    
    // فحص دالة extractRealDataFromDiscoveredVulnerability
    const extractFunctionMatch = code.match(/extractRealDataFromDiscoveredVulnerability\([\s\S]*?\{[\s\S]*?\}/);
    if (extractFunctionMatch) {
        const extractFunction = extractFunctionMatch[0];
        
        // فحص استخدام البيانات الحقيقية
        const hasRealDataExtraction = 
            extractFunction.includes('discoveredVuln.parameter') &&
            extractFunction.includes('discoveredVuln.payload') &&
            extractFunction.includes('discoveredVuln.url') &&
            extractFunction.includes('discoveredVuln.response') &&
            extractFunction.includes('discoveredVuln.evidence');
        
        if (hasRealDataExtraction) {
            console.log('  ✅ دالة استخراج البيانات تستخدم البيانات الحقيقية');
        } else {
            console.log('  ❌ دالة استخراج البيانات لا تستخدم البيانات الحقيقية');
        }
        
        // فحص عدم استخدام القيم الافتراضية العامة
        const hasGenericDefaults = 
            extractFunction.includes("'معامل مكتشف'") ||
            extractFunction.includes("'موقع مستهدف'") ||
            extractFunction.includes("'payload_discovered'") ||
            extractFunction.includes("'target_discovered'");
        
        if (!hasGenericDefaults) {
            console.log('  ✅ دالة استخراج البيانات لا تستخدم قيم افتراضية عامة');
        } else {
            console.log('  ⚠️ دالة استخراج البيانات تستخدم بعض القيم الافتراضية العامة');
        }
    } else {
        console.log('  ❌ لم يتم العثور على دالة استخراج البيانات');
    }
    
    // فحص دالة generateDynamicImpactForAnyVulnerability
    console.log('🔍 فحص دالة التأثير الديناميكي:');
    
    const impactFunctionMatch = code.match(/generateDynamicImpactForAnyVulnerability\([\s\S]*?\{[\s\S]*?extractRealDataFromDiscoveredVulnerability[\s\S]*?\}/);
    if (impactFunctionMatch) {
        const impactFunction = impactFunctionMatch[0];
        
        // فحص استخدام البيانات المستخرجة
        const usesExtractedData = 
            impactFunction.includes('extractRealDataFromDiscoveredVulnerability') &&
            impactFunction.includes('extractedRealData') &&
            impactFunction.includes('realData?.payload') &&
            impactFunction.includes('vulnerability.payload');
        
        if (usesExtractedData) {
            console.log('  ✅ دالة التأثير الديناميكي تستخدم البيانات المستخرجة');
        } else {
            console.log('  ❌ دالة التأثير الديناميكي لا تستخدم البيانات المستخرجة');
        }
        
        // فحص تحسين القيم الافتراضية
        const hasImprovedDefaults = 
            impactFunction.includes('payload حقيقي للثغرة') ||
            impactFunction.includes('الموقع المستهدف للثغرة');
        
        if (hasImprovedDefaults) {
            console.log('  ✅ دالة التأثير الديناميكي تستخدم قيم افتراضية محسنة');
        } else {
            console.log('  ❌ دالة التأثير الديناميكي لا تستخدم قيم افتراضية محسنة');
        }
    } else {
        console.log('  ❌ لم يتم العثور على دالة التأثير الديناميكي المحسنة');
    }
    
    // فحص دالة extractParameterFromDiscoveredVulnerability الجديدة
    console.log('🔍 فحص دالة استخراج المعامل الجديدة:');
    
    const paramFunctionExists = code.includes('extractParameterFromDiscoveredVulnerability(discoveredVuln)');
    if (paramFunctionExists) {
        console.log('  ✅ دالة استخراج المعامل الجديدة موجودة');
        
        const hasParameterExtraction = 
            code.includes('discoveredVuln.parameter') &&
            code.includes('discoveredVuln.affected_parameter') &&
            code.includes('discoveredVuln.vulnerable_param') &&
            code.includes('discoveredVuln.injection_point');
        
        if (hasParameterExtraction) {
            console.log('  ✅ دالة استخراج المعامل تبحث في جميع الحقول المحتملة');
        } else {
            console.log('  ❌ دالة استخراج المعامل لا تبحث في جميع الحقول المحتملة');
        }
        
        const hasUrlParameterExtraction = code.includes('extractParametersFromUrl');
        const hasPayloadParameterExtraction = code.includes('extractParameterFromPayload');
        
        if (hasUrlParameterExtraction && hasPayloadParameterExtraction) {
            console.log('  ✅ دالة استخراج المعامل تستخرج من URL و Payload');
        } else {
            console.log('  ❌ دالة استخراج المعامل لا تستخرج من URL و Payload');
        }
    } else {
        console.log('  ❌ دالة استخراج المعامل الجديدة غير موجودة');
    }
    
    // فحص استخدام القالب الشامل
    console.log('🔍 فحص استخدام القالب الشامل:');
    
    const templateUsage = code.includes('report_template.html') && 
                         code.includes('VULNERABILITIES_CONTENT') &&
                         code.includes('generateComprehensiveVulnerabilitiesContentUsingExistingFunctions');
    
    if (templateUsage) {
        console.log('  ✅ النظام يستخدم القالب الشامل الموجود في المشروع');
    } else {
        console.log('  ❌ النظام لا يستخدم القالب الشامل بشكل صحيح');
    }
    
    // فحص الدوال الشاملة التفصيلية
    console.log('🔍 فحص الدوال الشاملة التفصيلية:');
    
    const comprehensiveFunctions = [
        'generateComprehensiveDetailsFromRealData',
        'generateDynamicImpactForAnyVulnerability',
        'generateRealExploitationStepsForVulnerabilityComprehensive',
        'generateDynamicRecommendationsForVulnerability',
        'generateRealDetailedDialogueFromDiscoveredVulnerability'
    ];
    
    let functionsFound = 0;
    for (const func of comprehensiveFunctions) {
        if (code.includes(func)) {
            functionsFound++;
        }
    }
    
    console.log(`  📊 الدوال الشاملة الموجودة: ${functionsFound}/${comprehensiveFunctions.length}`);
    
    if (functionsFound === comprehensiveFunctions.length) {
        console.log('  ✅ جميع الدوال الشاملة التفصيلية موجودة');
    } else {
        console.log('  ❌ بعض الدوال الشاملة التفصيلية مفقودة');
    }
    
    // النتائج النهائية
    console.log('');
    console.log('🏁 نتائج الفحص النهائية:');
    
    const improvements = [
        extractFunctionMatch && code.includes('discoveredVuln.parameter'),
        impactFunctionMatch && code.includes('extractRealDataFromDiscoveredVulnerability'),
        paramFunctionExists,
        templateUsage,
        functionsFound === comprehensiveFunctions.length
    ];
    
    const successfulImprovements = improvements.filter(Boolean).length;
    const totalImprovements = improvements.length;
    
    console.log(`📊 التحسينات المطبقة: ${successfulImprovements}/${totalImprovements}`);
    
    if (successfulImprovements === totalImprovements) {
        console.log('🎉🎉🎉 جميع التحسينات مطبقة بنجاح!');
        console.log('✅ النظام الشامل التفصيلي v4.0 محسن ويستخدم البيانات الحقيقية');
        console.log('🔥 التقارير ستحتوي على التفاصيل الحقيقية للثغرات المكتشفة والمختبرة');
        console.log('🎯 لا يوجد محتوى عام أو افتراضي مثل "معامل مكتشف"');
    } else if (successfulImprovements >= totalImprovements * 0.8) {
        console.log('🎉 معظم التحسينات مطبقة بنجاح!');
        console.log('✅ النظام محسن بشكل كبير');
        console.log('⚠️ قد تحتاج بعض التحسينات الإضافية');
    } else {
        console.log('⚠️ التحسينات جزئية');
        console.log('❌ يحتاج المزيد من العمل لتطبيق جميع التحسينات');
    }
    
} catch (error) {
    console.log('❌ خطأ في الفحص:', error.message);
    process.exit(1);
}
