# Test ACTUAL usage of 36 functions and files in existing reports
Write-Host "=== TESTING ACTUAL USAGE IN REPORTS ===" -ForegroundColor Red
Write-Host "Checking if 36 functions and files are ACTUALLY used in generated reports" -ForegroundColor Yellow

$TestResults = @{
    ReportsFound = 0
    Functions36Used = 0
    FilesUsed = 0
    ComprehensiveContent = 0
    DynamicContent = 0
    TotalScore = 0
}

function Test-ReportContent {
    param($ReportPath, $SearchTexts)
    if (Test-Path $ReportPath) {
        $content = Get-Content $ReportPath -Raw -Encoding UTF8
        $matches = 0
        foreach ($text in $SearchTexts) {
            if ($content -like "*$text*") { $matches++ }
        }
        return $matches
    }
    return 0
}

Write-Host "`n1. Finding existing reports..." -ForegroundColor Cyan

# Find all HTML reports
$reportFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse | Where-Object { 
    $_.Name -like "*report*" -or 
    $_.Name -like "*bug*" -or 
    $_.Name -like "*vulnerability*" -or
    $_.Name -like "*comprehensive*" -or
    $_.Name -like "*main*" -or
    $_.Name -like "*separate*" -or
    $_.Name -like "*test*"
}

$TestResults.ReportsFound = $reportFiles.Count
Write-Host "  Found $($reportFiles.Count) report files" -ForegroundColor $(if ($reportFiles.Count -gt 0) { "Green" } else { "Red" })

if ($reportFiles.Count -eq 0) {
    Write-Host "  [ERROR] No report files found to test" -ForegroundColor Red
    exit
}

# Show found reports
foreach ($report in $reportFiles) {
    Write-Host "    - $($report.Name) ($('{0:N0}' -f ($report.Length / 1024)) KB)" -ForegroundColor Gray
}

Write-Host "`n2. Testing 36 Functions Usage..." -ForegroundColor Cyan

# Test for 36 comprehensive functions usage indicators
$functions36Indicators = @(
    "comprehensive_details",
    "dynamic_impact", 
    "exploitation_steps",
    "dynamic_recommendations",
    "visual_changes",
    "screenshot_data",
    "security_metrics",
    "remediation_plan",
    "comprehensive_documentation",
    "technical_report",
    "executive_summary",
    "compliance_report",
    "forensic_analysis",
    "comprehensive_analysis",
    "security_impact_analysis",
    "realtime_assessment",
    "risk_analysis",
    "threat_modeling",
    "testing_details",
    "response_analysis",
    "exploitation_chain",
    "expert_analysis",
    "real_visual_changes",
    "real_persistent_results",
    "real_payload",
    "detailed_vulnerability_analysis",
    "real_world_examples",
    "impact_visualizations",
    "before_after_screenshots",
    "advanced_payload_analysis",
    "interactive_dialogue",
    "real_impact_changes",
    "final_comprehensive_report",
    "business_impact",
    "affected_components",
    "confidence_level_analysis"
)

$totalFunctionsFound = 0
foreach ($report in $reportFiles) {
    $functionsInReport = Test-ReportContent $report.FullName $functions36Indicators
    $totalFunctionsFound += $functionsInReport
    Write-Host "    $($report.Name): $functionsInReport/36 function indicators" -ForegroundColor $(if ($functionsInReport -ge 20) { "Green" } elseif ($functionsInReport -ge 10) { "Yellow" } else { "Red" })
}

$TestResults.Functions36Used = $totalFunctionsFound
Write-Host "  Total function indicators: $totalFunctionsFound" -ForegroundColor $(if ($totalFunctionsFound -ge 50) { "Green" } else { "Yellow" })

Write-Host "`n3. Testing System Files Usage..." -ForegroundColor Cyan

# Test for system files usage indicators
$filesUsageIndicators = @(
    "impact_visualizer.js",
    "textual_impact_analyzer.js",
    "visual_impact_data",
    "textual_impact_analysis",
    "business_impact_analysis",
    "exploitation_screenshots",
    "before_after_comparison",
    "real_time_visual_analysis",
    "comprehensive_textual_report",
    "detailed_textual_analysis"
)

$totalFilesUsed = 0
foreach ($report in $reportFiles) {
    $filesInReport = Test-ReportContent $report.FullName $filesUsageIndicators
    $totalFilesUsed += $filesInReport
    Write-Host "    $($report.Name): $filesInReport/10 file usage indicators" -ForegroundColor $(if ($filesInReport -ge 6) { "Green" } elseif ($filesInReport -ge 3) { "Yellow" } else { "Red" })
}

$TestResults.FilesUsed = $totalFilesUsed
Write-Host "  Total file usage indicators: $totalFilesUsed" -ForegroundColor $(if ($totalFilesUsed -ge 20) { "Green" } else { "Yellow" })

Write-Host "`n4. Testing Comprehensive Content..." -ForegroundColor Cyan

# Test for comprehensive content sections (English indicators)
$comprehensiveContentIndicators = @(
    "comprehensive-section",
    "content-section", 
    "vulnerability-header",
    "comprehensive detailed",
    "exploitation steps",
    "impact analysis",
    "interactive dialogue",
    "testing details",
    "visual changes",
    "screenshots",
    "security metrics",
    "remediation plan",
    "comprehensive documentation",
    "technical report",
    "executive summary",
    "compliance report",
    "forensic analysis",
    "comprehensive recommendations"
)

$totalComprehensiveContent = 0
foreach ($report in $reportFiles) {
    $comprehensiveInReport = Test-ReportContent $report.FullName $comprehensiveContentIndicators
    $totalComprehensiveContent += $comprehensiveInReport
    Write-Host "    $($report.Name): $comprehensiveInReport/18 comprehensive sections" -ForegroundColor $(if ($comprehensiveInReport -ge 12) { "Green" } elseif ($comprehensiveInReport -ge 6) { "Yellow" } else { "Red" })
}

$TestResults.ComprehensiveContent = $totalComprehensiveContent
Write-Host "  Total comprehensive content: $totalComprehensiveContent" -ForegroundColor $(if ($totalComprehensiveContent -ge 30) { "Green" } else { "Yellow" })

Write-Host "`n5. Testing Dynamic Content..." -ForegroundColor Cyan

# Test for dynamic/vulnerability-specific content
$dynamicContentIndicators = @(
    "SQL Injection",
    "XSS",
    "CSRF",
    "LFI",
    "RFI",
    "payload",
    "response",
    "evidence",
    "confidence_level",
    "parameter",
    "UNION SELECT",
    "<script>",
    "alert(",
    "../../../../",
    "include(",
    "http://",
    "testphp.vulnweb.com"
)

$totalDynamicContent = 0
foreach ($report in $reportFiles) {
    $dynamicInReport = Test-ReportContent $report.FullName $dynamicContentIndicators
    $totalDynamicContent += $dynamicInReport
    Write-Host "    $($report.Name): $dynamicInReport/17 dynamic content indicators" -ForegroundColor $(if ($dynamicInReport -ge 10) { "Green" } elseif ($dynamicInReport -ge 5) { "Yellow" } else { "Red" })
}

$TestResults.DynamicContent = $totalDynamicContent
Write-Host "  Total dynamic content: $totalDynamicContent" -ForegroundColor $(if ($totalDynamicContent -ge 25) { "Green" } else { "Yellow" })

Write-Host "`n6. Testing v4.0 System Indicators..." -ForegroundColor Cyan

# Test for v4.0 system specific indicators
$v4SystemIndicators = @(
    "v4.0",
    "Bug Bounty v4.0",
    "36 functions",
    "comprehensive functions",
    "automatically",
    "dynamically",
    "discovered vulnerabilities",
    "all files"
)

$totalV4Indicators = 0
foreach ($report in $reportFiles) {
    $v4InReport = Test-ReportContent $report.FullName $v4SystemIndicators
    $totalV4Indicators += $v4InReport
    Write-Host "    $($report.Name): $v4InReport/8 v4.0 system indicators" -ForegroundColor $(if ($v4InReport -ge 5) { "Green" } elseif ($v4InReport -ge 3) { "Yellow" } else { "Red" })
}

Write-Host "  Total v4.0 system indicators: $totalV4Indicators" -ForegroundColor $(if ($totalV4Indicators -ge 15) { "Green" } else { "Yellow" })

Write-Host "`n7. Calculating ACTUAL Usage Score..." -ForegroundColor Cyan

# Calculate actual usage score
$maxScore = 100
$actualScore = 0

# Reports found (10 points)
$actualScore += [math]::Min($TestResults.ReportsFound * 2, 10)

# Functions usage (35 points)
$actualScore += [math]::Min($TestResults.Functions36Used * 0.5, 35)

# Files usage (25 points) 
$actualScore += [math]::Min($TestResults.FilesUsed * 1.25, 25)

# Comprehensive content (20 points)
$actualScore += [math]::Min($TestResults.ComprehensiveContent * 0.67, 20)

# Dynamic content (10 points)
$actualScore += [math]::Min($TestResults.DynamicContent * 0.4, 10)

$TestResults.TotalScore = [math]::Round($actualScore)

Write-Host "`n=== ACTUAL USAGE RESULTS ===" -ForegroundColor Yellow
Write-Host "Reports Found: $($TestResults.ReportsFound)" -ForegroundColor White
Write-Host "36 Functions Usage: $($TestResults.Functions36Used) indicators" -ForegroundColor White
Write-Host "System Files Usage: $($TestResults.FilesUsed) indicators" -ForegroundColor White
Write-Host "Comprehensive Content: $($TestResults.ComprehensiveContent) indicators" -ForegroundColor White
Write-Host "Dynamic Content: $($TestResults.DynamicContent) indicators" -ForegroundColor White
Write-Host "v4.0 System Indicators: $totalV4Indicators" -ForegroundColor White

Write-Host "`nACTUAL USAGE SCORE: $($TestResults.TotalScore)/100" -ForegroundColor $(if ($TestResults.TotalScore -ge 80) { "Green" } elseif ($TestResults.TotalScore -ge 60) { "Yellow" } else { "Red" })

if ($TestResults.TotalScore -ge 80) {
    Write-Host "`n[EXCELLENT] v4.0 system IS ACTUALLY USING all 36 functions and files!" -ForegroundColor Green
    Write-Host "- Reports contain comprehensive detailed content" -ForegroundColor Green
    Write-Host "- Dynamic content based on discovered vulnerabilities" -ForegroundColor Green
    Write-Host "- All system files are integrated and used" -ForegroundColor Green
    Write-Host "- Reports are generated automatically and dynamically" -ForegroundColor Green
} elseif ($TestResults.TotalScore -ge 60) {
    Write-Host "`n[GOOD] v4.0 system is using most functions and files" -ForegroundColor Yellow
    Write-Host "- Some improvements needed for full utilization" -ForegroundColor Yellow
} else {
    Write-Host "`n[NEEDS IMPROVEMENT] v4.0 system not fully utilizing functions and files" -ForegroundColor Red
    Write-Host "- Functions and files exist but not being used effectively" -ForegroundColor Red
}

Write-Host "`n=== VERIFICATION COMPLETE ===" -ForegroundColor Green
