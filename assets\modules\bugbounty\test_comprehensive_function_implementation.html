<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تطبيق الدالة الشاملة التفصيلية v4.0</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-weight: bold;
        }
        .code-block {
            background: #f4f4f4;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار تطبيق الدالة الشاملة التفصيلية v4.0</h1>
            <p>التحقق من تطبيق generateComprehensiveDetailsFromRealData الأصلية الحقيقية</p>
        </div>

        <div class="test-section">
            <h2>📊 حالة الاختبار</h2>
            <div class="progress">
                <div class="progress-bar" id="progressBar"></div>
            </div>
            <p id="statusText">جاهز للبدء...</p>
        </div>

        <div class="test-section">
            <h2>🎯 الاختبارات</h2>
            <button onclick="runAllTests()">🚀 تشغيل جميع الاختبارات</button>
            <button onclick="testFunctionExists()">🔍 اختبار وجود الدالة</button>
            <button onclick="testFunctionExecution()">⚡ اختبار تنفيذ الدالة</button>
            <button onclick="testRealDataExtraction()">📊 اختبار استخراج البيانات الحقيقية</button>
        </div>

        <div class="test-section">
            <h2>📋 نتائج الاختبارات</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🔍 تفاصيل التنفيذ</h2>
            <div id="executionDetails" class="code-block"></div>
        </div>
    </div>

    <script src="BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = [];
        let currentTest = 0;
        let totalTests = 4;

        // تهيئة النظام
        async function initializeSystem() {
            try {
                bugBountyCore = new BugBountyCore();
                addResult('✅ تم تهيئة BugBountyCore بنجاح', 'success');
                return true;
            } catch (error) {
                addResult(`❌ فشل في تهيئة BugBountyCore: ${error.message}`, 'error');
                return false;
            }
        }

        // إضافة نتيجة اختبار
        function addResult(message, type = 'info') {
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            document.getElementById('testResults').appendChild(resultDiv);
            
            // تحديث تفاصيل التنفيذ
            const details = document.getElementById('executionDetails');
            details.textContent += `[${new Date().toLocaleTimeString()}] ${message}\n`;
            details.scrollTop = details.scrollHeight;
        }

        // تحديث شريط التقدم
        function updateProgress() {
            const progress = (currentTest / totalTests) * 100;
            document.getElementById('progressBar').style.width = progress + '%';
            document.getElementById('statusText').textContent = 
                `تم إكمال ${currentTest} من ${totalTests} اختبارات (${Math.round(progress)}%)`;
        }

        // إنشاء ثغرة اختبار
        function createTestVulnerability() {
            return {
                name: 'SQL Injection Test',
                type: 'SQL Injection',
                severity: 'High',
                url: 'http://testphp.vulnweb.com/artists.php?artist=1',
                location: 'artist parameter',
                payload: "1' OR '1'='1",
                response: 'MySQL error detected',
                evidence: 'Database error message revealed',
                exploitation_result: 'Successfully extracted database information'
            };
        }

        // اختبار وجود الدالة
        async function testFunctionExists() {
            addResult('🔍 بدء اختبار وجود الدالة...', 'info');
            
            if (!bugBountyCore) {
                const initialized = await initializeSystem();
                if (!initialized) return false;
            }

            try {
                if (typeof bugBountyCore.generateComprehensiveDetailsFromRealData === 'function') {
                    addResult('✅ الدالة generateComprehensiveDetailsFromRealData موجودة', 'success');
                    currentTest++;
                    updateProgress();
                    return true;
                } else {
                    addResult('❌ الدالة generateComprehensiveDetailsFromRealData غير موجودة', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار وجود الدالة: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار تنفيذ الدالة
        async function testFunctionExecution() {
            addResult('⚡ بدء اختبار تنفيذ الدالة...', 'info');
            
            try {
                const testVuln = createTestVulnerability();
                const realData = {
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence,
                    url: testVuln.url
                };

                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                if (result && typeof result === 'object') {
                    addResult('✅ تم تنفيذ الدالة بنجاح وإرجاع كائن صحيح', 'success');
                    addResult(`📊 الكائن المُرجع يحتوي على ${Object.keys(result).length} خاصية`, 'info');
                    
                    // التحقق من الخصائص المطلوبة
                    const requiredProperties = [
                        'technical_details', 'impact_analysis', 'exploitation_results',
                        'interactive_dialogue', 'evidence', 'visual_changes',
                        'persistent_results', 'recommendations', 'expert_analysis'
                    ];
                    
                    let foundProperties = 0;
                    requiredProperties.forEach(prop => {
                        if (result[prop]) {
                            foundProperties++;
                            addResult(`✅ الخاصية ${prop} موجودة`, 'success');
                        } else {
                            addResult(`⚠️ الخاصية ${prop} مفقودة`, 'warning');
                        }
                    });
                    
                    addResult(`📈 تم العثور على ${foundProperties}/${requiredProperties.length} خصائص مطلوبة`, 'info');
                    currentTest++;
                    updateProgress();
                    return true;
                } else {
                    addResult('❌ الدالة لم ترجع كائناً صحيحاً', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في تنفيذ الدالة: ${error.message}`, 'error');
                return false;
            }
        }

        // اختبار استخراج البيانات الحقيقية
        async function testRealDataExtraction() {
            addResult('📊 بدء اختبار استخراج البيانات الحقيقية...', 'info');
            
            try {
                const testVuln = createTestVulnerability();
                const realData = {
                    payload: testVuln.payload,
                    response: testVuln.response,
                    evidence: testVuln.evidence,
                    url: testVuln.url
                };

                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVuln, realData);
                
                if (result) {
                    // التحقق من أن البيانات الحقيقية مستخدمة
                    const technicalDetails = result.technical_details;
                    if (technicalDetails && technicalDetails.real_payload_used === testVuln.payload) {
                        addResult('✅ تم استخدام البيانات الحقيقية في التفاصيل التقنية', 'success');
                    } else {
                        addResult('⚠️ البيانات الحقيقية قد لا تكون مستخدمة بشكل صحيح', 'warning');
                    }
                    
                    // التحقق من وجود محتوى ديناميكي
                    const exploitationResults = result.exploitation_results;
                    if (exploitationResults && exploitationResults.detailed_steps) {
                        addResult('✅ تم إنشاء خطوات الاستغلال التفصيلية', 'success');
                    }
                    
                    // التحقق من عدم وجود محتوى افتراضي
                    const resultString = JSON.stringify(result);
                    if (!resultString.includes('payload متخصص') && 
                        !resultString.includes('[object Object]') &&
                        !resultString.includes('تحت التقييم')) {
                        addResult('✅ لا يوجد محتوى افتراضي أو عام', 'success');
                    } else {
                        addResult('⚠️ تم العثور على محتوى افتراضي أو عام', 'warning');
                    }
                    
                    currentTest++;
                    updateProgress();
                    return true;
                } else {
                    addResult('❌ لم يتم إرجاع نتائج من الدالة', 'error');
                    return false;
                }
            } catch (error) {
                addResult(`❌ خطأ في اختبار استخراج البيانات: ${error.message}`, 'error');
                return false;
            }
        }

        // تشغيل جميع الاختبارات
        async function runAllTests() {
            addResult('🚀 بدء تشغيل جميع الاختبارات...', 'info');
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('executionDetails').textContent = '';
            currentTest = 0;
            updateProgress();

            const initialized = await initializeSystem();
            if (!initialized) return;

            const tests = [
                testFunctionExists,
                testFunctionExecution,
                testRealDataExtraction
            ];

            let passedTests = 0;
            for (const test of tests) {
                const result = await test();
                if (result) passedTests++;
                await new Promise(resolve => setTimeout(resolve, 500)); // تأخير قصير
            }

            // النتيجة النهائية
            const successRate = (passedTests / tests.length) * 100;
            if (successRate === 100) {
                addResult(`🎉 نجحت جميع الاختبارات! (${passedTests}/${tests.length})`, 'success');
                addResult('✅ الدالة الشاملة التفصيلية تعمل بشكل صحيح', 'success');
            } else if (successRate >= 75) {
                addResult(`⚠️ نجح معظم الاختبارات (${passedTests}/${tests.length}) - ${successRate.toFixed(1)}%`, 'warning');
            } else {
                addResult(`❌ فشل في معظم الاختبارات (${passedTests}/${tests.length}) - ${successRate.toFixed(1)}%`, 'error');
            }

            currentTest = totalTests;
            updateProgress();
        }

        // تهيئة الصفحة
        window.onload = function() {
            addResult('📋 تم تحميل صفحة الاختبار', 'info');
            addResult('🎯 جاهز لاختبار الدالة الشاملة التفصيلية v4.0', 'info');
        };
    </script>
</body>
</html>
