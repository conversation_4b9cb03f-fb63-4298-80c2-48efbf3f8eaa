<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 اختبار الدالة الشاملة التفصيلية - تشخيص المشكلة</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .test-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #007bff;
        }
        .result {
            background: #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left-color: #28a745; background: #d4edda; }
        .error { border-left-color: #dc3545; background: #f8d7da; }
        .warning { border-left-color: #ffc107; background: #fff3cd; }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .btn:hover { background: #0056b3; }
        .progress {
            background: #e9ecef;
            border-radius: 10px;
            height: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
        .progress-bar {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 اختبار الدالة الشاملة التفصيلية v4.0</h1>
            <p>تشخيص مشكلة generateComprehensiveDetailsFromRealData</p>
        </div>
        
        <div class="content">
            <div class="test-section">
                <h3>📊 حالة الاختبار</h3>
                <div class="progress">
                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                </div>
                <p id="status">جاهز للبدء...</p>
            </div>

            <div class="test-section">
                <h3>🎯 الاختبارات</h3>
                <button class="btn" onclick="runDiagnosticTests()">🚀 تشغيل التشخيص الشامل</button>
                <button class="btn" onclick="testFunctionExistence()">🔍 اختبار وجود الدالة</button>
                <button class="btn" onclick="testFunctionExecution()">⚡ اختبار تنفيذ الدالة</button>
                <button class="btn" onclick="testRealDataExtraction()">📊 اختبار استخراج البيانات</button>
                <button class="btn" onclick="testHelperFunctions()">🔧 اختبار الدوال المساعدة</button>
            </div>

            <div class="test-section">
                <h3>📋 نتائج التشخيص</h3>
                <div id="results" class="result"></div>
            </div>

            <div class="test-section">
                <h3>🔍 تفاصيل التنفيذ</h3>
                <div id="executionDetails" class="result"></div>
            </div>
        </div>
    </div>

    <script src="assets/modules/bugbounty/BugBountyCore.js"></script>
    <script>
        let bugBountyCore;
        let testResults = [];
        let executionLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = `[${timestamp}] ${message}`;
            executionLog.push(logEntry);
            
            const detailsDiv = document.getElementById('executionDetails');
            detailsDiv.textContent = executionLog.join('\n');
            detailsDiv.scrollTop = detailsDiv.scrollHeight;
            
            console.log(logEntry);
        }

        function updateProgress(percentage) {
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        function displayResults() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.textContent = testResults.join('\n');
        }

        async function initializeBugBountyCore() {
            try {
                log('✅ تم تهيئة BugBountyCore بنجاح');
                bugBountyCore = new BugBountyCore();
                return true;
            } catch (error) {
                log(`❌ فشل في تهيئة BugBountyCore: ${error.message}`, 'error');
                return false;
            }
        }

        async function testFunctionExistence() {
            log('🔍 بدء اختبار وجود الدالة...');
            
            try {
                if (typeof bugBountyCore.generateComprehensiveDetailsFromRealData === 'function') {
                    log('✅ الدالة generateComprehensiveDetailsFromRealData موجودة');
                    testResults.push('✅ الدالة generateComprehensiveDetailsFromRealData موجودة');
                    return true;
                } else {
                    log('❌ الدالة generateComprehensiveDetailsFromRealData غير موجودة');
                    testResults.push('❌ الدالة generateComprehensiveDetailsFromRealData غير موجودة');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار وجود الدالة: ${error.message}`, 'error');
                testResults.push(`❌ خطأ في اختبار وجود الدالة: ${error.message}`);
                return false;
            }
        }

        async function testFunctionExecution() {
            log('⚡ بدء اختبار تنفيذ الدالة...');

            try {
                // إنشاء بيانات اختبار بسيطة
                const testVulnerability = {
                    name: 'XSS Test Vulnerability',
                    type: 'XSS',
                    url: 'http://example.com/test',
                    payload: '<script>alert("test")</script>',
                    severity: 'High'
                };

                const testRealData = {
                    payload: '<script>alert("test")</script>',
                    response: 'Script executed successfully',
                    evidence: 'Alert dialog appeared',
                    url: 'http://example.com/test'
                };

                log('📝 استدعاء الدالة مع بيانات الاختبار...');
                const result = await bugBountyCore.generateComprehensiveDetailsFromRealData(testVulnerability, testRealData);

                log(`📊 نتيجة الدالة: ${typeof result}`);

                if (result && typeof result === 'object') {
                    log('✅ الدالة أرجعت كائناً صحيحاً');
                    testResults.push('✅ الدالة أرجعت كائناً صحيحاً');

                    // فحص محتوى الكائن
                    const keys = Object.keys(result);
                    log(`🔑 مفاتيح الكائن (${keys.length}): ${keys.join(', ')}`);

                    // فحص المحتوى بالتفصيل
                    if (result.technical_details) {
                        log('✅ technical_details موجود');
                        log(`📝 comprehensive_description: ${result.technical_details.comprehensive_description?.substring(0, 100)}...`);
                    }

                    if (result.impact_analysis) {
                        log('✅ impact_analysis موجود');
                        log(`📊 detailed_impact: ${typeof result.impact_analysis.detailed_impact}`);
                    }

                    if (result.exploitation_results) {
                        log('✅ exploitation_results موجود');
                        log(`🎯 detailed_steps: ${typeof result.exploitation_results.detailed_steps}`);
                    }

                    if (keys.length > 0) {
                        log('✅ الكائن يحتوي على بيانات شاملة');
                        testResults.push('✅ الكائن يحتوي على بيانات شاملة');

                        // فحص إضافي للتأكد من عدم وجود محتوى placeholder
                        const jsonStr = JSON.stringify(result);
                        if (jsonStr.includes('[object Object]') || jsonStr.includes('payload_example')) {
                            log('⚠️ تم اكتشاف محتوى placeholder في النتائج');
                            testResults.push('⚠️ تم اكتشاف محتوى placeholder في النتائج');
                            return false;
                        } else {
                            log('✅ لا يوجد محتوى placeholder - البيانات حقيقية');
                            testResults.push('✅ لا يوجد محتوى placeholder - البيانات حقيقية');
                            return true;
                        }
                    } else {
                        log('❌ الكائن فارغ');
                        testResults.push('❌ الكائن فارغ');
                        return false;
                    }
                } else if (result === null) {
                    log('❌ الدالة أرجعت null - حدث خطأ في التنفيذ');
                    testResults.push('❌ الدالة أرجعت null - حدث خطأ في التنفيذ');
                    return false;
                } else {
                    log('❌ الدالة لم ترجع كائناً صحيحاً');
                    testResults.push('❌ الدالة لم ترجع كائناً صحيحاً');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في تنفيذ الدالة: ${error.message}`, 'error');
                log(`❌ تفاصيل الخطأ: ${error.stack}`, 'error');
                testResults.push(`❌ خطأ في تنفيذ الدالة: ${error.message}`);
                return false;
            }
        }

        async function testRealDataExtraction() {
            log('📊 بدء اختبار استخراج البيانات الحقيقية...');

            try {
                // اختبار دالة استخراج البيانات الحقيقية
                const testVuln = {
                    name: 'SQL Injection Test',
                    type: 'SQL Injection',
                    url: 'http://example.com/login',
                    parameter: 'username',
                    payload: "' OR '1'='1' --",
                    evidence: 'Database error revealed'
                };

                log('🔍 اختبار دالة extractRealDataFromDiscoveredVulnerability...');
                const extractedData = bugBountyCore.extractRealDataFromDiscoveredVulnerability(testVuln);

                log(`📊 البيانات المستخرجة: ${JSON.stringify(extractedData, null, 2)}`);

                if (extractedData && typeof extractedData === 'object') {
                    log('✅ تم استخراج البيانات بنجاح');
                    testResults.push('✅ تم استخراج البيانات بنجاح');
                    return true;
                } else {
                    log('❌ لم يتم إرجاع نتائج من الدالة');
                    testResults.push('❌ لم يتم إرجاع نتائج من الدالة');
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في استخراج البيانات: ${error.message}`, 'error');
                testResults.push(`❌ خطأ في استخراج البيانات: ${error.message}`);
                return false;
            }
        }

        async function testHelperFunctions() {
            log('🔧 بدء اختبار الدوال المساعدة...');

            try {
                const testVuln = {
                    name: 'XSS Test',
                    type: 'XSS',
                    url: 'http://example.com/test',
                    payload: '<script>alert("test")</script>',
                    evidence: 'Script executed'
                };

                const testRealData = {
                    payload: '<script>alert("test")</script>',
                    response: 'Script executed successfully',
                    evidence: 'Alert dialog appeared'
                };

                let helperTestsPassed = 0;
                let totalHelperTests = 0;

                // اختبار extractRealEvidenceFromTesting
                totalHelperTests++;
                log('🔍 اختبار extractRealEvidenceFromTesting...');
                try {
                    const evidence = bugBountyCore.extractRealEvidenceFromTesting(testVuln, testRealData);
                    if (evidence && typeof evidence === 'string' && evidence.length > 0) {
                        log('✅ extractRealEvidenceFromTesting يعمل بشكل صحيح');
                        helperTestsPassed++;
                    } else {
                        log('❌ extractRealEvidenceFromTesting لا يعمل بشكل صحيح');
                    }
                } catch (error) {
                    log(`❌ خطأ في extractRealEvidenceFromTesting: ${error.message}`);
                }

                // اختبار generateDynamicImpactForAnyVulnerability
                totalHelperTests++;
                log('🔍 اختبار generateDynamicImpactForAnyVulnerability...');
                try {
                    const impact = bugBountyCore.generateDynamicImpactForAnyVulnerability(testVuln, testRealData);
                    if (impact && typeof impact === 'string' && impact.length > 0) {
                        log('✅ generateDynamicImpactForAnyVulnerability يعمل بشكل صحيح');
                        helperTestsPassed++;
                    } else {
                        log('❌ generateDynamicImpactForAnyVulnerability لا يعمل بشكل صحيح');
                    }
                } catch (error) {
                    log(`❌ خطأ في generateDynamicImpactForAnyVulnerability: ${error.message}`);
                }

                // اختبار generateRealExploitationStepsForVulnerabilityComprehensive
                totalHelperTests++;
                log('🔍 اختبار generateRealExploitationStepsForVulnerabilityComprehensive...');
                try {
                    const steps = bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, testRealData);
                    if (steps && Array.isArray(steps) && steps.length > 0) {
                        log('✅ generateRealExploitationStepsForVulnerabilityComprehensive يعمل بشكل صحيح');
                        helperTestsPassed++;
                    } else {
                        log('❌ generateRealExploitationStepsForVulnerabilityComprehensive لا يعمل بشكل صحيح');
                    }
                } catch (error) {
                    log(`❌ خطأ في generateRealExploitationStepsForVulnerabilityComprehensive: ${error.message}`);
                }

                const helperSuccessRate = (helperTestsPassed / totalHelperTests * 100).toFixed(1);
                log(`📊 نتائج اختبار الدوال المساعدة: ${helperTestsPassed}/${totalHelperTests} (${helperSuccessRate}%)`);

                if (helperTestsPassed === totalHelperTests) {
                    log('✅ جميع الدوال المساعدة تعمل بشكل صحيح');
                    testResults.push('✅ جميع الدوال المساعدة تعمل بشكل صحيح');
                    return true;
                } else {
                    log(`⚠️ بعض الدوال المساعدة لا تعمل بشكل صحيح (${helperTestsPassed}/${totalHelperTests})`);
                    testResults.push(`⚠️ بعض الدوال المساعدة لا تعمل بشكل صحيح (${helperTestsPassed}/${totalHelperTests})`);
                    return false;
                }
            } catch (error) {
                log(`❌ خطأ في اختبار الدوال المساعدة: ${error.message}`, 'error');
                testResults.push(`❌ خطأ في اختبار الدوال المساعدة: ${error.message}`);
                return false;
            }
        }

        async function runDiagnosticTests() {
            testResults = [];
            executionLog = [];
            updateStatus('جاري تشغيل التشخيص...');
            updateProgress(0);

            log('🚀 بدء التشخيص الشامل للدالة الشاملة التفصيلية');

            // تهيئة النظام
            updateProgress(10);
            const initSuccess = await initializeBugBountyCore();
            if (!initSuccess) {
                updateStatus('❌ فشل في التهيئة');
                displayResults();
                return;
            }

            // اختبار وجود الدالة
            updateProgress(30);
            const existenceTest = await testFunctionExistence();
            
            // اختبار تنفيذ الدالة
            updateProgress(60);
            const executionTest = await testFunctionExecution();
            
            // اختبار استخراج البيانات
            updateProgress(75);
            const extractionTest = await testRealDataExtraction();

            // اختبار الدوال المساعدة
            updateProgress(90);
            const helperTest = await testHelperFunctions();

            updateProgress(100);

            // حساب النتائج النهائية
            const totalTests = 4;
            const passedTests = [existenceTest, executionTest, extractionTest, helperTest].filter(Boolean).length;
            const successRate = (passedTests / totalTests * 100).toFixed(1);

            if (passedTests === totalTests) {
                updateStatus(`✅ نجح في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`✅ نجح في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
            } else if (passedTests > 0) {
                updateStatus(`⚠️ نجح في بعض الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`⚠️ نجح في بعض الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
            } else {
                updateStatus(`❌ فشل في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
                testResults.push(`❌ فشل في جميع الاختبارات (${passedTests}/${totalTests}) - ${successRate}%`);
            }

            displayResults();
            log('🏁 انتهى التشخيص الشامل');
        }

        // تشغيل تلقائي عند تحميل الصفحة
        window.addEventListener('load', () => {
            setTimeout(runDiagnosticTests, 1000);
        });
    </script>
</body>
</html>
